[{"/Users/<USER>/code/qr-code-donkey/app/about/page.tsx": "1", "/Users/<USER>/code/qr-code-donkey/app/layout.tsx": "2", "/Users/<USER>/code/qr-code-donkey/app/page.tsx": "3", "/Users/<USER>/code/qr-code-donkey/src/AboutQRCodePage.tsx": "4", "/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx": "5", "/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx": "6", "/Users/<USER>/code/qr-code-donkey/src/components/QRCodeDisplay.tsx": "7", "/Users/<USER>/code/qr-code-donkey/src/components/QRCodeForm.tsx": "8", "/Users/<USER>/code/qr-code-donkey/src/components/QRCodeHistory.tsx": "9"}, {"size": 128, "mtime": 1752933507974, "results": "10", "hashOfConfig": "11"}, {"size": 1711, "mtime": 1752933478298, "results": "12", "hashOfConfig": "11"}, {"size": 151, "mtime": 1752933484619, "results": "13", "hashOfConfig": "11"}, {"size": 3069, "mtime": 1752933558022, "results": "14", "hashOfConfig": "11"}, {"size": 1395, "mtime": 1752933642533, "results": "15", "hashOfConfig": "11"}, {"size": 6937, "mtime": 1752933917344, "results": "16", "hashOfConfig": "11"}, {"size": 1964, "mtime": 1752933971896, "results": "17", "hashOfConfig": "11"}, {"size": 2710, "mtime": 1752933596054, "results": "18", "hashOfConfig": "11"}, {"size": 2243, "mtime": 1752933627394, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bsk6nk", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/code/qr-code-donkey/app/about/page.tsx", [], [], "/Users/<USER>/code/qr-code-donkey/app/layout.tsx", ["47"], [], "/Users/<USER>/code/qr-code-donkey/app/page.tsx", [], [], "/Users/<USER>/code/qr-code-donkey/src/AboutQRCodePage.tsx", [], [], "/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx", [], [], "/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx", ["48"], [], "/Users/<USER>/code/qr-code-donkey/src/components/QRCodeDisplay.tsx", ["49"], [], "/Users/<USER>/code/qr-code-donkey/src/components/QRCodeForm.tsx", [], [], "/Users/<USER>/code/qr-code-donkey/src/components/QRCodeHistory.tsx", [], [], {"ruleId": "50", "severity": 1, "message": "51", "line": 16, "column": 14, "nodeType": "52", "messageId": "53", "endLine": 16, "endColumn": 22}, {"ruleId": "54", "severity": 1, "message": "55", "line": 94, "column": 6, "nodeType": "56", "endLine": 94, "endColumn": 8, "suggestions": "57"}, {"ruleId": "54", "severity": 1, "message": "58", "line": 54, "column": 6, "nodeType": "56", "endLine": 54, "endColumn": 8, "suggestions": "59"}, "react-refresh/only-export-components", "Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components.", "Identifier", "namedExport", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'backgroundColor', 'dotColor', 'dotType', and 'url'. Either include them or remove the dependency array.", "ArrayExpression", ["60"], "React Hook useEffect has missing dependencies: 'backgroundColor', 'data', 'dotColor', 'dotType', and 'size'. Either include them or remove the dependency array.", ["61"], {"desc": "62", "fix": "63"}, {"desc": "64", "fix": "65"}, "Update the dependencies array to be: [backgroundColor, dotColor, dotType, url]", {"range": "66", "text": "67"}, "Update the dependencies array to be: [backgroundColor, data, dotColor, dotType, size]", {"range": "68", "text": "69"}, [3131, 3133], "[backgroundColor, dotColor, dotType, url]", [1224, 1226], "[backgroundColor, data, dotColor, dotType, size]"]