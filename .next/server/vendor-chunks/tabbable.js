"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tabbable";
exports.ids = ["vendor-chunks/tabbable"];
exports.modules = {

/***/ "(ssr)/./node_modules/tabbable/dist/index.esm.js":
/*!*************************************************!*\
  !*** ./node_modules/tabbable/dist/index.esm.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusable: () => (/* binding */ focusable),\n/* harmony export */   getTabIndex: () => (/* binding */ getTabIndex),\n/* harmony export */   isFocusable: () => (/* binding */ isFocusable),\n/* harmony export */   isTabbable: () => (/* binding */ isTabbable),\n/* harmony export */   tabbable: () => (/* binding */ tabbable)\n/* harmony export */ });\n/*!\n* tabbable 6.2.0\n* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE\n*/\n// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nvar candidateSelectors = ['input:not([inert])', 'select:not([inert])', 'textarea:not([inert])', 'a[href]:not([inert])', 'button:not([inert])', '[tabindex]:not(slot):not([inert])', 'audio[controls]:not([inert])', 'video[controls]:not([inert])', '[contenteditable]:not([contenteditable=\"false\"]):not([inert])', 'details>summary:first-of-type:not([inert])', 'details:not([inert])'];\nvar candidateSelector = /* #__PURE__ */candidateSelectors.join(',');\nvar NoElement = typeof Element === 'undefined';\nvar matches = NoElement ? function () {} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\nvar getRootNode = !NoElement && Element.prototype.getRootNode ? function (element) {\n  var _element$getRootNode;\n  return element === null || element === void 0 ? void 0 : (_element$getRootNode = element.getRootNode) === null || _element$getRootNode === void 0 ? void 0 : _element$getRootNode.call(element);\n} : function (element) {\n  return element === null || element === void 0 ? void 0 : element.ownerDocument;\n};\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nvar isInert = function isInert(node, lookUp) {\n  var _node$getAttribute;\n  if (lookUp === void 0) {\n    lookUp = true;\n  }\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  var inertAtt = node === null || node === void 0 ? void 0 : (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'inert');\n  var inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  var result = inert || lookUp && node && isInert(node.parentNode); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nvar isContentEditable = function isContentEditable(node) {\n  var _node$getAttribute2;\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  var attValue = node === null || node === void 0 ? void 0 : (_node$getAttribute2 = node.getAttribute) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.call(node, 'contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nvar getCandidates = function getCandidates(el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nvar getCandidatesIteratively = function getCandidatesIteratively(elements, includeContainer, options) {\n  var candidates = [];\n  var elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    var element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      var assigned = element.assignedElements();\n      var content = assigned.length ? assigned : element.children;\n      var nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push.apply(candidates, nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates\n        });\n      }\n    } else {\n      // check candidate element\n      var validCandidate = matches.call(element, candidateSelector);\n      if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      var shadowRoot = element.shadowRoot ||\n      // check for an undisclosed shadow\n      typeof options.getShadowRoot === 'function' && options.getShadowRoot(element);\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      var validShadowRoot = !isInert(shadowRoot, false) && (!options.shadowRootFilter || options.shadowRootFilter(element));\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        var _nestedCandidates = getCandidatesIteratively(shadowRoot === true ? element.children : shadowRoot.children, true, options);\n        if (options.flatten) {\n          candidates.push.apply(candidates, _nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: _nestedCandidates\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift.apply(elementsToCheck, element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nvar hasTabIndex = function hasTabIndex(node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nvar getTabIndex = function getTabIndex(node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if ((/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || isContentEditable(node)) && !hasTabIndex(node)) {\n      return 0;\n    }\n  }\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nvar getSortOrderTabIndex = function getSortOrderTabIndex(node, isScope) {\n  var tabIndex = getTabIndex(node);\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n  return tabIndex;\n};\nvar sortOrderedTabbables = function sortOrderedTabbables(a, b) {\n  return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;\n};\nvar isInput = function isInput(node) {\n  return node.tagName === 'INPUT';\n};\nvar isHiddenInput = function isHiddenInput(node) {\n  return isInput(node) && node.type === 'hidden';\n};\nvar isDetailsWithSummary = function isDetailsWithSummary(node) {\n  var r = node.tagName === 'DETAILS' && Array.prototype.slice.apply(node.children).some(function (child) {\n    return child.tagName === 'SUMMARY';\n  });\n  return r;\n};\nvar getCheckedRadio = function getCheckedRadio(nodes, form) {\n  for (var i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\nvar isTabbableRadio = function isTabbableRadio(node) {\n  if (!node.name) {\n    return true;\n  }\n  var radioScope = node.form || getRootNode(node);\n  var queryRadios = function queryRadios(name) {\n    return radioScope.querySelectorAll('input[type=\"radio\"][name=\"' + name + '\"]');\n  };\n  var radioSet;\n  if (typeof window !== 'undefined' && typeof window.CSS !== 'undefined' && typeof window.CSS.escape === 'function') {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error('Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s', err.message);\n      return false;\n    }\n  }\n  var checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\nvar isRadio = function isRadio(node) {\n  return isInput(node) && node.type === 'radio';\n};\nvar isNonTabbableRadio = function isNonTabbableRadio(node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nvar isNodeAttached = function isNodeAttached(node) {\n  var _nodeRoot;\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  var nodeRoot = node && getRootNode(node);\n  var nodeRootHost = (_nodeRoot = nodeRoot) === null || _nodeRoot === void 0 ? void 0 : _nodeRoot.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  var attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    var _nodeRootHost, _nodeRootHost$ownerDo, _node$ownerDocument;\n    attached = !!((_nodeRootHost = nodeRootHost) !== null && _nodeRootHost !== void 0 && (_nodeRootHost$ownerDo = _nodeRootHost.ownerDocument) !== null && _nodeRootHost$ownerDo !== void 0 && _nodeRootHost$ownerDo.contains(nodeRootHost) || node !== null && node !== void 0 && (_node$ownerDocument = node.ownerDocument) !== null && _node$ownerDocument !== void 0 && _node$ownerDocument.contains(node));\n    while (!attached && nodeRootHost) {\n      var _nodeRoot2, _nodeRootHost2, _nodeRootHost2$ownerD;\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = (_nodeRoot2 = nodeRoot) === null || _nodeRoot2 === void 0 ? void 0 : _nodeRoot2.host;\n      attached = !!((_nodeRootHost2 = nodeRootHost) !== null && _nodeRootHost2 !== void 0 && (_nodeRootHost2$ownerD = _nodeRootHost2.ownerDocument) !== null && _nodeRootHost2$ownerD !== void 0 && _nodeRootHost2$ownerD.contains(nodeRootHost));\n    }\n  }\n  return attached;\n};\nvar isZeroArea = function isZeroArea(node) {\n  var _node$getBoundingClie = node.getBoundingClientRect(),\n    width = _node$getBoundingClie.width,\n    height = _node$getBoundingClie.height;\n  return width === 0 && height === 0;\n};\nvar isHidden = function isHidden(node, _ref) {\n  var displayCheck = _ref.displayCheck,\n    getShadowRoot = _ref.getShadowRoot;\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n  var isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n  if (!displayCheck || displayCheck === 'full' || displayCheck === 'legacy-full') {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      var originalNode = node;\n      while (node) {\n        var parentElement = node.parentElement;\n        var rootNode = getRootNode(node);\n        if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nvar isDisabledFromFieldset = function isDisabledFromFieldset(node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    var parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (var i = 0; i < parentNode.children.length; i++) {\n          var child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *') ? true : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\nvar isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable(options, node) {\n  if (node.disabled ||\n  // we must do an inert look up to filter out any elements inside an inert ancestor\n  //  because we're limited in the type of selectors we can use in JSDom (see related\n  //  note related to `candidateSelectors`)\n  isInert(node) || isHiddenInput(node) || isHidden(node, options) ||\n  // For a details element with a summary, the summary element gets the focus\n  isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {\n    return false;\n  }\n  return true;\n};\nvar isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable(options, node) {\n  if (isNonTabbableRadio(node) || getTabIndex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {\n    return false;\n  }\n  return true;\n};\nvar isValidShadowRootTabbable = function isValidShadowRootTabbable(shadowHostNode) {\n  var tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nvar sortByOrder = function sortByOrder(candidates) {\n  var regularTabbables = [];\n  var orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    var isScope = !!item.scopeParent;\n    var element = isScope ? item.scopeParent : item;\n    var candidateTabindex = getSortOrderTabIndex(element, isScope);\n    var elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements\n      });\n    }\n  });\n  return orderedTabbables.sort(sortOrderedTabbables).reduce(function (acc, sortable) {\n    sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);\n    return acc;\n  }, []).concat(regularTabbables);\n};\nvar tabbable = function tabbable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorTabbable.bind(null, options),\n      flatten: false,\n      getShadowRoot: options.getShadowRoot,\n      shadowRootFilter: isValidShadowRootTabbable\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));\n  }\n  return sortByOrder(candidates);\n};\nvar focusable = function focusable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorFocusable.bind(null, options),\n      flatten: true,\n      getShadowRoot: options.getShadowRoot\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorFocusable.bind(null, options));\n  }\n  return candidates;\n};\nvar isTabbable = function isTabbable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\nvar focusableCandidateSelector = /* #__PURE__ */candidateSelectors.concat('iframe').join(',');\nvar isFocusable = function isFocusable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tabbable/dist/index.esm.js\n");

/***/ })

};
;