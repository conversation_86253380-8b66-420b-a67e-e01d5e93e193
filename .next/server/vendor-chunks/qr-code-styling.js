/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/qr-code-styling";
exports.ids = ["vendor-chunks/qr-code-styling"];
exports.modules = {

/***/ "(ssr)/./node_modules/qr-code-styling/lib/qr-code-styling.js":
/*!*************************************************************!*\
  !*** ./node_modules/qr-code-styling/lib/qr-code-styling.js ***!
  \*************************************************************/
/***/ (function(module) {

eval("!function(t,e){ true?module.exports=e():0}(this,(()=>(()=>{var t={873:(t,e)=>{var i,r,n=function(){var t=function(t,e){var i=t,r=s[e],n=null,o=0,h=null,p=[],v={},m=function(t,e){n=function(t){for(var e=new Array(t),i=0;i<t;i+=1){e[i]=new Array(t);for(var r=0;r<t;r+=1)e[i][r]=null}return e}(o=4*i+17),b(0,0),b(o-7,0),b(0,o-7),x(),y(),C(t,e),i>=7&&S(t),null==h&&(h=M(i,r,p)),A(h,e)},b=function(t,e){for(var i=-1;i<=7;i+=1)if(!(t+i<=-1||o<=t+i))for(var r=-1;r<=7;r+=1)e+r<=-1||o<=e+r||(n[t+i][e+r]=0<=i&&i<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==i||6==i)||2<=i&&i<=4&&2<=r&&r<=4)},y=function(){for(var t=8;t<o-8;t+=1)null==n[t][6]&&(n[t][6]=t%2==0);for(var e=8;e<o-8;e+=1)null==n[6][e]&&(n[6][e]=e%2==0)},x=function(){for(var t=a.getPatternPosition(i),e=0;e<t.length;e+=1)for(var r=0;r<t.length;r+=1){var o=t[e],s=t[r];if(null==n[o][s])for(var h=-2;h<=2;h+=1)for(var d=-2;d<=2;d+=1)n[o+h][s+d]=-2==h||2==h||-2==d||2==d||0==h&&0==d}},S=function(t){for(var e=a.getBCHTypeNumber(i),r=0;r<18;r+=1){var s=!t&&1==(e>>r&1);n[Math.floor(r/3)][r%3+o-8-3]=s}for(r=0;r<18;r+=1)s=!t&&1==(e>>r&1),n[r%3+o-8-3][Math.floor(r/3)]=s},C=function(t,e){for(var i=r<<3|e,s=a.getBCHTypeInfo(i),h=0;h<15;h+=1){var d=!t&&1==(s>>h&1);h<6?n[h][8]=d:h<8?n[h+1][8]=d:n[o-15+h][8]=d}for(h=0;h<15;h+=1)d=!t&&1==(s>>h&1),h<8?n[8][o-h-1]=d:h<9?n[8][15-h-1+1]=d:n[8][15-h-1]=d;n[o-8][8]=!t},A=function(t,e){for(var i=-1,r=o-1,s=7,h=0,d=a.getMaskFunction(e),u=o-1;u>0;u-=2)for(6==u&&(u-=1);;){for(var c=0;c<2;c+=1)if(null==n[r][u-c]){var l=!1;h<t.length&&(l=1==(t[h]>>>s&1)),d(r,u-c)&&(l=!l),n[r][u-c]=l,-1==(s-=1)&&(h+=1,s=7)}if((r+=i)<0||o<=r){r-=i,i=-i;break}}},M=function(t,e,i){for(var r=u.getRSBlocks(t,e),n=c(),o=0;o<i.length;o+=1){var s=i[o];n.put(s.getMode(),4),n.put(s.getLength(),a.getLengthInBits(s.getMode(),t)),s.write(n)}var h=0;for(o=0;o<r.length;o+=1)h+=r[o].dataCount;if(n.getLengthInBits()>8*h)throw\"code length overflow. (\"+n.getLengthInBits()+\">\"+8*h+\")\";for(n.getLengthInBits()+4<=8*h&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(!1);for(;!(n.getLengthInBits()>=8*h||(n.put(236,8),n.getLengthInBits()>=8*h));)n.put(17,8);return function(t,e){for(var i=0,r=0,n=0,o=new Array(e.length),s=new Array(e.length),h=0;h<e.length;h+=1){var u=e[h].dataCount,c=e[h].totalCount-u;r=Math.max(r,u),n=Math.max(n,c),o[h]=new Array(u);for(var l=0;l<o[h].length;l+=1)o[h][l]=255&t.getBuffer()[l+i];i+=u;var g=a.getErrorCorrectPolynomial(c),f=d(o[h],g.getLength()-1).mod(g);for(s[h]=new Array(g.getLength()-1),l=0;l<s[h].length;l+=1){var w=l+f.getLength()-s[h].length;s[h][l]=w>=0?f.getAt(w):0}}var p=0;for(l=0;l<e.length;l+=1)p+=e[l].totalCount;var v=new Array(p),_=0;for(l=0;l<r;l+=1)for(h=0;h<e.length;h+=1)l<o[h].length&&(v[_]=o[h][l],_+=1);for(l=0;l<n;l+=1)for(h=0;h<e.length;h+=1)l<s[h].length&&(v[_]=s[h][l],_+=1);return v}(n,r)};v.addData=function(t,e){var i=null;switch(e=e||\"Byte\"){case\"Numeric\":i=l(t);break;case\"Alphanumeric\":i=g(t);break;case\"Byte\":i=f(t);break;case\"Kanji\":i=w(t);break;default:throw\"mode:\"+e}p.push(i),h=null},v.isDark=function(t,e){if(t<0||o<=t||e<0||o<=e)throw t+\",\"+e;return n[t][e]},v.getModuleCount=function(){return o},v.make=function(){if(i<1){for(var t=1;t<40;t++){for(var e=u.getRSBlocks(t,r),n=c(),o=0;o<p.length;o++){var s=p[o];n.put(s.getMode(),4),n.put(s.getLength(),a.getLengthInBits(s.getMode(),t)),s.write(n)}var h=0;for(o=0;o<e.length;o++)h+=e[o].dataCount;if(n.getLengthInBits()<=8*h)break}i=t}m(!1,function(){for(var t=0,e=0,i=0;i<8;i+=1){m(!0,i);var r=a.getLostPoint(v);(0==i||t>r)&&(t=r,e=i)}return e}())},v.createTableTag=function(t,e){t=t||2;var i=\"\";i+='<table style=\"',i+=\" border-width: 0px; border-style: none;\",i+=\" border-collapse: collapse;\",i+=\" padding: 0px; margin: \"+(e=void 0===e?4*t:e)+\"px;\",i+='\">',i+=\"<tbody>\";for(var r=0;r<v.getModuleCount();r+=1){i+=\"<tr>\";for(var n=0;n<v.getModuleCount();n+=1)i+='<td style=\"',i+=\" border-width: 0px; border-style: none;\",i+=\" border-collapse: collapse;\",i+=\" padding: 0px; margin: 0px;\",i+=\" width: \"+t+\"px;\",i+=\" height: \"+t+\"px;\",i+=\" background-color: \",i+=v.isDark(r,n)?\"#000000\":\"#ffffff\",i+=\";\",i+='\"/>';i+=\"</tr>\"}return(i+=\"</tbody>\")+\"</table>\"},v.createSvgTag=function(t,e,i,r){var n={};\"object\"==typeof arguments[0]&&(t=(n=arguments[0]).cellSize,e=n.margin,i=n.alt,r=n.title),t=t||2,e=void 0===e?4*t:e,(i=\"string\"==typeof i?{text:i}:i||{}).text=i.text||null,i.id=i.text?i.id||\"qrcode-description\":null,(r=\"string\"==typeof r?{text:r}:r||{}).text=r.text||null,r.id=r.text?r.id||\"qrcode-title\":null;var o,s,a,h,d=v.getModuleCount()*t+2*e,u=\"\";for(h=\"l\"+t+\",0 0,\"+t+\" -\"+t+\",0 0,-\"+t+\"z \",u+='<svg version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\"',u+=n.scalable?\"\":' width=\"'+d+'px\" height=\"'+d+'px\"',u+=' viewBox=\"0 0 '+d+\" \"+d+'\" ',u+=' preserveAspectRatio=\"xMinYMin meet\"',u+=r.text||i.text?' role=\"img\" aria-labelledby=\"'+$([r.id,i.id].join(\" \").trim())+'\"':\"\",u+=\">\",u+=r.text?'<title id=\"'+$(r.id)+'\">'+$(r.text)+\"</title>\":\"\",u+=i.text?'<description id=\"'+$(i.id)+'\">'+$(i.text)+\"</description>\":\"\",u+='<rect width=\"100%\" height=\"100%\" fill=\"white\" cx=\"0\" cy=\"0\"/>',u+='<path d=\"',s=0;s<v.getModuleCount();s+=1)for(a=s*t+e,o=0;o<v.getModuleCount();o+=1)v.isDark(s,o)&&(u+=\"M\"+(o*t+e)+\",\"+a+h);return(u+='\" stroke=\"transparent\" fill=\"black\"/>')+\"</svg>\"},v.createDataURL=function(t,e){t=t||2,e=void 0===e?4*t:e;var i=v.getModuleCount()*t+2*e,r=e,n=i-e;return _(i,i,(function(e,i){if(r<=e&&e<n&&r<=i&&i<n){var o=Math.floor((e-r)/t),s=Math.floor((i-r)/t);return v.isDark(s,o)?0:1}return 1}))},v.createImgTag=function(t,e,i){t=t||2,e=void 0===e?4*t:e;var r=v.getModuleCount()*t+2*e,n=\"\";return n+=\"<img\",n+=' src=\"',n+=v.createDataURL(t,e),n+='\"',n+=' width=\"',n+=r,n+='\"',n+=' height=\"',n+=r,n+='\"',i&&(n+=' alt=\"',n+=$(i),n+='\"'),n+\"/>\"};var $=function(t){for(var e=\"\",i=0;i<t.length;i+=1){var r=t.charAt(i);switch(r){case\"<\":e+=\"&lt;\";break;case\">\":e+=\"&gt;\";break;case\"&\":e+=\"&amp;\";break;case'\"':e+=\"&quot;\";break;default:e+=r}}return e};return v.createASCII=function(t,e){if((t=t||1)<2)return function(t){t=void 0===t?2:t;var e,i,r,n,o,s=1*v.getModuleCount()+2*t,a=t,h=s-t,d={\"██\":\"█\",\"█ \":\"▀\",\" █\":\"▄\",\"  \":\" \"},u={\"██\":\"▀\",\"█ \":\"▀\",\" █\":\" \",\"  \":\" \"},c=\"\";for(e=0;e<s;e+=2){for(r=Math.floor((e-a)/1),n=Math.floor((e+1-a)/1),i=0;i<s;i+=1)o=\"█\",a<=i&&i<h&&a<=e&&e<h&&v.isDark(r,Math.floor((i-a)/1))&&(o=\" \"),a<=i&&i<h&&a<=e+1&&e+1<h&&v.isDark(n,Math.floor((i-a)/1))?o+=\" \":o+=\"█\",c+=t<1&&e+1>=h?u[o]:d[o];c+=\"\\n\"}return s%2&&t>0?c.substring(0,c.length-s-1)+Array(s+1).join(\"▀\"):c.substring(0,c.length-1)}(e);t-=1,e=void 0===e?2*t:e;var i,r,n,o,s=v.getModuleCount()*t+2*e,a=e,h=s-e,d=Array(t+1).join(\"██\"),u=Array(t+1).join(\"  \"),c=\"\",l=\"\";for(i=0;i<s;i+=1){for(n=Math.floor((i-a)/t),l=\"\",r=0;r<s;r+=1)o=1,a<=r&&r<h&&a<=i&&i<h&&v.isDark(n,Math.floor((r-a)/t))&&(o=0),l+=o?d:u;for(n=0;n<t;n+=1)c+=l+\"\\n\"}return c.substring(0,c.length-1)},v.renderTo2dContext=function(t,e){e=e||2;for(var i=v.getModuleCount(),r=0;r<i;r++)for(var n=0;n<i;n++)t.fillStyle=v.isDark(r,n)?\"black\":\"white\",t.fillRect(r*e,n*e,e,e)},v};t.stringToBytes=(t.stringToBytesFuncs={default:function(t){for(var e=[],i=0;i<t.length;i+=1){var r=t.charCodeAt(i);e.push(255&r)}return e}}).default,t.createStringToBytes=function(t,e){var i=function(){for(var i=v(t),r=function(){var t=i.read();if(-1==t)throw\"eof\";return t},n=0,o={};;){var s=i.read();if(-1==s)break;var a=r(),h=r()<<8|r();o[String.fromCharCode(s<<8|a)]=h,n+=1}if(n!=e)throw n+\" != \"+e;return o}(),r=\"?\".charCodeAt(0);return function(t){for(var e=[],n=0;n<t.length;n+=1){var o=t.charCodeAt(n);if(o<128)e.push(o);else{var s=i[t.charAt(n)];\"number\"==typeof s?(255&s)==s?e.push(s):(e.push(s>>>8),e.push(255&s)):e.push(r)}}return e}};var e,i,r,n,o,s={L:1,M:0,Q:3,H:2},a=(e=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],i=1335,r=7973,o=function(t){for(var e=0;0!=t;)e+=1,t>>>=1;return e},(n={}).getBCHTypeInfo=function(t){for(var e=t<<10;o(e)-o(i)>=0;)e^=i<<o(e)-o(i);return 21522^(t<<10|e)},n.getBCHTypeNumber=function(t){for(var e=t<<12;o(e)-o(r)>=0;)e^=r<<o(e)-o(r);return t<<12|e},n.getPatternPosition=function(t){return e[t-1]},n.getMaskFunction=function(t){switch(t){case 0:return function(t,e){return(t+e)%2==0};case 1:return function(t,e){return t%2==0};case 2:return function(t,e){return e%3==0};case 3:return function(t,e){return(t+e)%3==0};case 4:return function(t,e){return(Math.floor(t/2)+Math.floor(e/3))%2==0};case 5:return function(t,e){return t*e%2+t*e%3==0};case 6:return function(t,e){return(t*e%2+t*e%3)%2==0};case 7:return function(t,e){return(t*e%3+(t+e)%2)%2==0};default:throw\"bad maskPattern:\"+t}},n.getErrorCorrectPolynomial=function(t){for(var e=d([1],0),i=0;i<t;i+=1)e=e.multiply(d([1,h.gexp(i)],0));return e},n.getLengthInBits=function(t,e){if(1<=e&&e<10)switch(t){case 1:return 10;case 2:return 9;case 4:case 8:return 8;default:throw\"mode:\"+t}else if(e<27)switch(t){case 1:return 12;case 2:return 11;case 4:return 16;case 8:return 10;default:throw\"mode:\"+t}else{if(!(e<41))throw\"type:\"+e;switch(t){case 1:return 14;case 2:return 13;case 4:return 16;case 8:return 12;default:throw\"mode:\"+t}}},n.getLostPoint=function(t){for(var e=t.getModuleCount(),i=0,r=0;r<e;r+=1)for(var n=0;n<e;n+=1){for(var o=0,s=t.isDark(r,n),a=-1;a<=1;a+=1)if(!(r+a<0||e<=r+a))for(var h=-1;h<=1;h+=1)n+h<0||e<=n+h||0==a&&0==h||s==t.isDark(r+a,n+h)&&(o+=1);o>5&&(i+=3+o-5)}for(r=0;r<e-1;r+=1)for(n=0;n<e-1;n+=1){var d=0;t.isDark(r,n)&&(d+=1),t.isDark(r+1,n)&&(d+=1),t.isDark(r,n+1)&&(d+=1),t.isDark(r+1,n+1)&&(d+=1),0!=d&&4!=d||(i+=3)}for(r=0;r<e;r+=1)for(n=0;n<e-6;n+=1)t.isDark(r,n)&&!t.isDark(r,n+1)&&t.isDark(r,n+2)&&t.isDark(r,n+3)&&t.isDark(r,n+4)&&!t.isDark(r,n+5)&&t.isDark(r,n+6)&&(i+=40);for(n=0;n<e;n+=1)for(r=0;r<e-6;r+=1)t.isDark(r,n)&&!t.isDark(r+1,n)&&t.isDark(r+2,n)&&t.isDark(r+3,n)&&t.isDark(r+4,n)&&!t.isDark(r+5,n)&&t.isDark(r+6,n)&&(i+=40);var u=0;for(n=0;n<e;n+=1)for(r=0;r<e;r+=1)t.isDark(r,n)&&(u+=1);return i+Math.abs(100*u/e/e-50)/5*10},n),h=function(){for(var t=new Array(256),e=new Array(256),i=0;i<8;i+=1)t[i]=1<<i;for(i=8;i<256;i+=1)t[i]=t[i-4]^t[i-5]^t[i-6]^t[i-8];for(i=0;i<255;i+=1)e[t[i]]=i;return{glog:function(t){if(t<1)throw\"glog(\"+t+\")\";return e[t]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return t[e]}}}();function d(t,e){if(void 0===t.length)throw t.length+\"/\"+e;var i=function(){for(var i=0;i<t.length&&0==t[i];)i+=1;for(var r=new Array(t.length-i+e),n=0;n<t.length-i;n+=1)r[n]=t[n+i];return r}(),r={getAt:function(t){return i[t]},getLength:function(){return i.length},multiply:function(t){for(var e=new Array(r.getLength()+t.getLength()-1),i=0;i<r.getLength();i+=1)for(var n=0;n<t.getLength();n+=1)e[i+n]^=h.gexp(h.glog(r.getAt(i))+h.glog(t.getAt(n)));return d(e,0)},mod:function(t){if(r.getLength()-t.getLength()<0)return r;for(var e=h.glog(r.getAt(0))-h.glog(t.getAt(0)),i=new Array(r.getLength()),n=0;n<r.getLength();n+=1)i[n]=r.getAt(n);for(n=0;n<t.getLength();n+=1)i[n]^=h.gexp(h.glog(t.getAt(n))+e);return d(i,0).mod(t)}};return r}var u=function(){var t=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],e=function(t,e){var i={};return i.totalCount=t,i.dataCount=e,i},i={getRSBlocks:function(i,r){var n=function(e,i){switch(i){case s.L:return t[4*(e-1)+0];case s.M:return t[4*(e-1)+1];case s.Q:return t[4*(e-1)+2];case s.H:return t[4*(e-1)+3];default:return}}(i,r);if(void 0===n)throw\"bad rs block @ typeNumber:\"+i+\"/errorCorrectionLevel:\"+r;for(var o=n.length/3,a=[],h=0;h<o;h+=1)for(var d=n[3*h+0],u=n[3*h+1],c=n[3*h+2],l=0;l<d;l+=1)a.push(e(u,c));return a}};return i}(),c=function(){var t=[],e=0,i={getBuffer:function(){return t},getAt:function(e){var i=Math.floor(e/8);return 1==(t[i]>>>7-e%8&1)},put:function(t,e){for(var r=0;r<e;r+=1)i.putBit(1==(t>>>e-r-1&1))},getLengthInBits:function(){return e},putBit:function(i){var r=Math.floor(e/8);t.length<=r&&t.push(0),i&&(t[r]|=128>>>e%8),e+=1}};return i},l=function(t){var e=t,i={getMode:function(){return 1},getLength:function(t){return e.length},write:function(t){for(var i=e,n=0;n+2<i.length;)t.put(r(i.substring(n,n+3)),10),n+=3;n<i.length&&(i.length-n==1?t.put(r(i.substring(n,n+1)),4):i.length-n==2&&t.put(r(i.substring(n,n+2)),7))}},r=function(t){for(var e=0,i=0;i<t.length;i+=1)e=10*e+n(t.charAt(i));return e},n=function(t){if(\"0\"<=t&&t<=\"9\")return t.charCodeAt(0)-\"0\".charCodeAt(0);throw\"illegal char :\"+t};return i},g=function(t){var e=t,i={getMode:function(){return 2},getLength:function(t){return e.length},write:function(t){for(var i=e,n=0;n+1<i.length;)t.put(45*r(i.charAt(n))+r(i.charAt(n+1)),11),n+=2;n<i.length&&t.put(r(i.charAt(n)),6)}},r=function(t){if(\"0\"<=t&&t<=\"9\")return t.charCodeAt(0)-\"0\".charCodeAt(0);if(\"A\"<=t&&t<=\"Z\")return t.charCodeAt(0)-\"A\".charCodeAt(0)+10;switch(t){case\" \":return 36;case\"$\":return 37;case\"%\":return 38;case\"*\":return 39;case\"+\":return 40;case\"-\":return 41;case\".\":return 42;case\"/\":return 43;case\":\":return 44;default:throw\"illegal char :\"+t}};return i},f=function(e){var i=t.stringToBytes(e);return{getMode:function(){return 4},getLength:function(t){return i.length},write:function(t){for(var e=0;e<i.length;e+=1)t.put(i[e],8)}}},w=function(e){var i=t.stringToBytesFuncs.SJIS;if(!i)throw\"sjis not supported.\";!function(){var t=i(\"友\");if(2!=t.length||38726!=(t[0]<<8|t[1]))throw\"sjis not supported.\"}();var r=i(e),n={getMode:function(){return 8},getLength:function(t){return~~(r.length/2)},write:function(t){for(var e=r,i=0;i+1<e.length;){var n=(255&e[i])<<8|255&e[i+1];if(33088<=n&&n<=40956)n-=33088;else{if(!(57408<=n&&n<=60351))throw\"illegal char at \"+(i+1)+\"/\"+n;n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13),i+=2}if(i<e.length)throw\"illegal char at \"+(i+1)}};return n},p=function(){var t=[],e={writeByte:function(e){t.push(255&e)},writeShort:function(t){e.writeByte(t),e.writeByte(t>>>8)},writeBytes:function(t,i,r){i=i||0,r=r||t.length;for(var n=0;n<r;n+=1)e.writeByte(t[n+i])},writeString:function(t){for(var i=0;i<t.length;i+=1)e.writeByte(t.charCodeAt(i))},toByteArray:function(){return t},toString:function(){var e=\"\";e+=\"[\";for(var i=0;i<t.length;i+=1)i>0&&(e+=\",\"),e+=t[i];return e+\"]\"}};return e},v=function(t){var e=t,i=0,r=0,n=0,o={read:function(){for(;n<8;){if(i>=e.length){if(0==n)return-1;throw\"unexpected end of file./\"+n}var t=e.charAt(i);if(i+=1,\"=\"==t)return n=0,-1;t.match(/^\\s$/)||(r=r<<6|s(t.charCodeAt(0)),n+=6)}var o=r>>>n-8&255;return n-=8,o}},s=function(t){if(65<=t&&t<=90)return t-65;if(97<=t&&t<=122)return t-97+26;if(48<=t&&t<=57)return t-48+52;if(43==t)return 62;if(47==t)return 63;throw\"c:\"+t};return o},_=function(t,e,i){for(var r=function(t,e){var i=t,r=e,n=new Array(t*e),o={setPixel:function(t,e,r){n[e*i+t]=r},write:function(t){t.writeString(\"GIF87a\"),t.writeShort(i),t.writeShort(r),t.writeByte(128),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(255),t.writeByte(255),t.writeByte(255),t.writeString(\",\"),t.writeShort(0),t.writeShort(0),t.writeShort(i),t.writeShort(r),t.writeByte(0);var e=s(2);t.writeByte(2);for(var n=0;e.length-n>255;)t.writeByte(255),t.writeBytes(e,n,255),n+=255;t.writeByte(e.length-n),t.writeBytes(e,n,e.length-n),t.writeByte(0),t.writeString(\";\")}},s=function(t){for(var e=1<<t,i=1+(1<<t),r=t+1,o=a(),s=0;s<e;s+=1)o.add(String.fromCharCode(s));o.add(String.fromCharCode(e)),o.add(String.fromCharCode(i));var h,d,u,c=p(),l=(h=c,d=0,u=0,{write:function(t,e){if(t>>>e!=0)throw\"length over\";for(;d+e>=8;)h.writeByte(255&(t<<d|u)),e-=8-d,t>>>=8-d,u=0,d=0;u|=t<<d,d+=e},flush:function(){d>0&&h.writeByte(u)}});l.write(e,r);var g=0,f=String.fromCharCode(n[g]);for(g+=1;g<n.length;){var w=String.fromCharCode(n[g]);g+=1,o.contains(f+w)?f+=w:(l.write(o.indexOf(f),r),o.size()<4095&&(o.size()==1<<r&&(r+=1),o.add(f+w)),f=w)}return l.write(o.indexOf(f),r),l.write(i,r),l.flush(),c.toByteArray()},a=function(){var t={},e=0,i={add:function(r){if(i.contains(r))throw\"dup key:\"+r;t[r]=e,e+=1},size:function(){return e},indexOf:function(e){return t[e]},contains:function(e){return void 0!==t[e]}};return i};return o}(t,e),n=0;n<e;n+=1)for(var o=0;o<t;o+=1)r.setPixel(o,n,i(o,n));var s=p();r.write(s);for(var a=function(){var t=0,e=0,i=0,r=\"\",n={},o=function(t){r+=String.fromCharCode(s(63&t))},s=function(t){if(t<0);else{if(t<26)return 65+t;if(t<52)return t-26+97;if(t<62)return t-52+48;if(62==t)return 43;if(63==t)return 47}throw\"n:\"+t};return n.writeByte=function(r){for(t=t<<8|255&r,e+=8,i+=1;e>=6;)o(t>>>e-6),e-=6},n.flush=function(){if(e>0&&(o(t<<6-e),t=0,e=0),i%3!=0)for(var n=3-i%3,s=0;s<n;s+=1)r+=\"=\"},n.toString=function(){return r},n}(),h=s.toByteArray(),d=0;d<h.length;d+=1)a.writeByte(h[d]);return a.flush(),\"data:image/gif;base64,\"+a};return t}();n.stringToBytesFuncs[\"UTF-8\"]=function(t){return function(t){for(var e=[],i=0;i<t.length;i++){var r=t.charCodeAt(i);r<128?e.push(r):r<2048?e.push(192|r>>6,128|63&r):r<55296||r>=57344?e.push(224|r>>12,128|r>>6&63,128|63&r):(i++,r=65536+((1023&r)<<10|1023&t.charCodeAt(i)),e.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|63&r))}return e}(t)},void 0===(r=\"function\"==typeof(i=function(){return n})?i.apply(e,[]):i)||(t.exports=r)}},e={};function i(r){var n=e[r];if(void 0!==n)return n.exports;var o=e[r]={exports:{}};return t[r](o,o.exports,i),o.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var r={};return(()=>{\"use strict\";i.d(r,{default:()=>$});const t=t=>!!t&&\"object\"==typeof t&&!Array.isArray(t);function e(i,...r){if(!r.length)return i;const n=r.shift();return void 0!==n&&t(i)&&t(n)?(i=Object.assign({},i),Object.keys(n).forEach((r=>{const o=i[r],s=n[r];Array.isArray(o)&&Array.isArray(s)?i[r]=s:t(o)&&t(s)?i[r]=e(Object.assign({},o),s):i[r]=s})),e(i,...r)):i}function n(t,e){const i=document.createElement(\"a\");i.download=e,i.href=t,document.body.appendChild(i),i.click(),document.body.removeChild(i)}const o={L:.07,M:.15,Q:.25,H:.3};class s{constructor({svg:t,type:e,window:i}){this._svg=t,this._type=e,this._window=i}draw(t,e,i,r){let n;switch(this._type){case\"dots\":n=this._drawDot;break;case\"classy\":n=this._drawClassy;break;case\"classy-rounded\":n=this._drawClassyRounded;break;case\"rounded\":n=this._drawRounded;break;case\"extra-rounded\":n=this._drawExtraRounded;break;default:n=this._drawSquare}n.call(this,{x:t,y:e,size:i,getNeighbor:r})}_rotateFigure({x:t,y:e,size:i,rotation:r=0,draw:n}){var o;const s=t+i/2,a=e+i/2;n(),null===(o=this._element)||void 0===o||o.setAttribute(\"transform\",`rotate(${180*r/Math.PI},${s},${a})`)}_basicDot(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"circle\"),this._element.setAttribute(\"cx\",String(i+e/2)),this._element.setAttribute(\"cy\",String(r+e/2)),this._element.setAttribute(\"r\",String(e/2))}}))}_basicSquare(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"rect\"),this._element.setAttribute(\"x\",String(i)),this._element.setAttribute(\"y\",String(r)),this._element.setAttribute(\"width\",String(e)),this._element.setAttribute(\"height\",String(e))}}))}_basicSideRounded(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"d\",`M ${i} ${r}v ${e}h `+e/2+`a ${e/2} ${e/2}, 0, 0, 0, 0 ${-e}`)}}))}_basicCornerRounded(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"d\",`M ${i} ${r}v ${e}h ${e}v `+-e/2+`a ${e/2} ${e/2}, 0, 0, 0, ${-e/2} ${-e/2}`)}}))}_basicCornerExtraRounded(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"d\",`M ${i} ${r}v ${e}h ${e}a ${e} ${e}, 0, 0, 0, ${-e} ${-e}`)}}))}_basicCornersRounded(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"d\",`M ${i} ${r}v `+e/2+`a ${e/2} ${e/2}, 0, 0, 0, ${e/2} ${e/2}h `+e/2+\"v \"+-e/2+`a ${e/2} ${e/2}, 0, 0, 0, ${-e/2} ${-e/2}`)}}))}_drawDot({x:t,y:e,size:i}){this._basicDot({x:t,y:e,size:i,rotation:0})}_drawSquare({x:t,y:e,size:i}){this._basicSquare({x:t,y:e,size:i,rotation:0})}_drawRounded({x:t,y:e,size:i,getNeighbor:r}){const n=r?+r(-1,0):0,o=r?+r(1,0):0,s=r?+r(0,-1):0,a=r?+r(0,1):0,h=n+o+s+a;if(0!==h)if(h>2||n&&o||s&&a)this._basicSquare({x:t,y:e,size:i,rotation:0});else{if(2===h){let r=0;return n&&s?r=Math.PI/2:s&&o?r=Math.PI:o&&a&&(r=-Math.PI/2),void this._basicCornerRounded({x:t,y:e,size:i,rotation:r})}if(1===h){let r=0;return s?r=Math.PI/2:o?r=Math.PI:a&&(r=-Math.PI/2),void this._basicSideRounded({x:t,y:e,size:i,rotation:r})}}else this._basicDot({x:t,y:e,size:i,rotation:0})}_drawExtraRounded({x:t,y:e,size:i,getNeighbor:r}){const n=r?+r(-1,0):0,o=r?+r(1,0):0,s=r?+r(0,-1):0,a=r?+r(0,1):0,h=n+o+s+a;if(0!==h)if(h>2||n&&o||s&&a)this._basicSquare({x:t,y:e,size:i,rotation:0});else{if(2===h){let r=0;return n&&s?r=Math.PI/2:s&&o?r=Math.PI:o&&a&&(r=-Math.PI/2),void this._basicCornerExtraRounded({x:t,y:e,size:i,rotation:r})}if(1===h){let r=0;return s?r=Math.PI/2:o?r=Math.PI:a&&(r=-Math.PI/2),void this._basicSideRounded({x:t,y:e,size:i,rotation:r})}}else this._basicDot({x:t,y:e,size:i,rotation:0})}_drawClassy({x:t,y:e,size:i,getNeighbor:r}){const n=r?+r(-1,0):0,o=r?+r(1,0):0,s=r?+r(0,-1):0,a=r?+r(0,1):0;0!==n+o+s+a?n||s?o||a?this._basicSquare({x:t,y:e,size:i,rotation:0}):this._basicCornerRounded({x:t,y:e,size:i,rotation:Math.PI/2}):this._basicCornerRounded({x:t,y:e,size:i,rotation:-Math.PI/2}):this._basicCornersRounded({x:t,y:e,size:i,rotation:Math.PI/2})}_drawClassyRounded({x:t,y:e,size:i,getNeighbor:r}){const n=r?+r(-1,0):0,o=r?+r(1,0):0,s=r?+r(0,-1):0,a=r?+r(0,1):0;0!==n+o+s+a?n||s?o||a?this._basicSquare({x:t,y:e,size:i,rotation:0}):this._basicCornerExtraRounded({x:t,y:e,size:i,rotation:Math.PI/2}):this._basicCornerExtraRounded({x:t,y:e,size:i,rotation:-Math.PI/2}):this._basicCornersRounded({x:t,y:e,size:i,rotation:Math.PI/2})}}const a={dot:\"dot\",square:\"square\",extraRounded:\"extra-rounded\"},h=Object.values(a);class d{constructor({svg:t,type:e,window:i}){this._svg=t,this._type=e,this._window=i}draw(t,e,i,r){let n;switch(this._type){case a.square:n=this._drawSquare;break;case a.extraRounded:n=this._drawExtraRounded;break;default:n=this._drawDot}n.call(this,{x:t,y:e,size:i,rotation:r})}_rotateFigure({x:t,y:e,size:i,rotation:r=0,draw:n}){var o;const s=t+i/2,a=e+i/2;n(),null===(o=this._element)||void 0===o||o.setAttribute(\"transform\",`rotate(${180*r/Math.PI},${s},${a})`)}_basicDot(t){const{size:e,x:i,y:r}=t,n=e/7;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"clip-rule\",\"evenodd\"),this._element.setAttribute(\"d\",`M ${i+e/2} ${r}a ${e/2} ${e/2} 0 1 0 0.1 0zm 0 ${n}a ${e/2-n} ${e/2-n} 0 1 1 -0.1 0Z`)}}))}_basicSquare(t){const{size:e,x:i,y:r}=t,n=e/7;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"clip-rule\",\"evenodd\"),this._element.setAttribute(\"d\",`M ${i} ${r}v ${e}h ${e}v `+-e+\"z\"+`M ${i+n} ${r+n}h `+(e-2*n)+\"v \"+(e-2*n)+\"h \"+(2*n-e)+\"z\")}}))}_basicExtraRounded(t){const{size:e,x:i,y:r}=t,n=e/7;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\"),this._element.setAttribute(\"clip-rule\",\"evenodd\"),this._element.setAttribute(\"d\",`M ${i} ${r+2.5*n}v `+2*n+`a ${2.5*n} ${2.5*n}, 0, 0, 0, ${2.5*n} ${2.5*n}h `+2*n+`a ${2.5*n} ${2.5*n}, 0, 0, 0, ${2.5*n} ${2.5*-n}v `+-2*n+`a ${2.5*n} ${2.5*n}, 0, 0, 0, ${2.5*-n} ${2.5*-n}h `+-2*n+`a ${2.5*n} ${2.5*n}, 0, 0, 0, ${2.5*-n} ${2.5*n}`+`M ${i+2.5*n} ${r+n}h `+2*n+`a ${1.5*n} ${1.5*n}, 0, 0, 1, ${1.5*n} ${1.5*n}v `+2*n+`a ${1.5*n} ${1.5*n}, 0, 0, 1, ${1.5*-n} ${1.5*n}h `+-2*n+`a ${1.5*n} ${1.5*n}, 0, 0, 1, ${1.5*-n} ${1.5*-n}v `+-2*n+`a ${1.5*n} ${1.5*n}, 0, 0, 1, ${1.5*n} ${1.5*-n}`)}}))}_drawDot({x:t,y:e,size:i,rotation:r}){this._basicDot({x:t,y:e,size:i,rotation:r})}_drawSquare({x:t,y:e,size:i,rotation:r}){this._basicSquare({x:t,y:e,size:i,rotation:r})}_drawExtraRounded({x:t,y:e,size:i,rotation:r}){this._basicExtraRounded({x:t,y:e,size:i,rotation:r})}}const u={dot:\"dot\",square:\"square\"},c=Object.values(u);class l{constructor({svg:t,type:e,window:i}){this._svg=t,this._type=e,this._window=i}draw(t,e,i,r){let n;n=this._type===u.square?this._drawSquare:this._drawDot,n.call(this,{x:t,y:e,size:i,rotation:r})}_rotateFigure({x:t,y:e,size:i,rotation:r=0,draw:n}){var o;const s=t+i/2,a=e+i/2;n(),null===(o=this._element)||void 0===o||o.setAttribute(\"transform\",`rotate(${180*r/Math.PI},${s},${a})`)}_basicDot(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"circle\"),this._element.setAttribute(\"cx\",String(i+e/2)),this._element.setAttribute(\"cy\",String(r+e/2)),this._element.setAttribute(\"r\",String(e/2))}}))}_basicSquare(t){const{size:e,x:i,y:r}=t;this._rotateFigure(Object.assign(Object.assign({},t),{draw:()=>{this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"rect\"),this._element.setAttribute(\"x\",String(i)),this._element.setAttribute(\"y\",String(r)),this._element.setAttribute(\"width\",String(e)),this._element.setAttribute(\"height\",String(e))}}))}_drawDot({x:t,y:e,size:i,rotation:r}){this._basicDot({x:t,y:e,size:i,rotation:r})}_drawSquare({x:t,y:e,size:i,rotation:r}){this._basicSquare({x:t,y:e,size:i,rotation:r})}}const g=\"circle\",f=[[1,1,1,1,1,1,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,1,1,1,1,1,1]],w=[[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]];class p{constructor(t,e){this._roundSize=t=>this._options.dotsOptions.roundSize?Math.floor(t):t,this._window=e,this._element=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"svg\"),this._element.setAttribute(\"width\",String(t.width)),this._element.setAttribute(\"height\",String(t.height)),this._element.setAttribute(\"xmlns:xlink\",\"http://www.w3.org/1999/xlink\"),t.dotsOptions.roundSize||this._element.setAttribute(\"shape-rendering\",\"crispEdges\"),this._element.setAttribute(\"viewBox\",`0 0 ${t.width} ${t.height}`),this._defs=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"defs\"),this._element.appendChild(this._defs),this._imageUri=t.image,this._instanceId=p.instanceCount++,this._options=t}get width(){return this._options.width}get height(){return this._options.height}getElement(){return this._element}async drawQR(t){const e=t.getModuleCount(),i=Math.min(this._options.width,this._options.height)-2*this._options.margin,r=this._options.shape===g?i/Math.sqrt(2):i,n=this._roundSize(r/e);let s={hideXDots:0,hideYDots:0,width:0,height:0};if(this._qr=t,this._options.image){if(await this.loadImage(),!this._image)return;const{imageOptions:t,qrOptions:i}=this._options,r=t.imageSize*o[i.errorCorrectionLevel],a=Math.floor(r*e*e);s=function({originalHeight:t,originalWidth:e,maxHiddenDots:i,maxHiddenAxisDots:r,dotSize:n}){const o={x:0,y:0},s={x:0,y:0};if(t<=0||e<=0||i<=0||n<=0)return{height:0,width:0,hideYDots:0,hideXDots:0};const a=t/e;return o.x=Math.floor(Math.sqrt(i/a)),o.x<=0&&(o.x=1),r&&r<o.x&&(o.x=r),o.x%2==0&&o.x--,s.x=o.x*n,o.y=1+2*Math.ceil((o.x*a-1)/2),s.y=Math.round(s.x*a),(o.y*o.x>i||r&&r<o.y)&&(r&&r<o.y?(o.y=r,o.y%2==0&&o.x--):o.y-=2,s.y=o.y*n,o.x=1+2*Math.ceil((o.y/a-1)/2),s.x=Math.round(s.y/a)),{height:s.y,width:s.x,hideYDots:o.y,hideXDots:o.x}}({originalWidth:this._image.width,originalHeight:this._image.height,maxHiddenDots:a,maxHiddenAxisDots:e-14,dotSize:n})}this.drawBackground(),this.drawDots(((t,i)=>{var r,n,o,a,h,d;return!(this._options.imageOptions.hideBackgroundDots&&t>=(e-s.hideYDots)/2&&t<(e+s.hideYDots)/2&&i>=(e-s.hideXDots)/2&&i<(e+s.hideXDots)/2||(null===(r=f[t])||void 0===r?void 0:r[i])||(null===(n=f[t-e+7])||void 0===n?void 0:n[i])||(null===(o=f[t])||void 0===o?void 0:o[i-e+7])||(null===(a=w[t])||void 0===a?void 0:a[i])||(null===(h=w[t-e+7])||void 0===h?void 0:h[i])||(null===(d=w[t])||void 0===d?void 0:d[i-e+7]))})),this.drawCorners(),this._options.image&&await this.drawImage({width:s.width,height:s.height,count:e,dotSize:n})}drawBackground(){var t,e,i;const r=this._element,n=this._options;if(r){const r=null===(t=n.backgroundOptions)||void 0===t?void 0:t.gradient,o=null===(e=n.backgroundOptions)||void 0===e?void 0:e.color;let s=n.height,a=n.width;if(r||o){const t=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"rect\");this._backgroundClipPath=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"clipPath\"),this._backgroundClipPath.setAttribute(\"id\",`clip-path-background-color-${this._instanceId}`),this._defs.appendChild(this._backgroundClipPath),(null===(i=n.backgroundOptions)||void 0===i?void 0:i.round)&&(s=a=Math.min(n.width,n.height),t.setAttribute(\"rx\",String(s/2*n.backgroundOptions.round))),t.setAttribute(\"x\",String(this._roundSize((n.width-a)/2))),t.setAttribute(\"y\",String(this._roundSize((n.height-s)/2))),t.setAttribute(\"width\",String(a)),t.setAttribute(\"height\",String(s)),this._backgroundClipPath.appendChild(t),this._createColor({options:r,color:o,additionalRotation:0,x:0,y:0,height:n.height,width:n.width,name:`background-color-${this._instanceId}`})}}}drawDots(t){var e,i;if(!this._qr)throw\"QR code is not defined\";const r=this._options,n=this._qr.getModuleCount();if(n>r.width||n>r.height)throw\"The canvas is too small.\";const o=Math.min(r.width,r.height)-2*r.margin,a=r.shape===g?o/Math.sqrt(2):o,h=this._roundSize(a/n),d=this._roundSize((r.width-n*h)/2),u=this._roundSize((r.height-n*h)/2),c=new s({svg:this._element,type:r.dotsOptions.type,window:this._window});this._dotsClipPath=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"clipPath\"),this._dotsClipPath.setAttribute(\"id\",`clip-path-dot-color-${this._instanceId}`),this._defs.appendChild(this._dotsClipPath),this._createColor({options:null===(e=r.dotsOptions)||void 0===e?void 0:e.gradient,color:r.dotsOptions.color,additionalRotation:0,x:0,y:0,height:r.height,width:r.width,name:`dot-color-${this._instanceId}`});for(let e=0;e<n;e++)for(let r=0;r<n;r++)t&&!t(e,r)||(null===(i=this._qr)||void 0===i?void 0:i.isDark(e,r))&&(c.draw(d+r*h,u+e*h,h,((i,o)=>!(r+i<0||e+o<0||r+i>=n||e+o>=n)&&!(t&&!t(e+o,r+i))&&!!this._qr&&this._qr.isDark(e+o,r+i))),c._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(c._element));if(r.shape===g){const t=this._roundSize((o/h-n)/2),e=n+2*t,i=d-t*h,r=u-t*h,s=[],a=this._roundSize(e/2);for(let i=0;i<e;i++){s[i]=[];for(let r=0;r<e;r++)i>=t-1&&i<=e-t&&r>=t-1&&r<=e-t||Math.sqrt((i-a)*(i-a)+(r-a)*(r-a))>a?s[i][r]=0:s[i][r]=this._qr.isDark(r-2*t<0?r:r>=n?r-2*t:r-t,i-2*t<0?i:i>=n?i-2*t:i-t)?1:0}for(let t=0;t<e;t++)for(let n=0;n<e;n++)s[t][n]&&(c.draw(i+n*h,r+t*h,h,((e,i)=>{var r;return!!(null===(r=s[t+i])||void 0===r?void 0:r[n+e])})),c._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(c._element))}}drawCorners(){if(!this._qr)throw\"QR code is not defined\";const t=this._element,e=this._options;if(!t)throw\"Element code is not defined\";const i=this._qr.getModuleCount(),r=Math.min(e.width,e.height)-2*e.margin,n=e.shape===g?r/Math.sqrt(2):r,o=this._roundSize(n/i),a=7*o,u=3*o,p=this._roundSize((e.width-i*o)/2),v=this._roundSize((e.height-i*o)/2);[[0,0,0],[1,0,Math.PI/2],[0,1,-Math.PI/2]].forEach((([t,r,n])=>{var g,_,m,b,y,x,S,C,A,M,$,O,D,z;const k=p+t*o*(i-7),B=v+r*o*(i-7);let P=this._dotsClipPath,I=this._dotsClipPath;if(((null===(g=e.cornersSquareOptions)||void 0===g?void 0:g.gradient)||(null===(_=e.cornersSquareOptions)||void 0===_?void 0:_.color))&&(P=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"clipPath\"),P.setAttribute(\"id\",`clip-path-corners-square-color-${t}-${r}-${this._instanceId}`),this._defs.appendChild(P),this._cornersSquareClipPath=this._cornersDotClipPath=I=P,this._createColor({options:null===(m=e.cornersSquareOptions)||void 0===m?void 0:m.gradient,color:null===(b=e.cornersSquareOptions)||void 0===b?void 0:b.color,additionalRotation:n,x:k,y:B,height:a,width:a,name:`corners-square-color-${t}-${r}-${this._instanceId}`})),(null===(y=e.cornersSquareOptions)||void 0===y?void 0:y.type)&&h.includes(e.cornersSquareOptions.type)){const t=new d({svg:this._element,type:e.cornersSquareOptions.type,window:this._window});t.draw(k,B,a,n),t._element&&P&&P.appendChild(t._element)}else{const t=new s({svg:this._element,type:(null===(x=e.cornersSquareOptions)||void 0===x?void 0:x.type)||e.dotsOptions.type,window:this._window});for(let e=0;e<f.length;e++)for(let i=0;i<f[e].length;i++)(null===(S=f[e])||void 0===S?void 0:S[i])&&(t.draw(k+i*o,B+e*o,o,((t,r)=>{var n;return!!(null===(n=f[e+r])||void 0===n?void 0:n[i+t])})),t._element&&P&&P.appendChild(t._element))}if(((null===(C=e.cornersDotOptions)||void 0===C?void 0:C.gradient)||(null===(A=e.cornersDotOptions)||void 0===A?void 0:A.color))&&(I=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"clipPath\"),I.setAttribute(\"id\",`clip-path-corners-dot-color-${t}-${r}-${this._instanceId}`),this._defs.appendChild(I),this._cornersDotClipPath=I,this._createColor({options:null===(M=e.cornersDotOptions)||void 0===M?void 0:M.gradient,color:null===($=e.cornersDotOptions)||void 0===$?void 0:$.color,additionalRotation:n,x:k+2*o,y:B+2*o,height:u,width:u,name:`corners-dot-color-${t}-${r}-${this._instanceId}`})),(null===(O=e.cornersDotOptions)||void 0===O?void 0:O.type)&&c.includes(e.cornersDotOptions.type)){const t=new l({svg:this._element,type:e.cornersDotOptions.type,window:this._window});t.draw(k+2*o,B+2*o,u,n),t._element&&I&&I.appendChild(t._element)}else{const t=new s({svg:this._element,type:(null===(D=e.cornersDotOptions)||void 0===D?void 0:D.type)||e.dotsOptions.type,window:this._window});for(let e=0;e<w.length;e++)for(let i=0;i<w[e].length;i++)(null===(z=w[e])||void 0===z?void 0:z[i])&&(t.draw(k+i*o,B+e*o,o,((t,r)=>{var n;return!!(null===(n=w[e+r])||void 0===n?void 0:n[i+t])})),t._element&&I&&I.appendChild(t._element))}}))}loadImage(){return new Promise(((t,e)=>{var i;const r=this._options;if(!r.image)return e(\"Image is not defined\");if(null===(i=r.nodeCanvas)||void 0===i?void 0:i.loadImage)r.nodeCanvas.loadImage(r.image).then((e=>{var i,n;if(this._image=e,this._options.imageOptions.saveAsBlob){const t=null===(i=r.nodeCanvas)||void 0===i?void 0:i.createCanvas(this._image.width,this._image.height);null===(n=null==t?void 0:t.getContext(\"2d\"))||void 0===n||n.drawImage(e,0,0),this._imageUri=null==t?void 0:t.toDataURL()}t()})).catch(e);else{const e=new this._window.Image;\"string\"==typeof r.imageOptions.crossOrigin&&(e.crossOrigin=r.imageOptions.crossOrigin),this._image=e,e.onload=async()=>{this._options.imageOptions.saveAsBlob&&(this._imageUri=await async function(t,e){return new Promise((i=>{const r=new e.XMLHttpRequest;r.onload=function(){const t=new e.FileReader;t.onloadend=function(){i(t.result)},t.readAsDataURL(r.response)},r.open(\"GET\",t),r.responseType=\"blob\",r.send()}))}(r.image||\"\",this._window)),t()},e.src=r.image}}))}async drawImage({width:t,height:e,count:i,dotSize:r}){const n=this._options,o=this._roundSize((n.width-i*r)/2),s=this._roundSize((n.height-i*r)/2),a=o+this._roundSize(n.imageOptions.margin+(i*r-t)/2),h=s+this._roundSize(n.imageOptions.margin+(i*r-e)/2),d=t-2*n.imageOptions.margin,u=e-2*n.imageOptions.margin,c=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"image\");c.setAttribute(\"href\",this._imageUri||\"\"),c.setAttribute(\"x\",String(a)),c.setAttribute(\"y\",String(h)),c.setAttribute(\"width\",`${d}px`),c.setAttribute(\"height\",`${u}px`),this._element.appendChild(c)}_createColor({options:t,color:e,additionalRotation:i,x:r,y:n,height:o,width:s,name:a}){const h=s>o?s:o,d=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"rect\");if(d.setAttribute(\"x\",String(r)),d.setAttribute(\"y\",String(n)),d.setAttribute(\"height\",String(o)),d.setAttribute(\"width\",String(s)),d.setAttribute(\"clip-path\",`url('#clip-path-${a}')`),t){let e;if(\"radial\"===t.type)e=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"radialGradient\"),e.setAttribute(\"id\",a),e.setAttribute(\"gradientUnits\",\"userSpaceOnUse\"),e.setAttribute(\"fx\",String(r+s/2)),e.setAttribute(\"fy\",String(n+o/2)),e.setAttribute(\"cx\",String(r+s/2)),e.setAttribute(\"cy\",String(n+o/2)),e.setAttribute(\"r\",String(h/2));else{const h=((t.rotation||0)+i)%(2*Math.PI),d=(h+2*Math.PI)%(2*Math.PI);let u=r+s/2,c=n+o/2,l=r+s/2,g=n+o/2;d>=0&&d<=.25*Math.PI||d>1.75*Math.PI&&d<=2*Math.PI?(u-=s/2,c-=o/2*Math.tan(h),l+=s/2,g+=o/2*Math.tan(h)):d>.25*Math.PI&&d<=.75*Math.PI?(c-=o/2,u-=s/2/Math.tan(h),g+=o/2,l+=s/2/Math.tan(h)):d>.75*Math.PI&&d<=1.25*Math.PI?(u+=s/2,c+=o/2*Math.tan(h),l-=s/2,g-=o/2*Math.tan(h)):d>1.25*Math.PI&&d<=1.75*Math.PI&&(c+=o/2,u+=s/2/Math.tan(h),g-=o/2,l-=s/2/Math.tan(h)),e=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"linearGradient\"),e.setAttribute(\"id\",a),e.setAttribute(\"gradientUnits\",\"userSpaceOnUse\"),e.setAttribute(\"x1\",String(Math.round(u))),e.setAttribute(\"y1\",String(Math.round(c))),e.setAttribute(\"x2\",String(Math.round(l))),e.setAttribute(\"y2\",String(Math.round(g)))}t.colorStops.forEach((({offset:t,color:i})=>{const r=this._window.document.createElementNS(\"http://www.w3.org/2000/svg\",\"stop\");r.setAttribute(\"offset\",100*t+\"%\"),r.setAttribute(\"stop-color\",i),e.appendChild(r)})),d.setAttribute(\"fill\",`url('#${a}')`),this._defs.appendChild(e)}else e&&d.setAttribute(\"fill\",e);this._element.appendChild(d)}}p.instanceCount=0;const v=p,_=\"canvas\",m={};for(let t=0;t<=40;t++)m[t]=t;const b={type:_,shape:\"square\",width:300,height:300,data:\"\",margin:0,qrOptions:{typeNumber:m[0],mode:void 0,errorCorrectionLevel:\"Q\"},imageOptions:{saveAsBlob:!0,hideBackgroundDots:!0,imageSize:.4,crossOrigin:void 0,margin:0},dotsOptions:{type:\"square\",color:\"#000\",roundSize:!0},backgroundOptions:{round:0,color:\"#fff\"}};function y(t){const e=Object.assign({},t);if(!e.colorStops||!e.colorStops.length)throw\"Field 'colorStops' is required in gradient\";return e.rotation?e.rotation=Number(e.rotation):e.rotation=0,e.colorStops=e.colorStops.map((t=>Object.assign(Object.assign({},t),{offset:Number(t.offset)}))),e}function x(t){const e=Object.assign({},t);return e.width=Number(e.width),e.height=Number(e.height),e.margin=Number(e.margin),e.imageOptions=Object.assign(Object.assign({},e.imageOptions),{hideBackgroundDots:Boolean(e.imageOptions.hideBackgroundDots),imageSize:Number(e.imageOptions.imageSize),margin:Number(e.imageOptions.margin)}),e.margin>Math.min(e.width,e.height)&&(e.margin=Math.min(e.width,e.height)),e.dotsOptions=Object.assign({},e.dotsOptions),e.dotsOptions.gradient&&(e.dotsOptions.gradient=y(e.dotsOptions.gradient)),e.cornersSquareOptions&&(e.cornersSquareOptions=Object.assign({},e.cornersSquareOptions),e.cornersSquareOptions.gradient&&(e.cornersSquareOptions.gradient=y(e.cornersSquareOptions.gradient))),e.cornersDotOptions&&(e.cornersDotOptions=Object.assign({},e.cornersDotOptions),e.cornersDotOptions.gradient&&(e.cornersDotOptions.gradient=y(e.cornersDotOptions.gradient))),e.backgroundOptions&&(e.backgroundOptions=Object.assign({},e.backgroundOptions),e.backgroundOptions.gradient&&(e.backgroundOptions.gradient=y(e.backgroundOptions.gradient))),e}var S=i(873),C=i.n(S);function A(t){if(!t)throw new Error(\"Extension must be defined\");\".\"===t[0]&&(t=t.substring(1));const e={bmp:\"image/bmp\",gif:\"image/gif\",ico:\"image/vnd.microsoft.icon\",jpeg:\"image/jpeg\",jpg:\"image/jpeg\",png:\"image/png\",svg:\"image/svg+xml\",tif:\"image/tiff\",tiff:\"image/tiff\",webp:\"image/webp\",pdf:\"application/pdf\"}[t.toLowerCase()];if(!e)throw new Error(`Extension \"${t}\" is not supported`);return e}class M{constructor(t){(null==t?void 0:t.jsdom)?this._window=new t.jsdom(\"\",{resources:\"usable\"}).window:this._window=window,this._options=t?x(e(b,t)):b,this.update()}static _clearContainer(t){t&&(t.innerHTML=\"\")}_setupSvg(){if(!this._qr)return;const t=new v(this._options,this._window);this._svg=t.getElement(),this._svgDrawingPromise=t.drawQR(this._qr).then((()=>{var e;this._svg&&(null===(e=this._extension)||void 0===e||e.call(this,t.getElement(),this._options))}))}_setupCanvas(){var t,e;this._qr&&((null===(t=this._options.nodeCanvas)||void 0===t?void 0:t.createCanvas)?(this._nodeCanvas=this._options.nodeCanvas.createCanvas(this._options.width,this._options.height),this._nodeCanvas.width=this._options.width,this._nodeCanvas.height=this._options.height):(this._domCanvas=document.createElement(\"canvas\"),this._domCanvas.width=this._options.width,this._domCanvas.height=this._options.height),this._setupSvg(),this._canvasDrawingPromise=null===(e=this._svgDrawingPromise)||void 0===e?void 0:e.then((()=>{var t;if(!this._svg)return;const e=this._svg,i=(new this._window.XMLSerializer).serializeToString(e),r=btoa(i),n=`data:${A(\"svg\")};base64,${r}`;if(null===(t=this._options.nodeCanvas)||void 0===t?void 0:t.loadImage)return this._options.nodeCanvas.loadImage(n).then((t=>{var e,i;t.width=this._options.width,t.height=this._options.height,null===(i=null===(e=this._nodeCanvas)||void 0===e?void 0:e.getContext(\"2d\"))||void 0===i||i.drawImage(t,0,0)}));{const t=new this._window.Image;return new Promise((e=>{t.onload=()=>{var i,r;null===(r=null===(i=this._domCanvas)||void 0===i?void 0:i.getContext(\"2d\"))||void 0===r||r.drawImage(t,0,0),e()},t.src=n}))}})))}async _getElement(t=\"png\"){if(!this._qr)throw\"QR code is empty\";return\"svg\"===t.toLowerCase()?(this._svg&&this._svgDrawingPromise||this._setupSvg(),await this._svgDrawingPromise,this._svg):((this._domCanvas||this._nodeCanvas)&&this._canvasDrawingPromise||this._setupCanvas(),await this._canvasDrawingPromise,this._domCanvas||this._nodeCanvas)}update(t){M._clearContainer(this._container),this._options=t?x(e(this._options,t)):this._options,this._options.data&&(this._qr=C()(this._options.qrOptions.typeNumber,this._options.qrOptions.errorCorrectionLevel),this._qr.addData(this._options.data,this._options.qrOptions.mode||function(t){switch(!0){case/^[0-9]*$/.test(t):return\"Numeric\";case/^[0-9A-Z $%*+\\-./:]*$/.test(t):return\"Alphanumeric\";default:return\"Byte\"}}(this._options.data)),this._qr.make(),this._options.type===_?this._setupCanvas():this._setupSvg(),this.append(this._container))}append(t){if(t){if(\"function\"!=typeof t.appendChild)throw\"Container should be a single DOM node\";this._options.type===_?this._domCanvas&&t.appendChild(this._domCanvas):this._svg&&t.appendChild(this._svg),this._container=t}}applyExtension(t){if(!t)throw\"Extension function should be defined.\";this._extension=t,this.update()}deleteExtension(){this._extension=void 0,this.update()}async getRawData(t=\"png\"){if(!this._qr)throw\"QR code is empty\";const e=await this._getElement(t),i=A(t);if(!e)return null;if(\"svg\"===t.toLowerCase()){const t=`<?xml version=\"1.0\" standalone=\"no\"?>\\r\\n${(new this._window.XMLSerializer).serializeToString(e)}`;return\"undefined\"==typeof Blob||this._options.jsdom?Buffer.from(t):new Blob([t],{type:i})}return new Promise((t=>{const r=e;if(\"toBuffer\"in r)if(\"image/png\"===i)t(r.toBuffer(i));else if(\"image/jpeg\"===i)t(r.toBuffer(i));else{if(\"application/pdf\"!==i)throw Error(\"Unsupported extension\");t(r.toBuffer(i))}else\"toBlob\"in r&&r.toBlob(t,i,1)}))}async download(t){if(!this._qr)throw\"QR code is empty\";if(\"undefined\"==typeof Blob)throw\"Cannot download in Node.js, call getRawData instead.\";let e=\"png\",i=\"qr\";\"string\"==typeof t?(e=t,console.warn(\"Extension is deprecated as argument for 'download' method, please pass object { name: '...', extension: '...' } as argument\")):\"object\"==typeof t&&null!==t&&(t.name&&(i=t.name),t.extension&&(e=t.extension));const r=await this._getElement(e);if(r)if(\"svg\"===e.toLowerCase()){let t=(new XMLSerializer).serializeToString(r);t='<?xml version=\"1.0\" standalone=\"no\"?>\\r\\n'+t,n(`data:${A(e)};charset=utf-8,${encodeURIComponent(t)}`,`${i}.svg`)}else n(r.toDataURL(A(e)),`${i}.${e}`)}}const $=M})(),r.default})()));\n//# sourceMappingURL=qr-code-styling.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr-code-styling/lib/qr-code-styling.js\n");

/***/ })

};
;