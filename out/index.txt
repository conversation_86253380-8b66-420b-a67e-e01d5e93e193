1:"$Sreact.fragment"
2:I[1383,["156","static/chunks/156-3422de1ed5e7e998.js","874","static/chunks/874-437a265a67d6cfee.js","237","static/chunks/237-632daa672173e060.js","177","static/chunks/app/layout-3ff8c680681dce50.js"],"MantineProvider"]
3:I[8641,["156","static/chunks/156-3422de1ed5e7e998.js","874","static/chunks/874-437a265a67d6cfee.js","237","static/chunks/237-632daa672173e060.js","177","static/chunks/app/layout-3ff8c680681dce50.js"],"AppHeader"]
4:I[1590,["156","static/chunks/156-3422de1ed5e7e998.js","874","static/chunks/874-437a265a67d6cfee.js","237","static/chunks/237-632daa672173e060.js","177","static/chunks/app/layout-3ff8c680681dce50.js"],"Container"]
5:I[7555,[],""]
6:I[1295,[],""]
7:I[894,[],"ClientPageRoot"]
8:I[932,["545","static/chunks/c16f53c3-347d59f65569d248.js","156","static/chunks/156-3422de1ed5e7e998.js","198","static/chunks/198-25053f2483bd60d8.js","974","static/chunks/app/page-52d8876503d79488.js"],"default"]
b:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[9665,[],"MetadataBoundary"]
12:"$Sreact.suspense"
14:I[8393,[],""]
:HL["/_next/static/css/eb8abf01ba248c84.css","style"]
:HL["/_next/static/css/dcf62dc2de18d7f8.css","style"]
0:{"P":null,"b":"b7UDc5bgYYlvKbqCMqsTx","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/eb8abf01ba248c84.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/dcf62dc2de18d7f8.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","meta",null,{"name":"google-adsense-account","content":"ca-pub-****************"}],["$","script",null,{"async":true,"src":"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************","crossOrigin":"anonymous"}],["$","script",null,{"async":true,"src":"https://www.googletagmanager.com/gtag/js?id=G-RCMH6J3KQ9"}],["$","script",null,{"dangerouslySetInnerHTML":{"__html":"\n              window.dataLayer = window.dataLayer || [];\n              function gtag(){dataLayer.push(arguments);}\n              gtag('js', new Date());\n              gtag('config', 'G-RCMH6J3KQ9');\n            "}}]]}],["$","body",null,{"children":["$","$L2",null,{"theme":{"primaryColor":"cyan","radius":{"sm":"12px","md":"16px","lg":"20px","xl":"24px"}},"children":[["$","$L3",null,{}],["$","$L4",null,{"fluid":true,"maw":1200,"pt":"xl","pb":"xl","children":["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]}]]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":{},"promises":["$@9","$@a"]}],null,["$","$Lb",null,{"children":["$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$Lf",null,{"children":"$L10"}],null],["$","$L11",null,{"children":["$","div",null,{"hidden":true,"children":["$","$12",null,{"fallback":null,"children":"$L13"}]}]}]]}],false]],"m":"$undefined","G":["$14",[]],"s":false,"S":true}
9:{}
a:"$0:f:0:1:2:children:1:props:children:0:props:params"
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
15:I[8175,[],"IconMark"]
e:{"metadata":[["$","title","0",{"children":"Free QR Code Generator Online | QRCode Donkey"}],["$","meta","1",{"name":"description","content":"Generate custom QR codes for free with QRCode Donkey. Create high-quality QR codes for websites, social media, business cards, and more. No sign-up required!"}],["$","link","2",{"rel":"icon","href":"/donkey-128.png"}],["$","$L15","3",{}]],"error":null,"digest":"$undefined"}
13:"$e:metadata"
