(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{4112:(e,n,s)=>{Promise.resolve().then(s.bind(s,1590)),Promise.resolve().then(s.bind(s,1383)),Promise.resolve().then(s.t.bind(s,2500,23)),Promise.resolve().then(s.bind(s,8641)),Promise.resolve().then(s.t.bind(s,7107,23))},7107:()=>{},8641:(e,n,s)=>{"use strict";s.d(n,{AppHeader:()=>x});var o=s(5155),r=s(3695),i=s(1587),t=s(8141),l=s(9077),d=s(3751),a=s(8887),c=s(6874),h=s.n(c);function p(e){let{to:n,label:s}=e;return(0,o.jsx)(h(),{href:n,style:{textDecoration:"none"},children:(0,o.jsx)(r.M,{component:"span",size:"sm",children:s})})}function x(){return(0,o.jsxs)(i.s,{direction:{base:"column",xs:"row"},align:"center",gap:{base:"sm",xs:"lg"},p:"xs",wrap:"wrap",style:{borderBottom:"1px solid var(--mantine-color-gray-1)",whiteSpace:"nowrap"},children:[(0,o.jsx)(i.s,{direction:"column",gap:"sm",children:(0,o.jsxs)(i.s,{direction:"row",align:"center",gap:"xs",children:[(0,o.jsx)(t.o,{children:(0,o.jsx)(l._,{w:52,src:"/donkey-256.png",alt:"Cool donkey logo | QRCode Donkey"})}),(0,o.jsx)(d.h,{order:2,children:"QRCode Donkey"})]})}),(0,o.jsx)(a.E,{size:"sm",c:"dimmed",children:"Free QR Code generator"}),(0,o.jsxs)(i.s,{gap:"lg",children:[(0,o.jsx)(p,{to:"/",label:"QR Code Generator"}),(0,o.jsx)(p,{to:"/about",label:"What is a QR Code?"})]})]})}}},e=>{e.O(0,[365,578,156,874,237,441,964,358],()=>e(e.s=4112)),_N_E=e.O()}]);