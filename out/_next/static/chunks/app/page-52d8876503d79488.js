(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{932:(e,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>_});var t=r(5155),l=r(2115),n=r(1587),a=r(1220),s=r(9791),d=r(6205),i=r.n(d),c=r(8593),u=r(610),p=r(4534),g=r(54),h=r(6707),y=r(1659);function m(e){let{initialUrl:o,initialDotType:r,initialDotColor:l,initialBackgroundColor:n,onGenerate:s,onDotTypeChange:d,onDotColorChange:i,onBackgroundColorChange:m}=e,x=(0,y.m)({mode:"uncontrolled",initialValues:{url:o},validate:{url:e=>/[(http(s)?)://(www.)?a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/.test(e)?null:"Invalid URL"}});return(0,t.jsx)("form",{onSubmit:x.onSubmit(e=>s({url:e.url,dotType:r,dotColor:l,backgroundColor:n})),children:(0,t.jsxs)(a.B,{children:[(0,t.jsx)(c.k,{size:"xl",label:"Your URL",placeholder:"https://www.sample.com",autoComplete:"qrcode-url",...x.getInputProps("url"),styles:{input:{fontWeight:"bold"}}},x.key("url")),(0,t.jsxs)(u.r,{cols:3,children:[(0,t.jsx)(p.l,{size:"md",label:"Dot style",value:r,onChange:(e,o)=>d(o.value),data:[{value:"square",label:"Square"},{value:"dots",label:"Dots"},{value:"rounded",label:"Rounded"},{value:"extra-rounded",label:"Extra rounded"},{value:"classy",label:"Classy"},{value:"classy-rounded",label:"Classy rounded"}]}),(0,t.jsx)(g.A,{size:"md",label:"Dot color",value:l,onChange:i}),(0,t.jsx)(g.A,{size:"md",label:"Background color",value:n,onChange:m})]}),(0,t.jsx)(h.$,{size:"lg",type:"submit",children:"Generate"})]})})}var x=r(8141);function f(e){let{data:o,dotType:r,dotColor:n,backgroundColor:a,size:s=300,onSvgGenerated:d}=e,c=(0,l.useRef)(null),u=(0,l.useRef)(null);return(0,l.useEffect)(()=>{u.current||(u.current=new(i())({width:s,height:s,type:"svg",data:o,image:"",dotsOptions:{color:n,type:r},backgroundOptions:{color:a},imageOptions:{crossOrigin:"anonymous",margin:20}})),c.current&&u.current&&u.current.append(c.current)},[]),(0,l.useEffect)(()=>{if(u.current&&(u.current.update({data:o,dotsOptions:{type:r,color:n},backgroundOptions:{color:a}}),d&&c.current)){let e=c.current.querySelector("svg");e&&d(new XMLSerializer().serializeToString(e))}},[o,r,n,a,d]),(0,t.jsx)(x.o,{children:(0,t.jsx)("div",{ref:c})})}var b=r(3751),C=r(8887),w=r(9436),j=r(112),k=r(3191),v=r(4594);function S(e){let{history:o,onLoadHistoryEntry:r,onDeleteHistoryEntry:l}=e;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.h,{order:2,children:"History"}),0===o.length?(0,t.jsx)(C.E,{children:"No history yet. Generate a QR code to see it here!"}):(0,t.jsx)(u.r,{cols:{base:1,sm:2,md:3},children:o.map((e,o)=>(0,t.jsxs)(w.Z,{shadow:"sm",padding:"lg",radius:"md",withBorder:!0,pos:"relative",children:[(0,t.jsx)(j.Y,{justify:"space-between",mb:"xs",children:(0,t.jsx)(C.E,{fw:500,truncate:!0,children:e.url})}),(0,t.jsx)(k.M,{variant:"transparent",color:"red",onClick:()=>l(o),"aria-label":"Delete QR code",pos:"absolute",top:10,right:10,children:(0,t.jsx)(v.A,{style:{width:"70%",height:"70%"},stroke:1.5})}),(0,t.jsx)(x.o,{children:(0,t.jsx)(f,{data:e.url,dotType:e.dotType,dotColor:e.dotColor,backgroundColor:e.backgroundColor,size:150})}),(0,t.jsx)(h.$,{variant:"light",fullWidth:!0,mt:"md",radius:"md",onClick:()=>r(e),children:"Load"})]},o))})]})}let O="qr_code_history",T="https://www.qrcode-donkey.com",E="square",R="#000000",z="#ffffff";function D(){let[e,o]=(0,l.useState)(T),[r,d]=(0,l.useState)(E),[c,u]=(0,l.useState)(R),[p,g]=(0,l.useState)(z),[h,y]=(0,l.useState)([]),x=(0,l.useRef)(null),b=(0,l.useRef)(null),C=e=>{try{localStorage.setItem(O,JSON.stringify(e))}catch(e){console.error("Failed to save history to local storage:",e)}};(0,l.useEffect)(()=>{if(b.current||(b.current=new(i())({width:300,height:300,type:"svg",data:e,image:"",dotsOptions:{color:c,type:r},backgroundOptions:{color:p},imageOptions:{crossOrigin:"anonymous",margin:20}})),x.current&&b.current){let e=new URLSearchParams(window.location.search).get("config");if(e)try{let r=JSON.parse(atob(e));r.url&&o(r.url),r.dotType&&d(r.dotType),r.dotColor&&u(r.dotColor),r.backgroundColor&&g(r.backgroundColor)}catch(e){console.error("Failed to parse config from URL:",e)}b.current.append(x.current)}y((()=>{try{let e=localStorage.getItem(O);return e?JSON.parse(e):[]}catch(e){return console.error("Failed to load history from local storage:",e),[]}})())},[]),(0,l.useEffect)(()=>{let o=new URLSearchParams(window.location.search),t=e===T&&r===E&&c===R&&p===z;if(t)o.delete("config");else{let t=btoa(JSON.stringify({url:e,dotType:r,dotColor:c,backgroundColor:p}));o.set("config",t)}o.delete("dotType"),o.delete("dotColor"),o.delete("backgroundColor"),window.history.replaceState({},"","".concat(window.location.pathname).concat(t?"":"?").concat(o)),b.current&&b.current.update({data:e,dotsOptions:{type:r,color:c},backgroundOptions:{color:p}})},[e,r,c,p]);let w=async e=>{o(e.url),d(e.dotType),u(e.dotColor),g(e.backgroundColor);let r={url:e.url,dotType:e.dotType,dotColor:e.dotColor,backgroundColor:e.backgroundColor,timestamp:Date.now()};y(e=>{let o=[r,...e].slice(0,100);return C(o),o})};return(0,t.jsxs)(n.s,{gap:"md",justify:"space-between",align:"flex-start",wrap:"wrap",children:[(0,t.jsx)(a.B,{gap:"xl",style:{flexGrow:1},children:(0,t.jsxs)(s.x,{gutter:"xl",breakpoints:{xs:"320px",sm:"640px",md:"768px",lg:"1024px",xl:"1200px"},children:[(0,t.jsx)(s.x.Col,{span:{sm:12,md:6,lg:8},order:{base:2,md:1},children:(0,t.jsx)(m,{initialUrl:e,initialDotType:r,initialDotColor:c,initialBackgroundColor:p,onGenerate:w,onDotTypeChange:d,onDotColorChange:u,onBackgroundColorChange:g})}),(0,t.jsx)(s.x.Col,{span:{sm:12,md:6,lg:4},order:{base:1,md:2},children:(0,t.jsx)(f,{data:e,dotType:r,dotColor:c,backgroundColor:p})})]})}),(0,t.jsx)(a.B,{w:"100%",gap:"md",children:(0,t.jsx)(S,{history:h,onLoadHistoryEntry:e=>{o(e.url),d(e.dotType),u(e.dotColor),g(e.backgroundColor)},onDeleteHistoryEntry:e=>{let o=h.filter((o,r)=>r!==e);y(o),C(o)}})})]})}function _(){return(0,t.jsx)(D,{})}},8235:(e,o,r)=>{Promise.resolve().then(r.bind(r,932))}},e=>{e.O(0,[545,156,198,441,964,358],()=>e(e.s=8235)),_N_E=e.O()}]);