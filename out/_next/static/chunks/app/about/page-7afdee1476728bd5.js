(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{5980:(e,r,s)=>{"use strict";s.d(r,{TypographyStylesProvider:()=>d});var o=s(5155);s(2115);var a=s(3664),t=s(862),l=s(311),y=s(6960),p={root:"m_d6493fad"};let i={},d=(0,y.P9)((e,r)=>{let s=(0,a.Y)("TypographyStylesProvider",i,e),{classNames:y,className:d,style:n,styles:h,unstyled:v,...P}=s,_=(0,t.I)({name:"TypographyStylesProvider",classes:p,props:s,className:d,style:n,classNames:y,styles:h,unstyled:v});return(0,o.jsx)(l.a,{ref:r,..._("root"),...P})});d.classes=p,d.displayName="@mantine/core/TypographyStylesProvider"},7204:(e,r,s)=>{Promise.resolve().then(s.bind(s,5980)),Promise.resolve().then(s.t.bind(s,6874,23))}},e=>{e.O(0,[156,874,441,964,358],()=>e(e.s=7204)),_N_E=e.O()}]);