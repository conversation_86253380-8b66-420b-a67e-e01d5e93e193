"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[156],{128:(t,e,r)=>{r.d(e,{g:()=>n});function n(t,e){return"number"==typeof t.primaryShade?t.primaryShade:"dark"===e?t.primaryShade.dark:t.primaryShade.light}},311:(t,e,r)=>{r.d(e,{a:()=>v});var n=r(5155),o=r(2115),a=r(2596),c=r(8570),i=r(8772),l=r(3656),s=r(3131);function d(t){return t.startsWith("data-")?t:"data-".concat(t)}function f(t,e){return Array.isArray(t)?[...t].reduce((t,r)=>({...t,...f(r,e)}),{}):"function"==typeof t?t(e):null==t?{}:t}var p=r(9537);let u={m:{type:"spacing",property:"margin"},mt:{type:"spacing",property:"marginTop"},mb:{type:"spacing",property:"marginBottom"},ml:{type:"spacing",property:"marginLeft"},mr:{type:"spacing",property:"marginRight"},ms:{type:"spacing",property:"marginInlineStart"},me:{type:"spacing",property:"marginInlineEnd"},mx:{type:"spacing",property:"marginInline"},my:{type:"spacing",property:"marginBlock"},p:{type:"spacing",property:"padding"},pt:{type:"spacing",property:"paddingTop"},pb:{type:"spacing",property:"paddingBottom"},pl:{type:"spacing",property:"paddingLeft"},pr:{type:"spacing",property:"paddingRight"},ps:{type:"spacing",property:"paddingInlineStart"},pe:{type:"spacing",property:"paddingInlineEnd"},px:{type:"spacing",property:"paddingInline"},py:{type:"spacing",property:"paddingBlock"},bd:{type:"border",property:"border"},bg:{type:"color",property:"background"},c:{type:"textColor",property:"color"},opacity:{type:"identity",property:"opacity"},ff:{type:"fontFamily",property:"fontFamily"},fz:{type:"fontSize",property:"fontSize"},fw:{type:"identity",property:"fontWeight"},lts:{type:"size",property:"letterSpacing"},ta:{type:"identity",property:"textAlign"},lh:{type:"lineHeight",property:"lineHeight"},fs:{type:"identity",property:"fontStyle"},tt:{type:"identity",property:"textTransform"},td:{type:"identity",property:"textDecoration"},w:{type:"spacing",property:"width"},miw:{type:"spacing",property:"minWidth"},maw:{type:"spacing",property:"maxWidth"},h:{type:"spacing",property:"height"},mih:{type:"spacing",property:"minHeight"},mah:{type:"spacing",property:"maxHeight"},bgsz:{type:"size",property:"backgroundSize"},bgp:{type:"identity",property:"backgroundPosition"},bgr:{type:"identity",property:"backgroundRepeat"},bga:{type:"identity",property:"backgroundAttachment"},pos:{type:"identity",property:"position"},top:{type:"size",property:"top"},left:{type:"size",property:"left"},bottom:{type:"size",property:"bottom"},right:{type:"size",property:"right"},inset:{type:"size",property:"inset"},display:{type:"identity",property:"display"},flex:{type:"identity",property:"flex"}};var m=r(6325),y=r(6390);let h=(0,o.forwardRef)((t,e)=>{var r;let{component:o,style:h,__vars:v,className:g,variant:b,mod:D,size:k,hiddenFrom:x,visibleFrom:w,lightHidden:S,darkHidden:C,renderRoot:A,__size:N,...j}=t,I=(0,s.xd)(),{styleProps:z,rest:T}=(0,p.j)(j),M=(0,l.NL)(),F=null==M||null==(r=M())?void 0:r(z.sx),W=(0,y.C)(),E=(0,m.X)({styleProps:z,theme:I,data:u}),H={ref:e,style:function(t){let{theme:e,style:r,vars:n,styleProps:o}=t,a=f(r,e),c=f(n,e);return{...a,...c,...o}}({theme:I,style:h,vars:v,styleProps:E.inlineStyles}),className:(0,a.A)(g,F,{[W]:E.hasResponsiveStyles,"mantine-light-hidden":S,"mantine-dark-hidden":C,["mantine-hidden-from-".concat(x)]:x,["mantine-visible-from-".concat(w)]:w}),"data-variant":b,"data-size":(0,i.t)(k)?void 0:k||void 0,size:N,...function t(e){return e?"string"==typeof e?{[d(e)]:!0}:Array.isArray(e)?[...e].reduce((e,r)=>({...e,...t(r)}),{}):Object.keys(e).reduce((t,r)=>{let n=e[r];return void 0===n||""===n||!1===n||null===n||(t[d(r)]=e[r]),t},{}):null}(D),...T};return(0,n.jsxs)(n.Fragment,{children:[E.hasResponsiveStyles&&(0,n.jsx)(c.K,{selector:".".concat(W),styles:E.styles,media:E.media}),"function"==typeof A?A(H):(0,n.jsx)(o||"div",{...H})]})});h.displayName="@mantine/core/Box";let v=h},714:(t,e,r)=>{r.d(e,{B:()=>o,X:()=>a});var n=r(8792);function o(t,e){if("string"!=typeof t||e>1||e<0)return"rgba(0, 0, 0, 1)";if(t.startsWith("var("))return"color-mix(in srgb, ".concat(t,", transparent ").concat((1-e)*100,"%)");if(t.startsWith("oklch"))return t.includes("/")?t.replace(/\/\s*[\d.]+\s*\)/,"/ ".concat(e,")")):t.replace(")"," / ".concat(e,")"));let{r,g:o,b:a}=(0,n.K)(t);return"rgba(".concat(r,", ").concat(o,", ").concat(a,", ").concat(e,")")}let a=o},862:(t,e,r)=>{r.d(e,{I:()=>p}),r(2115),r(5155);var n=r(3656),o=r(3131),a=r(2596);let c={always:"mantine-focus-always",auto:"mantine-focus-auto",never:"mantine-focus-never"};var i=r(9787);function l(t){let{selector:e,stylesCtx:r,theme:n,classNames:o,props:a}=t;return(0,i.J)({theme:n,classNames:o,props:a,stylesCtx:r})[e]}var s=r(4092);function d(t){let{style:e,theme:r}=t;return Array.isArray(e)?[...e].reduce((t,e)=>({...t,...d({style:e,theme:r})}),{}):"function"==typeof e?e(r):null==e?{}:e}var f=r(1526);function p(t){let{name:e,classes:r,props:p,stylesCtx:u,className:m,style:y,rootSelector:h="root",unstyled:v,classNames:g,styles:b,vars:D,varsResolver:k}=t,x=(0,o.xd)(),w=(0,n.AI)(),S=(0,n.If)(),C=(0,n.FI)(),A=(Array.isArray(e)?e:[e]).filter(t=>t),{withStylesTransform:N,getTransformedStyles:j}=function(t){var e;let{props:r,stylesCtx:a,themeName:c}=t,i=(0,o.xd)(),l=null==(e=(0,n.m6)())?void 0:e();return{getTransformedStyles:t=>l?[...t.map(t=>l(t,{props:r,theme:i,ctx:a})),...c.map(t=>{var e;return l(null==(e=i.components[t])?void 0:e.styles,{props:r,theme:i,ctx:a})})].filter(Boolean):[],withStylesTransform:!!l}}({props:p,stylesCtx:u,themeName:A});return(t,e)=>({className:function(t){let{theme:e,options:r,themeName:n,selector:o,classNamesPrefix:s,classNames:d,classes:f,unstyled:p,className:u,rootSelector:m,props:y,stylesCtx:h,withStaticClasses:v,headless:g,transformedStyles:b}=t;return(0,a.A)(function(t){let{theme:e,options:r,unstyled:n}=t;return(0,a.A)((null==r?void 0:r.focusable)&&!n&&(e.focusClassName||c[e.focusRing]),(null==r?void 0:r.active)&&!n&&e.activeClassName)}({theme:e,options:r,unstyled:p||g}),function(t){let{themeName:e,theme:r,selector:n,props:o,stylesCtx:a}=t;return e.map(t=>{var e,c;return null==(e=(0,i.J)({theme:r,classNames:null==(c=r.components[t])?void 0:c.classNames,props:o,stylesCtx:a}))?void 0:e[n]})}({theme:e,themeName:n,selector:o,props:y,stylesCtx:h}),function(t){let{options:e,classes:r,selector:n,unstyled:o}=t;return(null==e?void 0:e.variant)&&!o?r["".concat(n,"--").concat(e.variant)]:void 0}({options:r,classes:f,selector:o,unstyled:p}),l({selector:o,stylesCtx:h,theme:e,classNames:d,props:y}),l({selector:o,stylesCtx:h,theme:e,classNames:b,props:y}),function(t){let{selector:e,stylesCtx:r,options:n,props:o,theme:a}=t;return(0,i.J)({theme:a,classNames:null==n?void 0:n.classNames,props:(null==n?void 0:n.props)||o,stylesCtx:r})[e]}({selector:o,stylesCtx:h,options:r,props:y,theme:e}),function(t){let{rootSelector:e,selector:r,className:n}=t;return e===r?n:void 0}({rootSelector:m,selector:o,className:u}),function(t){let{selector:e,classes:r,unstyled:n}=t;return n?void 0:r[e]}({selector:o,classes:f,unstyled:p||g}),v&&!g&&function(t){let{themeName:e,classNamesPrefix:r,selector:n,withStaticClass:o}=t;return!1===o?[]:e.map(t=>"".concat(r,"-").concat(t,"-").concat(n))}({themeName:n,classNamesPrefix:s,selector:o,withStaticClass:null==r?void 0:r.withStaticClass}),null==r?void 0:r.className)}({theme:x,options:e,themeName:A,selector:t,classNamesPrefix:w,classNames:g,classes:r,unstyled:v,className:m,rootSelector:h,props:p,stylesCtx:u,withStaticClasses:S,headless:C,transformedStyles:j([null==e?void 0:e.styles,b])}),style:function(t){let{theme:e,themeName:r,selector:n,options:o,props:a,stylesCtx:c,rootSelector:i,styles:l,style:p,vars:u,varsResolver:m,headless:y,withStylesTransform:h}=t;return{...!h&&function(t){let{theme:e,themeName:r,props:n,stylesCtx:o,selector:a}=t;return r.map(t=>{var r;return(0,s.N)({theme:e,styles:null==(r=e.components[t])?void 0:r.styles,props:n,stylesCtx:o})[a]}).reduce((t,e)=>({...t,...e}),{})}({theme:e,themeName:r,props:a,stylesCtx:c,selector:n}),...!h&&(0,s.N)({theme:e,styles:l,props:a,stylesCtx:c})[n],...!h&&(0,s.N)({theme:e,styles:null==o?void 0:o.styles,props:(null==o?void 0:o.props)||a,stylesCtx:c})[n],...function(t){var e;let{vars:r,varsResolver:n,theme:o,props:a,stylesCtx:c,selector:i,themeName:l,headless:s}=t;return null==(e=[s?{}:null==n?void 0:n(o,a,c),...l.map(t=>{var e,r,n;return null==(n=o.components)||null==(r=n[t])||null==(e=r.vars)?void 0:e.call(r,o,a,c)}),null==r?void 0:r(o,a,c)].reduce((t,e)=>(e&&Object.keys(e).forEach(r=>{t[r]={...t[r],...(0,f.J)(e[r])}}),t),{}))?void 0:e[i]}({theme:e,props:a,stylesCtx:c,vars:u,varsResolver:m,selector:n,themeName:r,headless:y}),...i===n?d({style:p,theme:e}):null,...d({style:null==o?void 0:o.style,theme:e})}}({theme:x,themeName:A,selector:t,options:e,props:p,stylesCtx:u,rootSelector:h,styles:b,style:y,vars:D,varsResolver:k,headless:C,withStylesTransform:N})})}},1180:(t,e,r)=>{r.d(e,{r:()=>o});var n=r(8271);function o(t,e){let r=(0,n.g)({color:t||e.primaryColor,theme:e});return r.variable?"var(".concat(r.variable,")"):t}},1526:(t,e,r)=>{r.d(e,{J:()=>n});function n(t){return Object.keys(t).reduce((e,r)=>(void 0!==t[r]&&(e[r]=t[r]),e),{})}},1750:(t,e,r)=>{function n(t){return t&&"object"==typeof t&&!Array.isArray(t)}r.d(e,{$:()=>function t(e,r){let o={...e};return n(e)&&n(r)&&Object.keys(r).forEach(a=>{n(r[a])&&a in e?o[a]=t(o[a],r[a]):o[a]=r[a]}),o}})},2596:(t,e,r)=>{r.d(e,{A:()=>n});let n=function(){for(var t,e,r=0,n="",o=arguments.length;r<o;r++)(t=arguments[r])&&(e=function t(e){var r,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(r=0;r<a;r++)e[r]&&(n=t(e[r]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}(t))&&(n&&(n+=" "),n+=e);return n}},3131:(t,e,r)=>{r.d(e,{nW:()=>f,xd:()=>d});var n=r(5155),o=r(2115),a=r(4555),c=r(1750);function i(t){return!(t<0)&&!(t>9)&&parseInt(t.toString(),10)===t}function l(t){if(!(t.primaryColor in t.colors))throw Error("[@mantine/core] MantineProvider: Invalid theme.primaryColor, it accepts only key of theme.colors, learn more – https://mantine.dev/theming/colors/#primary-color");if("object"==typeof t.primaryShade&&(!i(t.primaryShade.dark)||!i(t.primaryShade.light))||"number"==typeof t.primaryShade&&!i(t.primaryShade))throw Error("[@mantine/core] MantineProvider: Invalid theme.primaryShade, it accepts only 0-9 integers or an object { light: 0-9, dark: 0-9 }")}let s=(0,o.createContext)(null);function d(){let t=(0,o.useContext)(s);if(!t)throw Error("@mantine/core: MantineProvider was not found in component tree, make sure you have it in your app");return t}function f(t){let{theme:e,children:r,inherit:i=!0}=t,d=(0,o.useContext)(s)||a.S,f=(0,o.useMemo)(()=>(function(t,e){var r;if(!e)return l(t),t;let n=(0,c.$)(t,e);return!e.fontFamily||(null==(r=e.headings)?void 0:r.fontFamily)||(n.headings.fontFamily=e.fontFamily),l(n),n})(i?d:a.S,e),[e,d,i]);return(0,n.jsx)(s.Provider,{value:f,children:r})}f.displayName="@mantine/core/MantineThemeProvider"},3656:(t,e,r)=>{r.d(e,{A$:()=>o,AI:()=>i,FI:()=>d,If:()=>s,NL:()=>f,OY:()=>c,WV:()=>l,m6:()=>p});var n=r(2115);let o=(0,n.createContext)(null);function a(){let t=(0,n.useContext)(o);if(!t)throw Error("[@mantine/core] MantineProvider was not found in tree");return t}function c(){return a().cssVariablesResolver}function i(){return a().classNamesPrefix}function l(){return a().getStyleNonce}function s(){return a().withStaticClasses}function d(){return a().headless}function f(){var t;return null==(t=a().stylesTransform)?void 0:t.sx}function p(){var t;return null==(t=a().stylesTransform)?void 0:t.styles}},3664:(t,e,r)=>{r.d(e,{Y:()=>a});var n=r(1526);r(2115),r(5155);var o=r(3131);function a(t,e,r){var a;let c=(0,o.xd)(),i=null==(a=c.components[t])?void 0:a.defaultProps,l="function"==typeof i?i(c):i;return{...e,...l,...(0,n.J)(r)}}},4092:(t,e,r)=>{r.d(e,{N:()=>n});function n(t){let{theme:e,styles:r,props:n,stylesCtx:o}=t;return(Array.isArray(r)?r:[r]).reduce((t,r)=>"function"==typeof r?{...t,...r(e,n,o)}:{...t,...r},{})}},4555:(t,e,r)=>{r.d(e,{S:()=>d});var n=r(5903);r(2115),r(5155);var o=r(8792);function a(t,e){if(t.startsWith("var("))return"color-mix(in srgb, ".concat(t,", black ").concat(100*e,"%)");let{r,g:n,b:a,a:c}=(0,o.K)(t),i=1-e,l=t=>Math.round(t*i);return"rgba(".concat(l(r),", ").concat(l(n),", ").concat(l(a),", ").concat(c,")")}var c=r(8512),i=r(8271),l=r(714);let s="-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji",d={scale:1,fontSmoothing:!0,focusRing:"auto",white:"#fff",black:"#000",colors:{dark:["#C9C9C9","#b8b8b8","#828282","#696969","#424242","#3b3b3b","#2e2e2e","#242424","#1f1f1f","#141414"],gray:["#f8f9fa","#f1f3f5","#e9ecef","#dee2e6","#ced4da","#adb5bd","#868e96","#495057","#343a40","#212529"],red:["#fff5f5","#ffe3e3","#ffc9c9","#ffa8a8","#ff8787","#ff6b6b","#fa5252","#f03e3e","#e03131","#c92a2a"],pink:["#fff0f6","#ffdeeb","#fcc2d7","#faa2c1","#f783ac","#f06595","#e64980","#d6336c","#c2255c","#a61e4d"],grape:["#f8f0fc","#f3d9fa","#eebefa","#e599f7","#da77f2","#cc5de8","#be4bdb","#ae3ec9","#9c36b5","#862e9c"],violet:["#f3f0ff","#e5dbff","#d0bfff","#b197fc","#9775fa","#845ef7","#7950f2","#7048e8","#6741d9","#5f3dc4"],indigo:["#edf2ff","#dbe4ff","#bac8ff","#91a7ff","#748ffc","#5c7cfa","#4c6ef5","#4263eb","#3b5bdb","#364fc7"],blue:["#e7f5ff","#d0ebff","#a5d8ff","#74c0fc","#4dabf7","#339af0","#228be6","#1c7ed6","#1971c2","#1864ab"],cyan:["#e3fafc","#c5f6fa","#99e9f2","#66d9e8","#3bc9db","#22b8cf","#15aabf","#1098ad","#0c8599","#0b7285"],teal:["#e6fcf5","#c3fae8","#96f2d7","#63e6be","#38d9a9","#20c997","#12b886","#0ca678","#099268","#087f5b"],green:["#ebfbee","#d3f9d8","#b2f2bb","#8ce99a","#69db7c","#51cf66","#40c057","#37b24d","#2f9e44","#2b8a3e"],lime:["#f4fce3","#e9fac8","#d8f5a2","#c0eb75","#a9e34b","#94d82d","#82c91e","#74b816","#66a80f","#5c940d"],yellow:["#fff9db","#fff3bf","#ffec99","#ffe066","#ffd43b","#fcc419","#fab005","#f59f00","#f08c00","#e67700"],orange:["#fff4e6","#ffe8cc","#ffd8a8","#ffc078","#ffa94d","#ff922b","#fd7e14","#f76707","#e8590c","#d9480f"]},primaryShade:{light:6,dark:8},primaryColor:"blue",variantColorResolver:t=>{let{color:e,theme:r,variant:o,gradient:s,autoContrast:d}=t,f=(0,i.g)({color:e,theme:r}),p="boolean"==typeof d?d:r.autoContrast;if("filled"===o){let t=p&&f.isLight?"var(--mantine-color-black)":"var(--mantine-color-white)";return f.isThemeColor?void 0===f.shade?{background:"var(--mantine-color-".concat(e,"-filled)"),hover:"var(--mantine-color-".concat(e,"-filled-hover)"),color:t,border:"".concat((0,n.D)(1)," solid transparent")}:{background:"var(--mantine-color-".concat(f.color,"-").concat(f.shade,")"),hover:"var(--mantine-color-".concat(f.color,"-").concat(9===f.shade?8:f.shade+1,")"),color:t,border:"".concat((0,n.D)(1)," solid transparent")}:{background:e,hover:a(e,.1),color:t,border:"".concat((0,n.D)(1)," solid transparent")}}if("light"===o){if(f.isThemeColor){if(void 0===f.shade)return{background:"var(--mantine-color-".concat(e,"-light)"),hover:"var(--mantine-color-".concat(e,"-light-hover)"),color:"var(--mantine-color-".concat(e,"-light-color)"),border:"".concat((0,n.D)(1)," solid transparent")};let t=r.colors[f.color][f.shade];return{background:(0,l.B)(t,.1),hover:(0,l.B)(t,.12),color:"var(--mantine-color-".concat(f.color,"-").concat(Math.min(f.shade,6),")"),border:"".concat((0,n.D)(1)," solid transparent")}}return{background:(0,l.B)(e,.1),hover:(0,l.B)(e,.12),color:e,border:"".concat((0,n.D)(1)," solid transparent")}}if("outline"===o)return f.isThemeColor?void 0===f.shade?{background:"transparent",hover:"var(--mantine-color-".concat(e,"-outline-hover)"),color:"var(--mantine-color-".concat(e,"-outline)"),border:"".concat((0,n.D)(1)," solid var(--mantine-color-").concat(e,"-outline)")}:{background:"transparent",hover:(0,l.B)(r.colors[f.color][f.shade],.05),color:"var(--mantine-color-".concat(f.color,"-").concat(f.shade,")"),border:"".concat((0,n.D)(1)," solid var(--mantine-color-").concat(f.color,"-").concat(f.shade,")")}:{background:"transparent",hover:(0,l.B)(e,.05),color:e,border:"".concat((0,n.D)(1)," solid ").concat(e)};if("subtle"===o){if(f.isThemeColor){if(void 0===f.shade)return{background:"transparent",hover:"var(--mantine-color-".concat(e,"-light-hover)"),color:"var(--mantine-color-".concat(e,"-light-color)"),border:"".concat((0,n.D)(1)," solid transparent")};let t=r.colors[f.color][f.shade];return{background:"transparent",hover:(0,l.B)(t,.12),color:"var(--mantine-color-".concat(f.color,"-").concat(Math.min(f.shade,6),")"),border:"".concat((0,n.D)(1)," solid transparent")}}return{background:"transparent",hover:(0,l.B)(e,.12),color:e,border:"".concat((0,n.D)(1)," solid transparent")}}if("transparent"===o)return f.isThemeColor?void 0===f.shade?{background:"transparent",hover:"transparent",color:"var(--mantine-color-".concat(e,"-light-color)"),border:"".concat((0,n.D)(1)," solid transparent")}:{background:"transparent",hover:"transparent",color:"var(--mantine-color-".concat(f.color,"-").concat(Math.min(f.shade,6),")"),border:"".concat((0,n.D)(1)," solid transparent")}:{background:"transparent",hover:"transparent",color:e,border:"".concat((0,n.D)(1)," solid transparent")};if("white"===o)return f.isThemeColor?void 0===f.shade?{background:"var(--mantine-color-white)",hover:a(r.white,.01),color:"var(--mantine-color-".concat(e,"-filled)"),border:"".concat((0,n.D)(1)," solid transparent")}:{background:"var(--mantine-color-white)",hover:a(r.white,.01),color:"var(--mantine-color-".concat(f.color,"-").concat(f.shade,")"),border:"".concat((0,n.D)(1)," solid transparent")}:{background:"var(--mantine-color-white)",hover:a(r.white,.01),color:e,border:"".concat((0,n.D)(1)," solid transparent")};return"gradient"===o?{background:(0,c.v)(s,r),hover:(0,c.v)(s,r),color:"var(--mantine-color-white)",border:"none"}:"default"===o?{background:"var(--mantine-color-default)",hover:"var(--mantine-color-default-hover)",color:"var(--mantine-color-default-color)",border:"".concat((0,n.D)(1)," solid var(--mantine-color-default-border)")}:{}},autoContrast:!1,luminanceThreshold:.3,fontFamily:s,fontFamilyMonospace:"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace",respectReducedMotion:!1,cursorType:"default",defaultGradient:{from:"blue",to:"cyan",deg:45},defaultRadius:"sm",activeClassName:"mantine-active",focusClassName:"",headings:{fontFamily:s,fontWeight:"700",textWrap:"wrap",sizes:{h1:{fontSize:(0,n.D)(34),lineHeight:"1.3"},h2:{fontSize:(0,n.D)(26),lineHeight:"1.35"},h3:{fontSize:(0,n.D)(22),lineHeight:"1.4"},h4:{fontSize:(0,n.D)(18),lineHeight:"1.45"},h5:{fontSize:(0,n.D)(16),lineHeight:"1.5"},h6:{fontSize:(0,n.D)(14),lineHeight:"1.5"}}},fontSizes:{xs:(0,n.D)(12),sm:(0,n.D)(14),md:(0,n.D)(16),lg:(0,n.D)(18),xl:(0,n.D)(20)},lineHeights:{xs:"1.4",sm:"1.45",md:"1.55",lg:"1.6",xl:"1.65"},radius:{xs:(0,n.D)(2),sm:(0,n.D)(4),md:(0,n.D)(8),lg:(0,n.D)(16),xl:(0,n.D)(32)},spacing:{xs:(0,n.D)(10),sm:(0,n.D)(12),md:(0,n.D)(16),lg:(0,n.D)(20),xl:(0,n.D)(32)},breakpoints:{xs:"36em",sm:"48em",md:"62em",lg:"75em",xl:"88em"},shadows:{xs:"0 ".concat((0,n.D)(1)," ").concat((0,n.D)(3)," rgba(0, 0, 0, 0.05), 0 ").concat((0,n.D)(1)," ").concat((0,n.D)(2)," rgba(0, 0, 0, 0.1)"),sm:"0 ".concat((0,n.D)(1)," ").concat((0,n.D)(3)," rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ").concat((0,n.D)(10)," ").concat((0,n.D)(15)," ").concat((0,n.D)(-5),", rgba(0, 0, 0, 0.04) 0 ").concat((0,n.D)(7)," ").concat((0,n.D)(7)," ").concat((0,n.D)(-5)),md:"0 ".concat((0,n.D)(1)," ").concat((0,n.D)(3)," rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ").concat((0,n.D)(20)," ").concat((0,n.D)(25)," ").concat((0,n.D)(-5),", rgba(0, 0, 0, 0.04) 0 ").concat((0,n.D)(10)," ").concat((0,n.D)(10)," ").concat((0,n.D)(-5)),lg:"0 ".concat((0,n.D)(1)," ").concat((0,n.D)(3)," rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ").concat((0,n.D)(28)," ").concat((0,n.D)(23)," ").concat((0,n.D)(-7),", rgba(0, 0, 0, 0.04) 0 ").concat((0,n.D)(12)," ").concat((0,n.D)(12)," ").concat((0,n.D)(-7)),xl:"0 ".concat((0,n.D)(1)," ").concat((0,n.D)(3)," rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ").concat((0,n.D)(36)," ").concat((0,n.D)(28)," ").concat((0,n.D)(-7),", rgba(0, 0, 0, 0.04) 0 ").concat((0,n.D)(17)," ").concat((0,n.D)(17)," ").concat((0,n.D)(-7))},other:{},components:{}}},5903:(t,e,r)=>{function n(t){return"0rem"===t?"0rem":"calc(".concat(t," * var(--mantine-scale))")}function o(t){let{shouldScale:e=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function r(o){if(0===o||"0"===o)return"0".concat(t);if("number"==typeof o){let r="".concat(o/16).concat(t);return e?n(r):r}if("string"==typeof o){if(""===o||o.startsWith("calc(")||o.startsWith("clamp(")||o.includes("rgba("))return o;if(o.includes(","))return o.split(",").map(t=>r(t)).join(",");if(o.includes(" "))return o.split(" ").map(t=>r(t)).join(" ");if(o.includes(t))return e?n(o):o;let a=o.replace("px","");if(!Number.isNaN(Number(a))){let r="".concat(Number(a)/16).concat(t);return e?n(r):r}}return o}}r.d(e,{D:()=>a,em:()=>c});let a=o("rem",{shouldScale:!0}),c=o("em")},6325:(t,e,r)=>{r.d(e,{X:()=>p});var n=r(9224);r(2115),r(5155);var o=r(5903),a=r(8271);function c(t,e){let r=(0,a.g)({color:t,theme:e});return"dimmed"===r.color?"var(--mantine-color-dimmed)":"bright"===r.color?"var(--mantine-color-bright)":r.variable?"var(".concat(r.variable,")"):r.color}let i={text:"var(--mantine-font-family)",mono:"var(--mantine-font-family-monospace)",monospace:"var(--mantine-font-family-monospace)",heading:"var(--mantine-font-family-headings)",headings:"var(--mantine-font-family-headings)"},l=["h1","h2","h3","h4","h5","h6"],s=["h1","h2","h3","h4","h5","h6"],d={color:c,textColor:function(t,e){let r=(0,a.g)({color:t,theme:e});return r.isThemeColor&&void 0===r.shade?"var(--mantine-color-".concat(r.color,"-text)"):c(t,e)},fontSize:function(t,e){return"string"==typeof t&&t in e.fontSizes?"var(--mantine-font-size-".concat(t,")"):"string"==typeof t&&l.includes(t)?"var(--mantine-".concat(t,"-font-size)"):"number"==typeof t||"string"==typeof t?(0,o.D)(t):t},spacing:function(t,e){if("number"==typeof t)return(0,o.D)(t);if("string"==typeof t){let r=t.replace("-","");if(!(r in e.spacing))return(0,o.D)(t);let n="--mantine-spacing-".concat(r);return t.startsWith("-")?"calc(var(".concat(n,") * -1)"):"var(".concat(n,")")}return t},identity:function(t){return t},size:function(t){return"number"==typeof t?(0,o.D)(t):t},lineHeight:function(t,e){return"string"==typeof t&&t in e.lineHeights?"var(--mantine-line-height-".concat(t,")"):"string"==typeof t&&s.includes(t)?"var(--mantine-".concat(t,"-line-height)"):t},fontFamily:function(t){return"string"==typeof t&&t in i?i[t]:t},border:function(t,e){if("number"==typeof t)return(0,o.D)(t);if("string"==typeof t){let[r,n,...a]=t.split(" ").filter(t=>""!==t.trim()),i="".concat((0,o.D)(r));return n&&(i+=" ".concat(n)),a.length>0&&(i+=" ".concat(c(a.join(" "),e))),i.trim()}return t}};function f(t){return t.replace("(min-width: ","").replace("em)","")}function p(t){let{styleProps:e,data:r,theme:o}=t;return function(t){let{media:e,...r}=t,n=Object.keys(e).sort((t,e)=>Number(f(t))-Number(f(e))).map(t=>({query:t,styles:e[t]}));return{...r,media:n}}((0,n.H)(e).reduce((t,a)=>{var c,i;if("hiddenFrom"===a||"visibleFrom"===a||"sx"===a)return t;let l=r[a],s=Array.isArray(l.property)?l.property:[l.property],f="object"==typeof(i=e[a])&&null!==i?"base"in i?i.base:void 0:i;if(!function(t){if("object"!=typeof t||null===t)return!1;let e=Object.keys(t);return 1!==e.length||"base"!==e[0]}(e[a]))return s.forEach(e=>{t.inlineStyles[e]=d[l.type](f,o)}),t;t.hasResponsiveStyles=!0;let p="object"==typeof(c=e[a])&&null!==c?(0,n.H)(c).filter(t=>"base"!==t):[];return s.forEach(r=>{f&&(t.styles[r]=d[l.type](f,o)),p.forEach(n=>{var c;let i="(min-width: ".concat(o.breakpoints[n],")");t.media[i]={...t.media[i],[r]:d[l.type]((c=e[a],"object"==typeof c&&null!==c&&n in c?c[n]:c),o)}})}),t},{hasResponsiveStyles:!1,styles:{},inlineStyles:{},media:{}}))}},6390:(t,e,r)=>{r.d(e,{C:()=>o});var n=r(2115);function o(){let t=(0,n.useId)().replace(/:/g,"");return"__m__-".concat(t)}},6960:(t,e,r)=>{r.d(e,{D_:()=>a,P9:()=>c});var n=r(5155),o=r(2115);function a(t){return t}function c(t){let e=(0,o.forwardRef)(t);return e.extend=a,e.withProps=t=>{let r=(0,o.forwardRef)((r,o)=>(0,n.jsx)(e,{...t,...r,ref:o}));return r.extend=e.extend,r.displayName="WithProps(".concat(e.displayName,")"),r},e}},8271:(t,e,r)=>{r.d(e,{g:()=>i});var n=r(128),o=r(8792);function a(t){return t<=.03928?t/12.92:((t+.055)/1.055)**2.4}function c(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.179;return!t.startsWith("var(")&&function(t){if(t.startsWith("oklch("))return(function(t){let e=t.match(/oklch\((.*?)%\s/);return e?parseFloat(e[1]):null}(t)||0)/100;let{r:e,g:r,b:n}=(0,o.K)(t),c=a(e/255);return .2126*c+.7152*a(r/255)+.0722*a(n/255)}(t)>e}function i(t){let{color:e,theme:r,colorScheme:o}=t;if("string"!=typeof e)throw Error("[@mantine/core] Failed to parse color. Expected color to be a string, instead got ".concat(typeof e));if("bright"===e)return{color:e,value:"dark"===o?r.white:r.black,shade:void 0,isThemeColor:!1,isLight:c("dark"===o?r.white:r.black,r.luminanceThreshold),variable:"--mantine-color-bright"};if("dimmed"===e)return{color:e,value:"dark"===o?r.colors.dark[2]:r.colors.gray[7],shade:void 0,isThemeColor:!1,isLight:c("dark"===o?r.colors.dark[2]:r.colors.gray[6],r.luminanceThreshold),variable:"--mantine-color-dimmed"};if("white"===e||"black"===e)return{color:e,value:"white"===e?r.white:r.black,shade:void 0,isThemeColor:!1,isLight:c("white"===e?r.white:r.black,r.luminanceThreshold),variable:"--mantine-color-".concat(e)};let[a,i]=e.split("."),l=i?Number(i):void 0,s=a in r.colors;if(s){let t=void 0!==l?r.colors[a][l]:r.colors[a][(0,n.g)(r,o||"light")];return{color:a,value:t,shade:l,isThemeColor:s,isLight:c(t,r.luminanceThreshold),variable:i?"--mantine-color-".concat(a,"-").concat(l):"--mantine-color-".concat(a,"-filled")}}return{color:e,value:e,isThemeColor:s,isLight:c(e,r.luminanceThreshold),shade:l,variable:void 0}}},8512:(t,e,r)=>{r.d(e,{v:()=>o});var n=r(1180);function o(t,e){let r={from:(null==t?void 0:t.from)||e.defaultGradient.from,to:(null==t?void 0:t.to)||e.defaultGradient.to,deg:(null==t?void 0:t.deg)||e.defaultGradient.deg||0},o=(0,n.r)(r.from,e),a=(0,n.r)(r.to,e);return"linear-gradient(".concat(r.deg,"deg, ").concat(o," 0%, ").concat(a," 100%)")}},8570:(t,e,r)=>{r.d(e,{K:()=>i});var n=r(5155);r(2115);var o=r(3656),a=r(9224);function c(t){return(0,a.H)(t).reduce((e,r)=>void 0!==t[r]?"".concat(e).concat(r.replace(/[A-Z]/g,t=>"-".concat(t.toLowerCase())),":").concat(t[r],";"):e,"").trim()}function i(t){let e=(0,o.WV)();return(0,n.jsx)("style",{"data-mantine-styles":"inline",nonce:null==e?void 0:e(),dangerouslySetInnerHTML:{__html:function(t){let{selector:e,styles:r,media:n,container:o}=t,a=r?c(r):"",i=Array.isArray(n)?n.map(t=>"@media".concat(t.query,"{").concat(e,"{").concat(c(t.styles),"}}")):[],l=Array.isArray(o)?o.map(t=>"@container ".concat(t.query,"{").concat(e,"{").concat(c(t.styles),"}}")):[];return"".concat(a?"".concat(e,"{").concat(a,"}"):"").concat(i.join("")).concat(l.join("")).trim()}(t)}})}},8772:(t,e,r)=>{r.d(e,{t:()=>n});function n(t){if("number"==typeof t)return!0;if("string"==typeof t){if(t.startsWith("calc(")||t.startsWith("var(")||t.includes(" ")&&""!==t.trim())return!0;let e=/^[+-]?[0-9]+(\.[0-9]+)?(px|em|rem|ex|ch|lh|rlh|vw|vh|vmin|vmax|vb|vi|svw|svh|lvw|lvh|dvw|dvh|cm|mm|in|pt|pc|q|cqw|cqh|cqi|cqb|cqmin|cqmax|%)?$/;return t.trim().split(/\s+/).every(t=>e.test(t))}return!1}},8792:(t,e,r)=>{r.d(e,{K:()=>n});function n(t){if(/^#?([0-9A-F]{3}){1,2}([0-9A-F]{2})?$/i.test(t)){let e=t.replace("#","");if(3===e.length){let t=e.split("");e=[t[0],t[0],t[1],t[1],t[2],t[2]].join("")}if(8===e.length){let t=parseInt(e.slice(6,8),16)/255;return{r:parseInt(e.slice(0,2),16),g:parseInt(e.slice(2,4),16),b:parseInt(e.slice(4,6),16),a:t}}let r=parseInt(e,16);return{r:r>>16&255,g:r>>8&255,b:255&r,a:1}}if(t.startsWith("rgb")){let[e,r,n,o]=t.replace(/[^0-9,./]/g,"").split(/[/,]/).map(Number);return{r:e,g:r,b:n,a:o||1}}return t.startsWith("hsl")?function(t){let e,r,n,o=t.match(/^hsla?\(\s*(\d+)\s*,\s*(\d+%)\s*,\s*(\d+%)\s*(,\s*(0?\.\d+|\d+(\.\d+)?))?\s*\)$/i);if(!o)return{r:0,g:0,b:0,a:1};let a=parseInt(o[1],10),c=parseInt(o[2],10)/100,i=parseInt(o[3],10)/100,l=o[5]?parseFloat(o[5]):void 0,s=(1-Math.abs(2*i-1))*c,d=a/60,f=s*(1-Math.abs(d%2-1)),p=i-s/2;return d>=0&&d<1?(e=s,r=f,n=0):d>=1&&d<2?(e=f,r=s,n=0):d>=2&&d<3?(e=0,r=s,n=f):d>=3&&d<4?(e=0,r=f,n=s):d>=4&&d<5?(e=f,r=0,n=s):(e=s,r=0,n=f),{r:Math.round((e+p)*255),g:Math.round((r+p)*255),b:Math.round((n+p)*255),a:l||1}}(t):{r:0,g:0,b:0,a:1}}},9224:(t,e,r)=>{r.d(e,{H:()=>n});function n(t){return Object.keys(t)}},9537:(t,e,r)=>{r.d(e,{j:()=>o});var n=r(1526);function o(t){let{m:e,mx:r,my:o,mt:a,mb:c,ml:i,mr:l,me:s,ms:d,p:f,px:p,py:u,pt:m,pb:y,pl:h,pr:v,pe:g,ps:b,bd:D,bg:k,c:x,opacity:w,ff:S,fz:C,fw:A,lts:N,ta:j,lh:I,fs:z,tt:T,td:M,w:F,miw:W,maw:E,h:H,mih:R,mah:B,bgsz:P,bgp:L,bgr:_,bga:q,pos:O,top:J,left:K,bottom:$,right:G,inset:V,display:X,flex:U,hiddenFrom:Y,visibleFrom:Z,lightHidden:Q,darkHidden:tt,sx:te,...tr}=t;return{styleProps:(0,n.J)({m:e,mx:r,my:o,mt:a,mb:c,ml:i,mr:l,me:s,ms:d,p:f,px:p,py:u,pt:m,pb:y,pl:h,pr:v,pe:g,ps:b,bd:D,bg:k,c:x,opacity:w,ff:S,fz:C,fw:A,lts:N,ta:j,lh:I,fs:z,tt:T,td:M,w:F,miw:W,maw:E,h:H,mih:R,mah:B,bgsz:P,bgp:L,bgr:_,bga:q,pos:O,top:J,left:K,bottom:$,right:G,inset:V,display:X,flex:U,hiddenFrom:Y,visibleFrom:Z,lightHidden:Q,darkHidden:tt,sx:te}),rest:tr}}r(2115),r(5155)},9787:(t,e,r)=>{r.d(e,{J:()=>a});var n=r(2596);let o={};function a(t){let{theme:e,classNames:r,props:a,stylesCtx:c}=t;var i=(Array.isArray(r)?r:[r]).map(t=>"function"==typeof t?t(e,a,c):t||o);let l={};return i.forEach(t=>{Object.entries(t).forEach(t=>{let[e,r]=t;l[e]?l[e]=(0,n.A)(l[e],r):l[e]=r})}),l}}}]);