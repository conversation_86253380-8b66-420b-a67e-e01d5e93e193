(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[237],{1187:(e,t,n)=>{"use strict";function o(e){var t;let n="string"==typeof e&&e.includes("var(--mantine-scale)")?null==(t=e.match(/^calc\((.*?)\)$/))?void 0:t[1].split("*")[0].trim():e;return"number"==typeof n?n:"string"==typeof n?n.includes("calc")||n.includes("var")?n:n.includes("px")?Number(n.replace("px","")):n.includes("rem")?16*Number(n.replace("rem","")):n.includes("em")?16*Number(n.replace("em","")):Number(n):NaN}n.d(t,{px:()=>o})},1383:(e,t,n)=>{"use strict";n.d(t,{<PERSON><PERSON>Provider:()=>E});var o=n(5155);function a(e){return"auto"===e||"dark"===e||"light"===e}var r=n(3656),c=n(9224),i=n(1187),l=n(5903),s=n(2115),m=n(3131);function d(){let e=(0,m.xd)(),t=(0,r.WV)(),n=(0,c.H)(e.breakpoints).reduce((t,n)=>{let o=e.breakpoints[n].includes("px"),a=(0,i.px)(e.breakpoints[n]),r=o?"".concat(a-.1,"px"):(0,l.em)(a-.1),c=o?"".concat(a,"px"):(0,l.em)(a);return"".concat(t,"@media (max-width: ").concat(r,") {.mantine-visible-from-").concat(n," {display: none !important;}}@media (min-width: ").concat(c,") {.mantine-hidden-from-").concat(n," {display: none !important;}}")},"");return(0,o.jsx)("style",{"data-mantine-styles":"classes",nonce:null==t?void 0:t(),dangerouslySetInnerHTML:{__html:n}})}function u(e){return Object.entries(e).map(e=>{let[t,n]=e;return"".concat(t,": ").concat(n,";")}).join("")}function h(e,t){return(Array.isArray(e)?e:[e]).reduce((e,t)=>"".concat(t,"{").concat(e,"}"),t)}var f=n(1750),v=n(128),g=n(8271);function p(e,t){return function(e){let{color:t,theme:n,autoContrast:o}=e;return("boolean"==typeof o?o:n.autoContrast)&&(0,g.g)({color:t||n.primaryColor,theme:n}).isLight?"var(--mantine-color-black)":"var(--mantine-color-white)"}({color:e.colors[e.primaryColor][(0,v.g)(e,t)],theme:e,autoContrast:null})}var y=n(714);function b(e){let{theme:t,color:n,colorScheme:o,name:a=n,withColorValues:r=!0}=e;if(!t.colors[n])return{};if("light"===o){let e=(0,v.g)(t,"light"),o={["--mantine-color-".concat(a,"-text")]:"var(--mantine-color-".concat(a,"-filled)"),["--mantine-color-".concat(a,"-filled")]:"var(--mantine-color-".concat(a,"-").concat(e,")"),["--mantine-color-".concat(a,"-filled-hover")]:"var(--mantine-color-".concat(a,"-").concat(9===e?8:e+1,")"),["--mantine-color-".concat(a,"-light")]:(0,y.X)(t.colors[n][e],.1),["--mantine-color-".concat(a,"-light-hover")]:(0,y.X)(t.colors[n][e],.12),["--mantine-color-".concat(a,"-light-color")]:"var(--mantine-color-".concat(a,"-").concat(e,")"),["--mantine-color-".concat(a,"-outline")]:"var(--mantine-color-".concat(a,"-").concat(e,")"),["--mantine-color-".concat(a,"-outline-hover")]:(0,y.X)(t.colors[n][e],.05)};return r?{["--mantine-color-".concat(a,"-0")]:t.colors[n][0],["--mantine-color-".concat(a,"-1")]:t.colors[n][1],["--mantine-color-".concat(a,"-2")]:t.colors[n][2],["--mantine-color-".concat(a,"-3")]:t.colors[n][3],["--mantine-color-".concat(a,"-4")]:t.colors[n][4],["--mantine-color-".concat(a,"-5")]:t.colors[n][5],["--mantine-color-".concat(a,"-6")]:t.colors[n][6],["--mantine-color-".concat(a,"-7")]:t.colors[n][7],["--mantine-color-".concat(a,"-8")]:t.colors[n][8],["--mantine-color-".concat(a,"-9")]:t.colors[n][9],...o}:o}let c=(0,v.g)(t,"dark"),i={["--mantine-color-".concat(a,"-text")]:"var(--mantine-color-".concat(a,"-4)"),["--mantine-color-".concat(a,"-filled")]:"var(--mantine-color-".concat(a,"-").concat(c,")"),["--mantine-color-".concat(a,"-filled-hover")]:"var(--mantine-color-".concat(a,"-").concat(9===c?8:c+1,")"),["--mantine-color-".concat(a,"-light")]:(0,y.X)(t.colors[n][Math.max(0,c-2)],.15),["--mantine-color-".concat(a,"-light-hover")]:(0,y.X)(t.colors[n][Math.max(0,c-2)],.2),["--mantine-color-".concat(a,"-light-color")]:"var(--mantine-color-".concat(a,"-").concat(Math.max(c-5,0),")"),["--mantine-color-".concat(a,"-outline")]:"var(--mantine-color-".concat(a,"-").concat(Math.max(c-4,0),")"),["--mantine-color-".concat(a,"-outline-hover")]:(0,y.X)(t.colors[n][Math.max(c-4,0)],.05)};return r?{["--mantine-color-".concat(a,"-0")]:t.colors[n][0],["--mantine-color-".concat(a,"-1")]:t.colors[n][1],["--mantine-color-".concat(a,"-2")]:t.colors[n][2],["--mantine-color-".concat(a,"-3")]:t.colors[n][3],["--mantine-color-".concat(a,"-4")]:t.colors[n][4],["--mantine-color-".concat(a,"-5")]:t.colors[n][5],["--mantine-color-".concat(a,"-6")]:t.colors[n][6],["--mantine-color-".concat(a,"-7")]:t.colors[n][7],["--mantine-color-".concat(a,"-8")]:t.colors[n][8],["--mantine-color-".concat(a,"-9")]:t.colors[n][9],...i}:i}function w(e,t,n){(0,c.H)(t).forEach(o=>Object.assign(e,{["--mantine-".concat(n,"-").concat(o)]:t[o]}))}let x=e=>{let t=(0,v.g)(e,"light"),n=e.defaultRadius in e.radius?e.radius[e.defaultRadius]:(0,l.D)(e.defaultRadius),o={variables:{"--mantine-scale":e.scale.toString(),"--mantine-cursor-type":e.cursorType,"--mantine-color-scheme":"light dark","--mantine-webkit-font-smoothing":e.fontSmoothing?"antialiased":"unset","--mantine-moz-font-smoothing":e.fontSmoothing?"grayscale":"unset","--mantine-color-white":e.white,"--mantine-color-black":e.black,"--mantine-line-height":e.lineHeights.md,"--mantine-font-family":e.fontFamily,"--mantine-font-family-monospace":e.fontFamilyMonospace,"--mantine-font-family-headings":e.headings.fontFamily,"--mantine-heading-font-weight":e.headings.fontWeight,"--mantine-heading-text-wrap":e.headings.textWrap,"--mantine-radius-default":n,"--mantine-primary-color-filled":"var(--mantine-color-".concat(e.primaryColor,"-filled)"),"--mantine-primary-color-filled-hover":"var(--mantine-color-".concat(e.primaryColor,"-filled-hover)"),"--mantine-primary-color-light":"var(--mantine-color-".concat(e.primaryColor,"-light)"),"--mantine-primary-color-light-hover":"var(--mantine-color-".concat(e.primaryColor,"-light-hover)"),"--mantine-primary-color-light-color":"var(--mantine-color-".concat(e.primaryColor,"-light-color)")},light:{"--mantine-primary-color-contrast":p(e,"light"),"--mantine-color-bright":"var(--mantine-color-black)","--mantine-color-text":e.black,"--mantine-color-body":e.white,"--mantine-color-error":"var(--mantine-color-red-6)","--mantine-color-placeholder":"var(--mantine-color-gray-5)","--mantine-color-anchor":"var(--mantine-color-".concat(e.primaryColor,"-").concat(t,")"),"--mantine-color-default":"var(--mantine-color-white)","--mantine-color-default-hover":"var(--mantine-color-gray-0)","--mantine-color-default-color":"var(--mantine-color-black)","--mantine-color-default-border":"var(--mantine-color-gray-4)","--mantine-color-dimmed":"var(--mantine-color-gray-6)"},dark:{"--mantine-primary-color-contrast":p(e,"dark"),"--mantine-color-bright":"var(--mantine-color-white)","--mantine-color-text":"var(--mantine-color-dark-0)","--mantine-color-body":"var(--mantine-color-dark-7)","--mantine-color-error":"var(--mantine-color-red-8)","--mantine-color-placeholder":"var(--mantine-color-dark-3)","--mantine-color-anchor":"var(--mantine-color-".concat(e.primaryColor,"-4)"),"--mantine-color-default":"var(--mantine-color-dark-6)","--mantine-color-default-hover":"var(--mantine-color-dark-5)","--mantine-color-default-color":"var(--mantine-color-white)","--mantine-color-default-border":"var(--mantine-color-dark-4)","--mantine-color-dimmed":"var(--mantine-color-dark-2)"}};w(o.variables,e.breakpoints,"breakpoint"),w(o.variables,e.spacing,"spacing"),w(o.variables,e.fontSizes,"font-size"),w(o.variables,e.lineHeights,"line-height"),w(o.variables,e.shadows,"shadow"),w(o.variables,e.radius,"radius"),e.colors[e.primaryColor].forEach((t,n)=>{o.variables["--mantine-primary-color-".concat(n)]="var(--mantine-color-".concat(e.primaryColor,"-").concat(n,")")}),(0,c.H)(e.colors).forEach(t=>{let n=e.colors[t];if(function(e){return!!e&&"object"==typeof e&&"mantine-virtual-color"in e}(n)){Object.assign(o.light,b({theme:e,name:n.name,color:n.light,colorScheme:"light",withColorValues:!0})),Object.assign(o.dark,b({theme:e,name:n.name,color:n.dark,colorScheme:"dark",withColorValues:!0}));return}n.forEach((e,n)=>{o.variables["--mantine-color-".concat(t,"-").concat(n)]=e}),Object.assign(o.light,b({theme:e,color:t,colorScheme:"light",withColorValues:!1})),Object.assign(o.dark,b({theme:e,color:t,colorScheme:"dark",withColorValues:!1}))});let a=e.headings.sizes;return(0,c.H)(a).forEach(t=>{o.variables["--mantine-".concat(t,"-font-size")]=a[t].fontSize,o.variables["--mantine-".concat(t,"-line-height")]=a[t].lineHeight,o.variables["--mantine-".concat(t,"-font-weight")]=a[t].fontWeight||e.headings.fontWeight}),o},k=x(n(4555).S);function C(e){let{cssVariablesSelector:t,deduplicateCssVariables:n}=e,a=(0,m.xd)(),i=(0,r.WV)(),l=function(e){let{theme:t,generator:n}=e,o=x(t),a=null==n?void 0:n(t);return a?(0,f.$)(o,a):o}({theme:a,generator:(0,r.OY)()}),s=":root"===t&&n,d=function(e,t){let n=u(e.variables),o=n?h(t,n):"",a=u(e.dark),r=u(e.light),c=a?":host"===t?h("".concat(t,'([data-mantine-color-scheme="dark"])'),a):h("".concat(t,'[data-mantine-color-scheme="dark"]'),a):"",i=r?":host"===t?h("".concat(t,'([data-mantine-color-scheme="light"])'),r):h("".concat(t,'[data-mantine-color-scheme="light"]'),r):"";return"".concat(o).concat(c).concat(i)}(s?function(e){let t={variables:{},light:{},dark:{}};return(0,c.H)(e.variables).forEach(n=>{k.variables[n]!==e.variables[n]&&(t.variables[n]=e.variables[n])}),(0,c.H)(e.light).forEach(n=>{k.light[n]!==e.light[n]&&(t.light[n]=e.light[n])}),(0,c.H)(e.dark).forEach(n=>{k.dark[n]!==e.dark[n]&&(t.dark[n]=e.dark[n])}),t}(l):l,t);if(d)return(0,o.jsx)("style",{"data-mantine-styles":!0,nonce:null==i?void 0:i(),dangerouslySetInnerHTML:{__html:"".concat(d).concat(s?"":"\n  ".concat(t,'[data-mantine-color-scheme="dark"] { --mantine-color-scheme: dark; }\n  ').concat(t,'[data-mantine-color-scheme="light"] { --mantine-color-scheme: light; }\n'))}});return null}C.displayName="@mantine/CssVariables";var j=n(3141);function S(e,t){var n;let o="undefined"!=typeof window&&"matchMedia"in window&&window.matchMedia("(prefers-color-scheme: dark)").matches;null==(n=t())||n.setAttribute("data-mantine-color-scheme","auto"!==e?e:o?"dark":"light")}function E(e){let{theme:t,children:n,getStyleNonce:c,withStaticClasses:i=!0,withGlobalClasses:l=!0,deduplicateCssVariables:u=!0,withCssVariables:h=!0,cssVariablesSelector:f=":root",classNamesPrefix:v="mantine",colorSchemeManager:g=function(){let e,{key:t="mantine-color-scheme-value"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{get:e=>{if("undefined"==typeof window)return e;try{let n=window.localStorage.getItem(t);return a(n)?n:e}catch(t){return e}},set:e=>{try{window.localStorage.setItem(t,e)}catch(e){console.warn("[@mantine/core] Local storage color scheme manager was unable to save color scheme.",e)}},subscribe:n=>{e=e=>{e.storageArea===window.localStorage&&e.key===t&&a(e.newValue)&&n(e.newValue)},window.addEventListener("storage",e)},unsubscribe:()=>{window.removeEventListener("storage",e)},clear:()=>{window.localStorage.removeItem(t)}}}(),defaultColorScheme:p="light",getRootElement:y=()=>document.documentElement,cssVariablesResolver:b,forceColorScheme:w,stylesTransform:x}=e,{colorScheme:k,setColorScheme:E,clearColorScheme:N}=function(e){let{manager:t,defaultColorScheme:n,getRootElement:o,forceColorScheme:a}=e,r=(0,s.useRef)(null),[c,i]=(0,s.useState)(()=>t.get(n)),l=a||c,m=(0,s.useCallback)(e=>{a||(S(e,o),i(e),t.set(e))},[t.set,l,a]),d=(0,s.useCallback)(()=>{i(n),S(n,o),t.clear()},[t.clear,n]);return(0,s.useEffect)(()=>(t.subscribe(m),t.unsubscribe),[t.subscribe,t.unsubscribe]),(0,j.o)(()=>{S(t.get(n),o)},[]),(0,s.useEffect)(()=>{var e;if(a)return S(a,o),()=>{};void 0===a&&S(c,o),"undefined"!=typeof window&&"matchMedia"in window&&(r.current=window.matchMedia("(prefers-color-scheme: dark)"));let t=e=>{"auto"===c&&S(e.matches?"dark":"light",o)};return null==(e=r.current)||e.addEventListener("change",t),()=>{var e;return null==(e=r.current)?void 0:e.removeEventListener("change",t)}},[c,a]),{colorScheme:l,setColorScheme:m,clearColorScheme:d}}({defaultColorScheme:p,forceColorScheme:w,manager:g,getRootElement:y});return!function(e){let{respectReducedMotion:t,getRootElement:n}=e;(0,j.o)(()=>{if(t){var e;null==(e=n())||e.setAttribute("data-respect-reduced-motion","true")}},[t])}({respectReducedMotion:(null==t?void 0:t.respectReducedMotion)||!1,getRootElement:y}),(0,o.jsx)(r.A$.Provider,{value:{colorScheme:k,setColorScheme:E,clearColorScheme:N,getRootElement:y,classNamesPrefix:v,getStyleNonce:c,cssVariablesResolver:b,cssVariablesSelector:f,withStaticClasses:i,stylesTransform:x},children:(0,o.jsxs)(m.nW,{theme:t,children:[h&&(0,o.jsx)(C,{cssVariablesSelector:f,deduplicateCssVariables:u}),l&&(0,o.jsx)(d,{}),n]})})}!function(){let e=console.error;console.error=function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];n.length>1&&"string"==typeof n[0]&&n[0].toLowerCase().includes("extra attributes from the server")&&"string"==typeof n[1]&&n[1].toLowerCase().includes("data-mantine-color-scheme")||e(...n)}}(),E.displayName="@mantine/core/MantineProvider"},1587:(e,t,n)=>{"use strict";n.d(t,{s:()=>g});var o=n(5155),a=n(1526);n(2115);var r=n(3131),c=n(3664),i=n(862),l=n(8570),s=n(6325),m=n(6390),d=n(311),u=n(4511);let h={gap:{type:"spacing",property:"gap"},rowGap:{type:"spacing",property:"rowGap"},columnGap:{type:"spacing",property:"columnGap"},align:{type:"identity",property:"alignItems"},justify:{type:"identity",property:"justifyContent"},wrap:{type:"identity",property:"flexWrap"},direction:{type:"identity",property:"flexDirection"}};var f={root:"m_8bffd616"};let v={},g=(0,u.v)((e,t)=>{let n=(0,c.Y)("Flex",v,e),{classNames:u,className:g,style:p,styles:y,unstyled:b,vars:w,gap:x,rowGap:k,columnGap:C,align:j,justify:S,wrap:E,direction:N,..._}=n,z=(0,i.I)({name:"Flex",classes:f,props:n,className:g,style:p,classNames:u,styles:y,unstyled:b,vars:w}),M=(0,r.xd)(),H=(0,m.C)(),I=(0,s.X)({styleProps:{gap:x,rowGap:k,columnGap:C,align:j,justify:S,wrap:E,direction:N},theme:M,data:h});return(0,o.jsxs)(o.Fragment,{children:[I.hasResponsiveStyles&&(0,o.jsx)(l.K,{selector:".".concat(H),styles:I.styles,media:I.media}),(0,o.jsx)(d.a,{ref:t,...z("root",{className:H,style:(0,a.J)(I.inlineStyles)}),..._})]})});g.classes=f,g.displayName="@mantine/core/Flex"},1590:(e,t,n)=>{"use strict";n.d(t,{Container:()=>h});var o=n(5155);n(2115);var a=n(6204),r=n(8918),c=n(3664),i=n(862),l=n(311),s=n(6960),m={root:"m_7485cace"};let d={},u=(0,r.V)((e,t)=>{let{size:n,fluid:o}=t;return{root:{"--container-size":o?void 0:(0,a.YC)(n,"container-size")}}}),h=(0,s.P9)((e,t)=>{let n=(0,c.Y)("Container",d,e),{classNames:a,className:r,style:s,styles:h,unstyled:f,vars:v,fluid:g,mod:p,...y}=n,b=(0,i.I)({name:"Container",classes:m,props:n,className:r,style:s,classNames:a,styles:h,unstyled:f,vars:v,varsResolver:u});return(0,o.jsx)(l.a,{ref:t,mod:[{fluid:g},p],...b("root"),...y})});h.classes=m,h.displayName="@mantine/core/Container"},2500:()=>{},3141:(e,t,n)=>{"use strict";n.d(t,{o:()=>a});var o=n(2115);let a="undefined"!=typeof document?o.useLayoutEffect:o.useEffect},3695:(e,t,n)=>{"use strict";n.d(t,{M:()=>m});var o=n(5155),a=n(2596);n(2115);var r=n(3664),c=n(4511),i=n(8887),l={root:"m_849cf0da"};let s={underline:"hover"},m=(0,c.v)((e,t)=>{let{underline:n,className:c,unstyled:m,mod:d,...u}=(0,r.Y)("Anchor",s,e);return(0,o.jsx)(i.E,{component:"a",ref:t,className:(0,a.A)({[l.root]:!m},c),...u,mod:[{underline:n},d],__staticSelector:"Anchor",unstyled:m})});m.classes=l,m.displayName="@mantine/core/Anchor"},3751:(e,t,n)=>{"use strict";n.d(t,{h:()=>v});var o=n(5155);n(2115);var a=n(8918),r=n(3664),c=n(862),i=n(311),l=n(6960),s=n(5903);let m=["h1","h2","h3","h4","h5","h6"],d=["xs","sm","md","lg","xl"];var u={root:"m_8a5d1357"};let h={order:1},f=(0,a.V)((e,t)=>{let{order:n,size:o,lineClamp:a,textWrap:r}=t,c=function(e,t){let n=void 0!==t?t:"h".concat(e);return m.includes(n)?{fontSize:"var(--mantine-".concat(n,"-font-size)"),fontWeight:"var(--mantine-".concat(n,"-font-weight)"),lineHeight:"var(--mantine-".concat(n,"-line-height)")}:d.includes(n)?{fontSize:"var(--mantine-font-size-".concat(n,")"),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}:{fontSize:(0,s.D)(n),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}}(n,o);return{root:{"--title-fw":c.fontWeight,"--title-lh":c.lineHeight,"--title-fz":c.fontSize,"--title-line-clamp":"number"==typeof a?a.toString():void 0,"--title-text-wrap":r}}}),v=(0,l.P9)((e,t)=>{let n=(0,r.Y)("Title",h,e),{classNames:a,className:l,style:s,styles:m,unstyled:d,order:v,vars:g,size:p,variant:y,lineClamp:b,textWrap:w,mod:x,...k}=n,C=(0,c.I)({name:"Title",props:n,classes:u,className:l,style:s,classNames:a,styles:m,unstyled:d,vars:g,varsResolver:f});return[1,2,3,4,5,6].includes(v)?(0,o.jsx)(i.a,{...C("root"),component:"h".concat(v),variant:y,ref:t,mod:[{order:v,"data-line-clamp":"number"==typeof b},x],size:p,...k}):null});v.classes=u,v.displayName="@mantine/core/Title"},4511:(e,t,n)=>{"use strict";n.d(t,{v:()=>c});var o=n(5155),a=n(2115),r=n(6960);function c(e){let t=(0,a.forwardRef)(e);return t.withProps=e=>{let n=(0,a.forwardRef)((n,a)=>(0,o.jsx)(t,{...e,...n,ref:a}));return n.extend=t.extend,n.displayName="WithProps(".concat(t.displayName,")"),n},t.extend=r.D_,t}},6204:(e,t,n)=>{"use strict";n.d(t,{GY:()=>c,YC:()=>r,dh:()=>m,ks:()=>s,nJ:()=>i,ny:()=>l});var o=n(8772),a=n(5903);function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"size",n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(void 0!==e)return(0,o.t)(e)?n?(0,a.D)(e):e:"var(--".concat(t,"-").concat(e,")")}function c(e){return r(e,"mantine-spacing")}function i(e){return void 0===e?"var(--mantine-radius-default)":r(e,"mantine-radius")}function l(e){return r(e,"mantine-font-size")}function s(e){return r(e,"mantine-line-height",!1)}function m(e){if(e)return r(e,"mantine-shadow",!1)}},8141:(e,t,n)=>{"use strict";n.d(t,{o:()=>m});var o=n(5155);n(2115);var a=n(3664),r=n(862),c=n(311),i=n(4511),l={root:"m_4451eb3a"};let s={},m=(0,i.v)((e,t)=>{let n=(0,a.Y)("Center",s,e),{classNames:i,className:m,style:d,styles:u,unstyled:h,vars:f,inline:v,mod:g,...p}=n,y=(0,r.I)({name:"Center",props:n,classes:l,className:m,style:d,classNames:i,styles:u,unstyled:h,vars:f});return(0,o.jsx)(c.a,{ref:t,mod:[{inline:v},g],...y("root"),...p})});m.classes=l,m.displayName="@mantine/core/Center"},8887:(e,t,n)=>{"use strict";n.d(t,{E:()=>v});var o=n(5155);n(2115);var a=n(6204),r=n(8918),c=n(1180),i=n(8512),l=n(3664),s=n(862),m=n(311),d=n(4511),u={root:"m_b6d8b162"};let h={inherit:!1},f=(0,r.V)((e,t)=>{let{variant:n,lineClamp:o,gradient:r,size:l,color:s}=t;return{root:{"--text-fz":(0,a.ny)(l),"--text-lh":(0,a.ks)(l),"--text-gradient":"gradient"===n?(0,i.v)(r,e):void 0,"--text-line-clamp":"number"==typeof o?o.toString():void 0,"--text-color":s?(0,c.r)(s,e):void 0}}}),v=(0,d.v)((e,t)=>{let n=(0,l.Y)("Text",h,e),{lineClamp:a,truncate:r,inline:c,inherit:i,gradient:d,span:v,__staticSelector:g,vars:p,className:y,style:b,classNames:w,styles:x,unstyled:k,variant:C,mod:j,size:S,...E}=n,N=(0,s.I)({name:["Text",g],props:n,classes:u,className:y,style:b,classNames:w,styles:x,unstyled:k,vars:p,varsResolver:f});return(0,o.jsx)(m.a,{...N("root",{focusable:!0}),ref:t,component:v?"span":"p",variant:C,mod:[{"data-truncate":function(e){return"start"===e?"start":"end"===e||e?"end":void 0}(r),"data-line-clamp":"number"==typeof a,"data-inline":c,"data-inherit":i},j],size:S,...E})});v.classes=u,v.displayName="@mantine/core/Text"},8918:(e,t,n)=>{"use strict";function o(e){return e}n.d(t,{V:()=>o})},9077:(e,t,n)=>{"use strict";n.d(t,{_:()=>f});var o=n(5155),a=n(2115),r=n(6204),c=n(8918),i=n(3664),l=n(862),s=n(311),m=n(4511),d={root:"m_9e117634"};let u={},h=(0,c.V)((e,t)=>{let{radius:n,fit:o}=t;return{root:{"--image-radius":void 0===n?void 0:(0,r.nJ)(n),"--image-object-fit":o}}}),f=(0,m.v)((e,t)=>{let n=(0,i.Y)("Image",u,e),{classNames:r,className:c,style:m,styles:f,unstyled:v,vars:g,onError:p,src:y,radius:b,fit:w,fallbackSrc:x,mod:k,...C}=n,[j,S]=(0,a.useState)(!y);(0,a.useEffect)(()=>S(!y),[y]);let E=(0,l.I)({name:"Image",classes:d,props:n,className:c,style:m,classNames:r,styles:f,unstyled:v,vars:g,varsResolver:h});return j&&x?(0,o.jsx)(s.a,{component:"img",ref:t,src:x,...E("root"),onError:p,mod:["fallback",k],...C}):(0,o.jsx)(s.a,{component:"img",ref:t,...E("root"),src:y,onError:e=>{null==p||p(e),S(!0)},mod:k,...C})});f.classes=d,f.displayName="@mantine/core/Image"}}]);