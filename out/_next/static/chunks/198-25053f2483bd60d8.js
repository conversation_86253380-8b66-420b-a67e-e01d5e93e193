(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[198],{54:(e,t,r)=>{"use strict";r.d(t,{A:()=>ep});var n=r(5155),o=r(2115),i=r(7613),a=r(3141),s=r(4237),l=r(6204),u=r(8918),c=r(6028),d=r(862),f=r(3664),h=r(6960),p=r(3191),g=r(311),m=r(5903),v=r(4511),y={root:"m_de3d2490",colorOverlay:"m_862f3d1b",shadowOverlay:"m_98ae7f22",alphaOverlay:"m_95709ac0",childrenOverlay:"m_93e74e3"};let b={withShadow:!0},w=(0,u.V)((e,t)=>{let{radius:r,size:n}=t;return{root:{"--cs-radius":void 0===r?void 0:(0,l.nJ)(r),"--cs-size":(0,m.D)(n)}}}),x=(0,v.v)((e,t)=>{let r=(0,f.Y)("ColorSwatch",b,e),{classNames:o,className:i,style:a,styles:s,unstyled:l,vars:u,color:c,size:h,radius:p,withShadow:m,children:v,variant:x,...C}=(0,f.Y)("ColorSwatch",b,r),S=(0,d.I)({name:"ColorSwatch",props:r,classes:y,className:i,style:a,classNames:o,styles:s,unstyled:l,vars:u,varsResolver:w});return(0,n.jsxs)(g.a,{ref:t,variant:x,size:h,...S("root",{focusable:!0}),...C,children:[(0,n.jsx)("span",{...S("alphaOverlay")}),m&&(0,n.jsx)("span",{...S("shadowOverlay")}),(0,n.jsx)("span",{...S("colorOverlay",{style:{backgroundColor:c}})}),(0,n.jsx)("span",{...S("childrenOverlay"),children:v})]})});function C(e,t,r){return void 0===t&&void 0===r?e:void 0!==t&&void 0===r?Math.max(e,t):void 0===t&&void 0!==r?Math.min(e,r):Math.min(Math.max(e,t),r)}function S(e){return{x:C(e.x,0,1),y:C(e.y,0,1)}}function _(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=(0,o.useRef)(null),i=(0,o.useRef)(!1),a=(0,o.useRef)(!1),s=(0,o.useRef)(0),[l,u]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{i.current=!0},[]),(0,o.useEffect)(()=>{var o,l;let c=t=>{let{x:o,y:a}=t;cancelAnimationFrame(s.current),s.current=requestAnimationFrame(()=>{if(i.current&&n.current){n.current.style.userSelect="none";let t=n.current.getBoundingClientRect();if(t.width&&t.height){let n=C((o-t.left)/t.width,0,1);e({x:"ltr"===r?n:1-n,y:C((a-t.top)/t.height,0,1)})}}})},d=()=>{!a.current&&i.current&&(a.current=!0,"function"==typeof(null==t?void 0:t.onScrubStart)&&t.onScrubStart(),u(!0),document.addEventListener("mousemove",p),document.addEventListener("mouseup",f),document.addEventListener("touchmove",m),document.addEventListener("touchend",f))},f=()=>{a.current&&i.current&&(a.current=!1,u(!1),document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",f),document.removeEventListener("touchmove",m),document.removeEventListener("touchend",f),setTimeout(()=>{"function"==typeof(null==t?void 0:t.onScrubEnd)&&t.onScrubEnd()},0))},h=e=>{d(),e.preventDefault(),p(e)},p=e=>c({x:e.clientX,y:e.clientY}),g=e=>{e.cancelable&&e.preventDefault(),d(),m(e)},m=e=>{e.cancelable&&e.preventDefault(),c({x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY})};return null==(o=n.current)||o.addEventListener("mousedown",h),null==(l=n.current)||l.addEventListener("touchstart",g,{passive:!1}),()=>{n.current&&(n.current.removeEventListener("mousedown",h),n.current.removeEventListener("touchstart",g))}},[r,e]),{ref:n,active:l}}x.classes=y,x.displayName="@mantine/core/ColorSwatch";var A=r(8551),E=r(3131);let[j,k]=(0,r(9830).e)(null),O=(0,o.forwardRef)((e,t)=>{let{position:r,...o}=e;return(0,n.jsx)(g.a,{ref:t,__vars:{"--thumb-y-offset":"".concat(100*r.y,"%"),"--thumb-x-offset":"".concat(100*r.x,"%")},...o})});O.displayName="@mantine/core/ColorPickerThumb";var D={wrapper:"m_fee9c77",preview:"m_9dddfbac",body:"m_bffecc3e",sliders:"m_3283bb96",thumb:"m_40d572ba",swatch:"m_d8ee6fd8",swatches:"m_5711e686",saturation:"m_202a296e",saturationOverlay:"m_11b3db02",slider:"m_d856d47d",sliderOverlay:"m_8f327113"};let P={},I=(0,h.P9)((e,t)=>{var r;let i=(0,f.Y)("ColorSlider",P,e),{classNames:a,className:l,style:u,styles:c,unstyled:h,vars:p,onChange:v,onChangeEnd:y,maxValue:b,round:w,size:x="md",focusable:C=!0,value:j,overlays:I,thumbColor:R="transparent",onScrubStart:T,onScrubEnd:L,__staticSelector:M="ColorPicker",...N}=i,z=(0,d.I)({name:M,classes:D,props:i,className:l,style:u,classNames:a,styles:c,unstyled:h}),B=(null==(r=k())?void 0:r.getStyles)||z,Y=(0,E.xd)(),[F,$]=(0,o.useState)({y:0,x:j/b}),q=(0,o.useRef)(F),V=e=>w?Math.round(e*b):e*b,{ref:U}=_(e=>{let{x:t,y:r}=e;q.current={x:t,y:r},null==v||v(V(t))},{onScrubEnd:()=>{let{x:e}=q.current;null==y||y(V(e)),null==L||L()},onScrubStart:T});(0,s.C)(()=>{$({y:0,x:j/b})},[j]);let H=(e,t)=>{e.preventDefault();let r=S(t);null==v||v(V(r.x)),null==y||y(V(r.x))},G=I.map((e,t)=>(0,o.createElement)("div",{...B("sliderOverlay"),style:e,key:t}));return(0,n.jsxs)(g.a,{...N,ref:(0,A.pc)(U,t),...B("slider"),role:"slider","aria-valuenow":j,"aria-valuemax":b,"aria-valuemin":0,tabIndex:C?0:-1,onKeyDown:e=>{switch(e.key){case"ArrowRight":H(e,{x:F.x+.05,y:F.y});break;case"ArrowLeft":H(e,{x:F.x-.05,y:F.y})}},"data-focus-ring":Y.focusRing,__vars:{"--cp-thumb-size":"var(--cp-thumb-size-".concat(x,")")},children:[G,(0,n.jsx)(O,{position:F,...B("thumb",{style:{top:(0,m.D)(1),background:R}})})]})});function R(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10**t;return Math.round(r*e)/r}I.displayName="@mantine/core/ColorSlider";let T={grad:.9,turn:360,rad:360/(2*Math.PI)},L=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i;function M(e){let t=L.exec(e);return t?function(e){let{h:t,s:r,l:n,a:o}=e,i=(n<50?n:100-n)/100*r;return{h:t,s:i>0?2*i/(n+i)*100:0,v:n+i,a:o}}({h:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"deg";return Number(e)*(T[t]||1)}(t[1],t[2]),s:Number(t[3]),l:Number(t[4]),a:void 0===t[5]?1:Number(t[5])/(t[6]?100:1)}):{h:0,s:0,v:0,a:1}}function N(e){let{r:t,g:r,b:n,a:o}=e,i=Math.max(t,r,n),a=i-Math.min(t,r,n),s=a?i===t?(r-n)/a:i===r?2+(n-t)/a:4+(t-r)/a:0;return{h:R(60*(s<0?s+6:s),3),s:R(i?a/i*100:0,3),v:R(i/255*100,3),a:o}}function z(e){let t="#"===e[0]?e.slice(1):e;return 3===t.length?N({r:parseInt(t[0]+t[0],16),g:parseInt(t[1]+t[1],16),b:parseInt(t[2]+t[2],16),a:1}):N({r:parseInt(t.slice(0,2),16),g:parseInt(t.slice(2,4),16),b:parseInt(t.slice(4,6),16),a:1})}let B=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i;function Y(e){let t=B.exec(e);return t?N({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):{h:0,s:0,v:0,a:1}}let F={hex:/^#?([0-9A-F]{3}){1,2}$/i,hexa:/^#?([0-9A-F]{4}){1,2}$/i,rgb:/^rgb\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/i,rgba:/^rgba\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/i,hsl:/hsl\(\s*(\d+)\s*,\s*(\d+(?:\.\d+)?%)\s*,\s*(\d+(?:\.\d+)?%)\)/i,hsla:/^hsla\((\d+),\s*([\d.]+)%,\s*([\d.]+)%,\s*(\d*(?:\.\d+)?)\)$/i},$={hex:z,hexa:function(e){let t="#"===e[0]?e.slice(1):e,r=e=>R(parseInt(e,16)/255,3);if(4===t.length){let e=t.slice(0,3),n=r(t[3]+t[3]);return{...z(e),a:n}}let n=t.slice(0,6),o=r(t.slice(6,8));return{...z(n),a:o}},rgb:Y,rgba:Y,hsl:M,hsla:M};function q(e){for(let[,t]of Object.entries(F))if(t.test(e))return!0;return!1}function V(e){if("string"!=typeof e)return{h:0,s:0,v:0,a:1};if("transparent"===e)return{h:0,s:0,v:0,a:0};let t=e.trim();for(let[e,r]of Object.entries(F))if(r.test(t))return $[e](t);return{h:0,s:0,v:0,a:1}}let U={},H=(0,o.forwardRef)((e,t)=>{let{value:r,onChange:o,onChangeEnd:i,color:a,...s}=(0,f.Y)("AlphaSlider",U,e);return(0,n.jsx)(I,{...s,ref:t,value:r,onChange:e=>null==o?void 0:o(R(e,2)),onChangeEnd:e=>null==i?void 0:i(R(e,2)),maxValue:1,round:!1,"data-alpha":!0,overlays:[{backgroundImage:"linear-gradient(45deg, var(--slider-checkers) 25%, transparent 25%), linear-gradient(-45deg, var(--slider-checkers) 25%, transparent 25%), linear-gradient(45deg, transparent 75%, var(--slider-checkers) 75%), linear-gradient(-45deg, var(--mantine-color-body) 75%, var(--slider-checkers) 75%)",backgroundSize:"".concat((0,m.D)(8)," ").concat((0,m.D)(8)),backgroundPosition:"0 0, 0 ".concat((0,m.D)(4),", ").concat((0,m.D)(4)," ").concat((0,m.D)(-4),", ").concat((0,m.D)(-4)," 0")},{backgroundImage:"linear-gradient(90deg, transparent, ".concat(a,")")},{boxShadow:"rgba(0, 0, 0, .1) 0 0 0 ".concat((0,m.D)(1)," inset, rgb(0, 0, 0, .15) 0 0 ").concat((0,m.D)(4)," inset")}]})});function G(e){let{h:t,s:r,v:n,a:o}=e,i=t/360*6,a=r/100,s=n/100,l=Math.floor(i),u=s*(1-a),c=s*(1-(i-l)*a),d=s*(1-(1-i+l)*a),f=l%6;return{r:R(255*[s,c,u,u,d,s][f]),g:R(255*[d,s,s,c,u,u][f]),b:R(255*[u,u,d,s,s,c][f]),a:R(o,2)}}function W(e,t){let{r,g:n,b:o,a:i}=G(e);return t?"rgba(".concat(r,", ").concat(n,", ").concat(o,", ").concat(R(i,2),")"):"rgb(".concat(r,", ").concat(n,", ").concat(o,")")}function X(e,t){let{h:r,s:n,v:o,a:i}=e,a=(200-n)*o/100,s={h:Math.round(r),s:Math.round(a>0&&a<200?n*o/100/(a<=100?a:200-a)*100:0),l:Math.round(a/2)};return t?"hsla(".concat(s.h,", ").concat(s.s,"%, ").concat(s.l,"%, ").concat(R(i,2),")"):"hsl(".concat(s.h,", ").concat(s.s,"%, ").concat(s.l,"%)")}function J(e){let t=e.toString(16);return t.length<2?"0".concat(t):t}function K(e){let{r:t,g:r,b:n}=G(e);return"#".concat(J(t)).concat(J(r)).concat(J(n))}H.displayName="@mantine/core/AlphaSlider";let Z={hex:K,hexa:e=>(function(e){let t=Math.round(255*e.a);return"".concat(K(e)).concat(J(t))})(e),rgb:e=>W(e,!1),rgba:e=>W(e,!0),hsl:e=>X(e,!1),hsla:e=>X(e,!0)};function Q(e,t){return t?e in Z?Z[e](t):Z.hex(t):"#000000"}let ee=(0,o.forwardRef)((e,t)=>{let{value:r,onChange:o,onChangeEnd:i,color:a,...s}=(0,f.Y)("HueSlider",{},e);return(0,n.jsx)(I,{...s,ref:t,value:r,onChange:o,onChangeEnd:i,maxValue:360,thumbColor:"hsl(".concat(r,", 100%, 50%)"),round:!0,"data-hue":!0,overlays:[{backgroundImage:"linear-gradient(to right,hsl(0,100%,50%),hsl(60,100%,50%),hsl(120,100%,50%),hsl(170,100%,50%),hsl(240,100%,50%),hsl(300,100%,50%),hsl(360,100%,50%))"},{boxShadow:"rgba(0, 0, 0, .1) 0 0 0 ".concat((0,m.D)(1)," inset, rgb(0, 0, 0, .15) 0 0 ").concat((0,m.D)(4)," inset")}]})});function et(e){let{className:t,onChange:r,onChangeEnd:i,value:a,saturationLabel:s,focusable:l=!0,size:u,color:c,onScrubStart:d,onScrubEnd:f,...h}=e,{getStyles:p}=k(),[m,v]=(0,o.useState)({x:a.s/100,y:1-a.v/100}),y=(0,o.useRef)(m),{ref:b}=_(e=>{let{x:t,y:n}=e;y.current={x:t,y:n},r({s:Math.round(100*t),v:Math.round((1-n)*100)})},{onScrubEnd:()=>{let{x:e,y:t}=y.current;i({s:Math.round(100*e),v:Math.round((1-t)*100)}),null==f||f()},onScrubStart:d});(0,o.useEffect)(()=>{v({x:a.s/100,y:1-a.v/100})},[a.s,a.v]);let w=(e,t)=>{e.preventDefault();let n=S(t);r({s:Math.round(100*n.x),v:Math.round((1-n.y)*100)}),i({s:Math.round(100*n.x),v:Math.round((1-n.y)*100)})};return(0,n.jsxs)(g.a,{...p("saturation"),ref:b,...h,role:"slider","aria-label":s,"aria-valuenow":m.x,"aria-valuetext":Q("rgba",a),tabIndex:l?0:-1,onKeyDown:e=>{switch(e.key){case"ArrowUp":w(e,{y:m.y-.05,x:m.x});break;case"ArrowDown":w(e,{y:m.y+.05,x:m.x});break;case"ArrowRight":w(e,{x:m.x+.05,y:m.y});break;case"ArrowLeft":w(e,{x:m.x-.05,y:m.y})}},children:[(0,n.jsx)("div",{...p("saturationOverlay",{style:{backgroundColor:"hsl(".concat(a.h,", 100%, 50%)")}})}),(0,n.jsx)("div",{...p("saturationOverlay",{style:{backgroundImage:"linear-gradient(90deg, #fff, transparent)"}})}),(0,n.jsx)("div",{...p("saturationOverlay",{style:{backgroundImage:"linear-gradient(0deg, #000, transparent)"}})}),(0,n.jsx)(O,{position:m,...p("thumb",{style:{backgroundColor:c}})})]})}ee.displayName="@mantine/core/HueSlider",et.displayName="@mantine/core/Saturation";let er=(0,o.forwardRef)((e,t)=>{let{className:r,datatype:i,setValue:a,onChangeEnd:s,size:l,focusable:u,data:c,swatchesPerRow:d,...f}=e,h=k(),p=c.map((e,t)=>(0,o.createElement)(x,{...h.getStyles("swatch"),unstyled:h.unstyled,component:"button",type:"button",color:e,key:t,radius:"sm",onClick:()=>{a(e),null==s||s(e)},"aria-label":e,tabIndex:u?0:-1,"data-swatch":!0}));return(0,n.jsx)(g.a,{...h.getStyles("swatches"),ref:t,...f,children:p})});er.displayName="@mantine/core/Swatches";let en={swatchesPerRow:7,withPicker:!0,focusable:!0,size:"md",__staticSelector:"ColorPicker"},eo=(0,u.V)((e,t)=>{let{size:r,swatchesPerRow:n}=t;return{wrapper:{"--cp-preview-size":(0,l.YC)(r,"cp-preview-size"),"--cp-width":(0,l.YC)(r,"cp-width"),"--cp-body-spacing":(0,l.GY)(r),"--cp-swatch-size":"".concat(100/n,"%"),"--cp-thumb-size":(0,l.YC)(r,"cp-thumb-size"),"--cp-saturation-height":(0,l.YC)(r,"cp-saturation-height")}}}),ei=(0,h.P9)((e,t)=>{let r=(0,f.Y)("ColorPicker",en,e),{classNames:a,className:l,style:u,styles:c,unstyled:h,vars:p,format:m,value:v,defaultValue:y,onChange:b,onChangeEnd:w,withPicker:C,size:S,saturationLabel:_,hueLabel:A,alphaLabel:E,focusable:k,swatches:O,swatchesPerRow:P,fullWidth:I,onColorSwatchClick:R,__staticSelector:T,mod:L,...M}=r,N=(0,d.I)({name:T,props:r,classes:D,className:l,style:u,classNames:a,styles:c,unstyled:h,rootSelector:"wrapper",vars:p,varsResolver:eo}),z=(0,o.useRef)(m),B=(0,o.useRef)(""),Y=(0,o.useRef)(-1),F=(0,o.useRef)(!1),$="hexa"===m||"rgba"===m||"hsla"===m,[U,G,W]=(0,i.Z)({value:v,defaultValue:y,finalValue:"#FFFFFF",onChange:b}),[X,J]=(0,o.useState)(V(U)),K=()=>{window.clearTimeout(Y.current),F.current=!0},Z=()=>{window.clearTimeout(Y.current),Y.current=window.setTimeout(()=>{F.current=!1},200)},ei=e=>{J(t=>{let r={...t,...e};return B.current=Q(z.current,r),r}),G(B.current)};return(0,s.C)(()=>{q(v)&&!F.current&&J(V(v))},[v]),(0,s.C)(()=>{z.current=m,G(Q(m,X))},[m]),(0,n.jsx)(j,{value:{getStyles:N,unstyled:h},children:(0,n.jsxs)(g.a,{ref:t,...N("wrapper"),size:S,mod:[{"full-width":I},L],...M,children:[C&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(et,{value:X,onChange:ei,onChangeEnd:e=>{let{s:t,v:r}=e;return null==w?void 0:w(Q(z.current,{...X,s:t,v:r}))},color:U,size:S,focusable:k,saturationLabel:_,onScrubStart:K,onScrubEnd:Z}),(0,n.jsxs)("div",{...N("body"),children:[(0,n.jsxs)("div",{...N("sliders"),children:[(0,n.jsx)(ee,{value:X.h,onChange:e=>ei({h:e}),onChangeEnd:e=>null==w?void 0:w(Q(z.current,{...X,h:e})),size:S,focusable:k,"aria-label":A,onScrubStart:K,onScrubEnd:Z}),$&&(0,n.jsx)(H,{value:X.a,onChange:e=>ei({a:e}),onChangeEnd:e=>{null==w||w(Q(z.current,{...X,a:e}))},size:S,color:Q("hex",X),focusable:k,"aria-label":E,onScrubStart:K,onScrubEnd:Z})]}),$&&(0,n.jsx)(x,{color:U,radius:"sm",size:"var(--cp-preview-size)",...N("preview")})]})]}),Array.isArray(O)&&(0,n.jsx)(er,{data:O,swatchesPerRow:P,focusable:k,setValue:G,onChangeEnd:e=>{let t=Q(m,V(e));null==R||R(t),null==w||w(t),W||J(V(e))}})]})})});ei.classes=D,ei.displayName="@mantine/core/ColorPicker";var ea=r(8993),es=r(3746),el=r(4225),eu=r(728);function ec(e){let{style:t,...r}=e;return(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",style:{width:"var(--ci-eye-dropper-icon-size)",height:"var(--ci-eye-dropper-icon-size)",...t},viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",...r,children:[(0,n.jsx)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,n.jsx)("path",{d:"M11 7l6 6"}),(0,n.jsx)("path",{d:"M4 16l11.7 -11.7a1 1 0 0 1 1.4 0l2.6 2.6a1 1 0 0 1 0 1.4l-11.7 11.7h-4v-4z"})]})}var ed={eyeDropperIcon:"m_b077c2bc",colorPreview:"m_c5ccdcab",dropdown:"m_5ece2cd7"};let ef={format:"hex",fixOnBlur:!0,withPreview:!0,swatchesPerRow:7,withPicker:!0,popoverProps:{transitionProps:{transition:"fade",duration:0}},withEyeDropper:!0},eh=(0,u.V)((e,t)=>{let{size:r}=t;return{eyeDropperIcon:{"--ci-eye-dropper-icon-size":(0,l.YC)(r,"ci-eye-dropper-icon-size")},colorPreview:{"--ci-preview-size":(0,l.YC)(r,"ci-preview-size")}}}),ep=(0,h.P9)((e,t)=>{let r=(0,f.Y)("ColorInput",ef,e),{classNames:l,styles:u,unstyled:h,disallowInput:g,fixOnBlur:m,popoverProps:v,withPreview:y,withEyeDropper:b,eyeDropperIcon:w,closeOnColorSwatchClick:C,eyeDropperButtonProps:S,value:_,defaultValue:A,onChange:E,onChangeEnd:j,onClick:k,onFocus:O,onBlur:D,inputProps:P,format:I,wrapperProps:R,readOnly:T,withPicker:L,swatches:M,disabled:N,leftSection:z,rightSection:B,swatchesPerRow:Y,...F}=(0,es.I)("ColorInput",ef,e),$=(0,d.I)({name:"ColorInput",props:r,classes:ed,classNames:l,styles:u,unstyled:h,rootSelector:"wrapper",vars:r.vars,varsResolver:eh}),{resolvedClassNames:U,resolvedStyles:H}=(0,c.Y)({classNames:l,styles:u,props:r}),[G,W]=(0,o.useState)(!1),[X,J]=(0,o.useState)(""),[K,Z]=(0,i.Z)({value:_,defaultValue:A,finalValue:"",onChange:E}),{supported:ee,open:et}=function(){let[e,t]=(0,o.useState)(!1);(0,a.o)(()=>{t("undefined"!=typeof window&&!navigator.userAgent.includes("OPR")&&"EyeDropper"in window)},[]);let r=(0,o.useCallback)(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e?new window.EyeDropper().open(t):Promise.resolve(void 0)},[e]);return{supported:e,open:r}}(),er=(0,n.jsx)(p.M,{...S,...$("eyeDropperButton",{className:null==S?void 0:S.className,style:null==S?void 0:S.style}),variant:"subtle",color:"gray",size:P.size,unstyled:h,onClick:()=>et().then(e=>{if(null==e?void 0:e.sRGBHex){let t=Q(I,V(e.sRGBHex));Z(t),null==j||j(t)}}).catch(()=>{}),children:w||(0,n.jsx)(ec,{...$("eyeDropperIcon")})});return(0,o.useEffect)(()=>{(q(K)||""===K.trim())&&J(K)},[K]),(0,s.C)(()=>{q(K)&&Z(Q(I,V(K)))},[I]),(0,n.jsx)(ea.p.Wrapper,{...R,classNames:U,styles:H,__staticSelector:"ColorInput",children:(0,n.jsxs)(eu.A,{__staticSelector:"ColorInput",position:"bottom-start",offset:5,opened:G,...v,classNames:U,styles:H,unstyled:h,withRoles:!1,disabled:T||!1===L&&(!Array.isArray(M)||0===M.length),children:[(0,n.jsx)(eu.A.Target,{children:(0,n.jsx)(ea.p,{autoComplete:"off",...F,...P,classNames:U,styles:H,disabled:N,ref:t,__staticSelector:"ColorInput",onFocus:e=>{null==O||O(e),W(!0)},onBlur:e=>{m&&Z(X),null==D||D(e),W(!1)},onClick:e=>{null==k||k(e),W(!0)},spellCheck:!1,value:K,onChange:e=>{let t=e.currentTarget.value;Z(t),q(t)&&(null==j||j(Q(I,V(t))))},leftSection:z||(y?(0,n.jsx)(x,{color:q(K)?K:"#fff",size:"var(--ci-preview-size)",...$("colorPreview")}):null),readOnly:g||T,pointer:g,unstyled:h,rightSection:B||(b&&!N&&!T&&ee?er:null)})}),(0,n.jsx)(eu.A.Dropdown,{onMouseDown:e=>e.preventDefault(),className:ed.dropdown,children:(0,n.jsx)(ei,{__staticSelector:"ColorInput",value:K,onChange:Z,onChangeEnd:j,format:I,swatches:M,swatchesPerRow:Y,withPicker:L,size:P.size,focusable:!1,unstyled:h,styles:H,classNames:U,onColorSwatchClick:()=>C&&W(!1)})})]})})});ep.classes=el.O.classes,ep.displayName="@mantine/core/ColorInput"},112:(e,t,r)=>{"use strict";r.d(t,{Y:()=>p});var n=r(5155),o=r(2115),i=r(6204),a=r(8918),s=r(3664),l=r(862),u=r(311),c=r(6960),d={root:"m_4081bf90"};let f={preventGrowOverflow:!0,gap:"md",align:"center",justify:"flex-start",wrap:"wrap"},h=(0,a.V)((e,t,r)=>{let{grow:n,preventGrowOverflow:o,gap:a,align:s,justify:l,wrap:u}=t,{childWidth:c}=r;return{root:{"--group-child-width":n&&o?c:void 0,"--group-gap":(0,i.GY)(a),"--group-align":s,"--group-justify":l,"--group-wrap":u}}}),p=(0,c.P9)((e,t)=>{let r=(0,s.Y)("Group",f,e),{classNames:a,className:c,style:p,styles:g,unstyled:m,children:v,gap:y,align:b,justify:w,wrap:x,grow:C,preventGrowOverflow:S,vars:_,variant:A,__size:E,mod:j,...k}=r,O=o.Children.toArray(v).filter(Boolean),D=O.length,P=(0,i.GY)(null!=y?y:"md"),I="calc(".concat(100/D,"% - (").concat(P," - ").concat(P," / ").concat(D,"))"),R=(0,l.I)({name:"Group",props:r,stylesCtx:{childWidth:I},className:c,style:p,classes:d,classNames:a,styles:g,unstyled:m,vars:_,varsResolver:h});return(0,n.jsx)(u.a,{...R("root"),ref:t,variant:A,mod:[{grow:C},j],size:E,...k,children:O})});p.classes=d,p.displayName="@mantine/core/Group"},610:(e,t,r)=>{"use strict";r.d(t,{r:()=>C});var n=r(5155);r(2115);var o=r(3664),i=r(862),a=r(6390),s=r(311),l=r(6960),u=r(9224),c=r(1187),d=r(1526),f=r(6204),h=r(4861),p=r(3288),g=r(3131),m=r(8570);function v(e){var t;let{spacing:r,verticalSpacing:o,cols:i,selector:a}=e,s=(0,g.xd)(),l=void 0===o?r:o,c=(0,d.J)({"--sg-spacing-x":(0,f.GY)((0,p.D)(r)),"--sg-spacing-y":(0,f.GY)((0,p.D)(l)),"--sg-cols":null==(t=(0,p.D)(i))?void 0:t.toString()}),v=(0,u.H)(s.breakpoints).reduce((e,t)=>(e[t]||(e[t]={}),"object"==typeof r&&void 0!==r[t]&&(e[t]["--sg-spacing-x"]=(0,f.GY)(r[t])),"object"==typeof l&&void 0!==l[t]&&(e[t]["--sg-spacing-y"]=(0,f.GY)(l[t])),"object"==typeof i&&void 0!==i[t]&&(e[t]["--sg-cols"]=i[t]),e),{}),y=(0,h.q)((0,u.H)(v),s.breakpoints).filter(e=>(0,u.H)(v[e.value]).length>0).map(e=>({query:"(min-width: ".concat(s.breakpoints[e.value],")"),styles:v[e.value]}));return(0,n.jsx)(m.K,{styles:c,media:y,selector:a})}function y(e){return"object"==typeof e&&null!==e?(0,u.H)(e):[]}function b(e){var t;let{spacing:r,verticalSpacing:o,cols:i,selector:a}=e,s=void 0===o?r:o,l=(0,d.J)({"--sg-spacing-x":(0,f.GY)((0,p.D)(r)),"--sg-spacing-y":(0,f.GY)((0,p.D)(s)),"--sg-cols":null==(t=(0,p.D)(i))?void 0:t.toString()}),u=function(e){let{spacing:t,verticalSpacing:r,cols:n}=e;return Array.from(new Set([...y(t),...y(r),...y(n)])).sort((e,t)=>(0,c.px)(e)-(0,c.px)(t))}({spacing:r,verticalSpacing:o,cols:i}),h=u.reduce((e,t)=>(e[t]||(e[t]={}),"object"==typeof r&&void 0!==r[t]&&(e[t]["--sg-spacing-x"]=(0,f.GY)(r[t])),"object"==typeof s&&void 0!==s[t]&&(e[t]["--sg-spacing-y"]=(0,f.GY)(s[t])),"object"==typeof i&&void 0!==i[t]&&(e[t]["--sg-cols"]=i[t]),e),{}),g=u.map(e=>({query:"simple-grid (min-width: ".concat(e,")"),styles:h[e]}));return(0,n.jsx)(m.K,{styles:l,container:g,selector:a})}var w={container:"m_925c2d2c",root:"m_2415a157"};let x={cols:1,spacing:"md",type:"media"},C=(0,l.P9)((e,t)=>{let r=(0,o.Y)("SimpleGrid",x,e),{classNames:l,className:u,style:c,styles:d,unstyled:f,vars:h,cols:p,verticalSpacing:g,spacing:m,type:y,...C}=r,S=(0,i.I)({name:"SimpleGrid",classes:w,props:r,className:u,style:c,classNames:l,styles:d,unstyled:f,vars:h}),_=(0,a.C)();return"container"===y?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(b,{...r,selector:".".concat(_)}),(0,n.jsx)("div",{...S("container"),children:(0,n.jsx)(s.a,{ref:t,...S("root",{className:_}),...C})})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(v,{...r,selector:".".concat(_)}),(0,n.jsx)(s.a,{ref:t,...S("root",{className:_}),...C})]})});C.classes=w,C.displayName="@mantine/core/SimpleGrid"},728:(e,t,r)=>{"use strict";r.d(t,{A:()=>el});var n=r(5155),o=r(2115),i=r(1513);let a=["mousedown","touchstart"],s={app:100,modal:200,popover:300,overlay:400,max:9999};var l=r(6204),u=r(8918),c=r(6028),d=r(862),f=r(3664),h=r(3304),p=r(5903),g=r(714),m=r(311),v=r(4511),y={root:"m_9814e45f"};let b={zIndex:s.modal},w=(0,u.V)((e,t)=>{let{gradient:r,color:n,backgroundOpacity:o,blur:i,radius:a,zIndex:s}=t;return{root:{"--overlay-bg":r||(void 0!==n||void 0!==o)&&(0,g.B)(n||"#000",null!=o?o:.6)||void 0,"--overlay-filter":i?"blur(".concat((0,p.D)(i),")"):void 0,"--overlay-radius":void 0===a?void 0:(0,l.nJ)(a),"--overlay-z-index":null==s?void 0:s.toString()}}}),x=(0,v.v)((e,t)=>{let r=(0,f.Y)("Overlay",b,e),{classNames:o,className:i,style:a,styles:s,unstyled:l,vars:u,fixed:c,center:h,children:p,radius:g,zIndex:v,gradient:x,blur:C,color:S,backgroundOpacity:_,mod:A,...E}=r,j=(0,d.I)({name:"Overlay",props:r,classes:y,className:i,style:a,classNames:o,styles:s,unstyled:l,vars:u,varsResolver:w});return(0,n.jsx)(m.a,{ref:t,...j("root"),mod:[{center:h,fixed:c},A],...E,children:p})});x.classes=y,x.displayName="@mantine/core/Overlay";var C=r(7650),S=r(3141),_=r(8551);let A={},E=(0,o.forwardRef)((e,t)=>{let{children:r,target:i,...a}=(0,f.Y)("Portal",A,e),[s,l]=(0,o.useState)(!1),u=(0,o.useRef)(null);return((0,S.o)(()=>(l(!0),u.current=i?"string"==typeof i?document.querySelector(i):i:function(e){let t=document.createElement("div");return t.setAttribute("data-portal","true"),"string"==typeof e.className&&t.classList.add(...e.className.split(" ").filter(Boolean)),"object"==typeof e.style&&Object.assign(t.style,e.style),"string"==typeof e.id&&t.setAttribute("id",e.id),t}(a),(0,_.bl)(t,u.current),!i&&u.current&&document.body.appendChild(u.current),()=>{!i&&u.current&&document.body.removeChild(u.current)}),[i]),s&&u.current)?(0,C.createPortal)((0,n.jsx)(n.Fragment,{children:r}),u.current):null});function j(e){let{withinPortal:t=!0,children:r,...o}=e;return t?(0,n.jsx)(E,{...o,children:r}):(0,n.jsx)(n.Fragment,{children:r})}E.displayName="@mantine/core/Portal",j.displayName="@mantine/core/OptionalPortal";var k=r(2213);let[O,D]=(0,r(6970).F)("Popover component was not found in the tree");var P=r(4237);let I=()=>{};var R=r(6960);function T(e,t,r,n){return"center"===e||"center"===n?{top:t}:"end"===e?{bottom:r}:"start"===e?{top:r}:{}}function L(e,t,r,n,o){return"center"===e||"center"===n?{left:t}:"end"===e?{["ltr"===o?"right":"left"]:r}:"start"===e?{["ltr"===o?"left":"right"]:r}:{}}let M={bottom:"borderTopLeftRadius",left:"borderTopRightRadius",right:"borderBottomLeftRadius",top:"borderBottomRightRadius"},N=(0,o.forwardRef)((e,t)=>{let{position:r,arrowSize:o,arrowOffset:i,arrowRadius:a,arrowPosition:s,visible:l,arrowX:u,arrowY:c,style:d,...f}=e,{dir:p}=(0,h.jH)();return l?(0,n.jsx)("div",{...f,ref:t,style:{...d,...function(e){let{position:t,arrowSize:r,arrowOffset:n,arrowRadius:o,arrowPosition:i,arrowX:a,arrowY:s,dir:l}=e,[u,c="center"]=t.split("-"),d={width:r,height:r,transform:"rotate(45deg)",position:"absolute",[M[u]]:o},f=-r/2;return"left"===u?{...d,...T(c,s,n,i),right:f,borderLeftColor:"transparent",borderBottomColor:"transparent",clipPath:"polygon(100% 0, 0 0, 100% 100%)"}:"right"===u?{...d,...T(c,s,n,i),left:f,borderRightColor:"transparent",borderTopColor:"transparent",clipPath:"polygon(0 100%, 0 0, 100% 100%)"}:"top"===u?{...d,...L(c,a,n,i,l),bottom:f,borderTopColor:"transparent",borderLeftColor:"transparent",clipPath:"polygon(0 100%, 100% 100%, 100% 0)"}:"bottom"===u?{...d,...L(c,a,n,i,l),top:f,borderBottomColor:"transparent",borderRightColor:"transparent",clipPath:"polygon(0 100%, 0 0, 100% 0)"}:{}}({position:r,arrowSize:o,arrowOffset:i,arrowRadius:a,arrowPosition:s,dir:p,arrowX:u,arrowY:c})}}):null});N.displayName="@mantine/core/FloatingArrow";let z=/input|select|textarea|button|object/,B="a, input, select, textarea, button, object, [tabindex]";function Y(e){let t=e.getAttribute("tabindex");return null===t&&(t=void 0),parseInt(t,10)}function F(e){let t=e.nodeName.toLowerCase(),r=!Number.isNaN(Y(e));return(z.test(t)&&!e.disabled||e instanceof HTMLAnchorElement&&e.href||r)&&function(e){if(e.getAttribute("aria-hidden")||e.getAttribute("hidden")||"hidden"===e.getAttribute("type"))return!1;let t=e;for(;t&&t!==document.body&&11!==t.nodeType;){if("none"===t.style.display)return!1;t=t.parentNode}return!0}(e)}function $(e){let t=Y(e);return(Number.isNaN(t)||t>=0)&&F(e)}var q=r(866),V={root:"m_515a97f8"};let U={},H=(0,R.P9)((e,t)=>{let r=(0,f.Y)("VisuallyHidden",U,e),{classNames:o,className:i,style:a,styles:s,unstyled:l,vars:u,...c}=r,h=(0,d.I)({name:"VisuallyHidden",classes:V,props:r,className:i,style:a,classNames:o,styles:s,unstyled:l});return(0,n.jsx)(m.a,{component:"span",ref:t,...h("root"),...c})});function G(e){let{children:t,active:r=!0,refProp:n="ref",innerRef:i}=e,a=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,o.useRef)(null),r=e=>{let t=e.querySelector("[data-autofocus]");if(!t){let r=Array.from(e.querySelectorAll(B));!(t=r.find($)||r.find(F)||null)&&F(e)&&(t=e)}t&&t.focus({preventScroll:!0})},n=(0,o.useCallback)(n=>{e&&null!==n&&t.current!==n&&(n?(setTimeout(()=>{n.getRootNode()&&r(n)}),t.current=n):t.current=null)},[e]);return(0,o.useEffect)(()=>{if(!e)return;t.current&&setTimeout(()=>r(t.current));let n=e=>{"Tab"===e.key&&t.current&&function(e,t){let r=Array.from(e.querySelectorAll(B)).filter($);if(!r.length)return t.preventDefault();let n=r[t.shiftKey?0:r.length-1],o=e.getRootNode(),i=n===o.activeElement||e===o.activeElement,a=o.activeElement;if("INPUT"===a.tagName&&"radio"===a.getAttribute("type")&&(i=r.filter(e=>"radio"===e.getAttribute("type")&&e.getAttribute("name")===a.getAttribute("name")).includes(n)),!i)return;t.preventDefault();let s=r[t.shiftKey?r.length-1:0];s&&s.focus()}(t.current,e)};return document.addEventListener("keydown",n),()=>document.removeEventListener("keydown",n)},[e]),n}(r),s=(0,_.pc)(a,i);return(0,q.v)(t)?(0,o.cloneElement)(t,{[n]:s}):t}function W(e){return(0,n.jsx)(H,{tabIndex:-1,"data-autofocus":!0,...e})}H.classes=V,H.displayName="@mantine/core/VisuallyHidden",G.displayName="@mantine/core/FocusTrap",W.displayName="@mantine/core/FocusTrapInitialFocus",G.InitialFocus=W;var X={dropdown:"m_38a85659",arrow:"m_a31dc6c1",overlay:"m_3d7bc908"};let J={},K=(0,R.P9)((e,t)=>{var r,i,a,s,l;let u=(0,f.Y)("PopoverDropdown",J,e),{className:c,style:d,vars:h,children:g,onKeyDownCapture:v,variant:y,classNames:b,styles:w,...x}=u,C=D(),S=function(e){let{opened:t,shouldReturnFocus:r=!0}=e,n=(0,o.useRef)(null),i=()=>{if(n.current&&"focus"in n.current&&"function"==typeof n.current.focus){var e;null==(e=n.current)||e.focus({preventScroll:!0})}};return(0,P.C)(()=>{let e=-1,o=t=>{"Tab"===t.key&&window.clearTimeout(e)};return document.addEventListener("keydown",o),t?n.current=document.activeElement:r&&(e=window.setTimeout(i,10)),()=>{window.clearTimeout(e),document.removeEventListener("keydown",o)}},[t,r]),i}({opened:C.opened,shouldReturnFocus:C.returnFocus}),A=C.withRoles?{"aria-labelledby":C.getTargetId(),id:C.getDropdownId(),role:"dialog",tabIndex:-1}:{},E=(0,_.pc)(t,C.floating);return C.disabled?null:(0,n.jsx)(j,{...C.portalProps,withinPortal:C.withinPortal,children:(0,n.jsx)(k.e,{mounted:C.opened,...C.transitionProps,transition:(null==(r=C.transitionProps)?void 0:r.transition)||"fade",duration:null!=(l=null==(i=C.transitionProps)?void 0:i.duration)?l:150,keepMounted:C.keepMounted,exitDuration:"number"==typeof(null==(a=C.transitionProps)?void 0:a.exitDuration)?C.transitionProps.exitDuration:null==(s=C.transitionProps)?void 0:s.duration,children:e=>{var t,r;return(0,n.jsx)(G,{active:C.trapFocus&&C.opened,innerRef:E,children:(0,n.jsxs)(m.a,{...A,...x,variant:y,onKeyDownCapture:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{active:!0};return"function"==typeof e&&t.active?r=>{if("Escape"===r.key){var n;e(r),null==(n=t.onTrigger)||n.call(t)}}:t.onKeyDown||I}(C.onClose,{active:C.closeOnEscape,onTrigger:S,onKeyDown:v}),"data-position":C.placement,"data-fixed":"fixed"===C.floatingStrategy||void 0,...C.getStyles("dropdown",{className:c,props:u,classNames:b,styles:w,style:[{...e,zIndex:C.zIndex,top:null!=(t=C.y)?t:0,left:null!=(r=C.x)?r:0,width:"target"===C.width?void 0:(0,p.D)(C.width)},C.resolvedStyles.dropdown,null==w?void 0:w.dropdown,d]}),children:[g,(0,n.jsx)(N,{ref:C.arrowRef,arrowX:C.arrowX,arrowY:C.arrowY,visible:C.withArrow,position:C.placement,arrowSize:C.arrowSize,arrowRadius:C.arrowRadius,arrowOffset:C.arrowOffset,arrowPosition:C.arrowPosition,...C.getStyles("arrow",{props:u,classNames:b,styles:w})})]})})}})})});K.classes=X,K.displayName="@mantine/core/PopoverDropdown";var Z=r(2596),Q=r(2200);let ee={refProp:"ref",popupType:"dialog"},et=(0,R.P9)((e,t)=>{let{children:r,refProp:n,popupType:i,...a}=(0,f.Y)("PopoverTarget",ee,e);if(!(0,q.v)(r))throw Error("Popover.Target component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");let s=D(),l=(0,_.pc)(s.reference,(0,Q.x)(r),t),u=s.withRoles?{"aria-haspopup":i,"aria-expanded":s.opened,"aria-controls":s.getDropdownId(),id:s.getTargetId()}:{};return(0,o.cloneElement)(r,{...a,...u,...s.targetProps,className:(0,Z.A)(s.targetProps.className,a.className,r.props.className),[n]:l,...!s.controlled?{onClick:s.onToggle}:null})});et.displayName="@mantine/core/PopoverTarget";var er=r(4945),en=r(5299),eo=r(7613),ei=r(6492);let ea={position:"bottom",offset:8,positionDependencies:[],transitionProps:{transition:"fade",duration:150},middlewares:{flip:!0,shift:!0,inline:!1},arrowSize:7,arrowOffset:5,arrowRadius:0,arrowPosition:"side",closeOnClickOutside:!0,withinPortal:!0,closeOnEscape:!0,trapFocus:!1,withRoles:!0,returnFocus:!1,withOverlay:!1,clickOutsideEvents:["mousedown","touchstart"],zIndex:s.popover,__staticSelector:"Popover",width:"max-content"},es=(0,u.V)((e,t)=>{let{radius:r,shadow:n}=t;return{dropdown:{"--popover-radius":void 0===r?void 0:(0,l.nJ)(r),"--popover-shadow":(0,l.dh)(n)}}});function el(e){var t,r,s,l,u,p;let g=(0,f.Y)("Popover",ea,e),{children:m,position:v,offset:y,onPositionChange:b,positionDependencies:w,opened:C,transitionProps:S,onExitTransitionEnd:_,onEnterTransitionEnd:A,width:E,middlewares:D,withArrow:I,arrowSize:R,arrowOffset:T,arrowRadius:L,arrowPosition:M,unstyled:N,classNames:z,styles:B,closeOnClickOutside:Y,withinPortal:F,portalProps:$,closeOnEscape:q,clickOutsideEvents:V,trapFocus:U,onClose:H,onOpen:G,onChange:W,zIndex:J,radius:K,shadow:Z,id:Q,defaultOpened:ee,__staticSelector:et,withRoles:el,disabled:eu,returnFocus:ec,variant:ed,keepMounted:ef,vars:eh,floatingStrategy:ep,withOverlay:eg,overlayProps:em,...ev}=g,ey=(0,d.I)({name:et,props:g,classes:X,classNames:z,styles:B,unstyled:N,rootSelector:"dropdown",vars:eh,varsResolver:es}),{resolvedStyles:eb}=(0,c.Y)({classNames:z,styles:B,props:g}),ew=(0,o.useRef)(null),[ex,eC]=(0,o.useState)(null),[eS,e_]=(0,o.useState)(null),{dir:eA}=(0,h.jH)(),eE=(0,i.B)(Q),ej=function(e){let[t,r]=(0,eo.Z)({value:e.opened,defaultValue:e.defaultOpened,finalValue:!1,onChange:e.onChange}),n=(0,o.useRef)(t),i=(0,en.we)({strategy:e.strategy,placement:e.position,middleware:function(e,t){let r=function(e){if(void 0===e)return{shift:!0,flip:!0};let t={...e};return void 0===e.shift&&(t.shift=!0),void 0===e.flip&&(t.flip=!0),t}(e.middlewares),n=[(0,er.cY)(e.offset)];return r.shift&&n.push((0,er.BN)("boolean"==typeof r.shift?{limiter:(0,er.ER)(),padding:5}:{limiter:(0,er.ER)(),padding:5,...r.shift})),r.flip&&n.push("boolean"==typeof r.flip?(0,er.UU)():(0,er.UU)(r.flip)),r.inline&&n.push("boolean"==typeof r.inline?(0,er.mG)():(0,er.mG)(r.inline)),n.push((0,er.UE)({element:e.arrowRef,padding:e.arrowOffset})),(r.size||"target"===e.width)&&n.push((0,er.Ej)({..."boolean"==typeof r.size?{}:r.size,apply(n){var o,i;let{rects:a,availableWidth:s,availableHeight:l,...u}=n,c=null!=(i=null==(o=t().refs.floating.current)?void 0:o.style)?i:{};r.size&&("object"==typeof r.size&&r.size.apply?r.size.apply({rects:a,availableWidth:s,availableHeight:l,...u}):Object.assign(c,{maxWidth:"".concat(s,"px"),maxHeight:"".concat(l,"px")})),"target"===e.width&&Object.assign(c,{width:"".concat(a.reference.width,"px")})}})),n}(e,()=>i)});return!function(e){let{opened:t,floating:r,position:n,positionDependencies:i}=e,[a,s]=(0,o.useState)(0);(0,o.useEffect)(()=>{if(r.refs.reference.current&&r.refs.floating.current&&t)return(0,ei.ll)(r.refs.reference.current,r.refs.floating.current,r.update)},[r.refs.reference.current,r.refs.floating.current,t,a,n]),(0,P.C)(()=>{r.update()},i),(0,P.C)(()=>{s(e=>e+1)},[t])}({opened:t,position:e.position,positionDependencies:e.positionDependencies||[],floating:i}),(0,P.C)(()=>{var t;null==(t=e.onPositionChange)||t.call(e,i.placement)},[i.placement]),(0,P.C)(()=>{if(t!==n.current){var r,o;t?null==(o=e.onOpen)||o.call(e):null==(r=e.onClose)||r.call(e)}n.current=t},[t,e.onClose,e.onOpen]),{floating:i,controlled:"boolean"==typeof e.opened,opened:t,onClose:()=>{t&&r(!1)},onToggle:()=>r(!t)}}({middlewares:D,width:E,position:function(e,t){if("rtl"===e&&(t.includes("right")||t.includes("left"))){let[e,r]=t.split("-"),n="right"===e?"left":"right";return void 0===r?n:"".concat(n,"-").concat(r)}return t}(eA,v),offset:"number"==typeof y?y+(I?R/2:0):y,arrowRef:ew,arrowOffset:T,onPositionChange:b,positionDependencies:w,opened:C,defaultOpened:ee,onChange:W,onOpen:G,onClose:H,strategy:ep});!function(e,t,r){let n=(0,o.useRef)(null);(0,o.useEffect)(()=>{let o=t=>{let{target:o}=null!=t?t:{};if(Array.isArray(r)){let n=(null==o?void 0:o.hasAttribute("data-ignore-outside-clicks"))||!document.body.contains(o)&&"HTML"!==o.tagName;r.every(e=>!!e&&!t.composedPath().includes(e))&&!n&&e()}else n.current&&!n.current.contains(o)&&e()};return(t||a).forEach(e=>document.addEventListener(e,o)),()=>{(t||a).forEach(e=>document.removeEventListener(e,o))}},[n,e,r])}(()=>Y&&ej.onClose(),V,[ex,eS]);let ek=(0,o.useCallback)(e=>{eC(e),ej.floating.refs.setReference(e)},[ej.floating.refs.setReference]),eO=(0,o.useCallback)(e=>{e_(e),ej.floating.refs.setFloating(e)},[ej.floating.refs.setFloating]),eD=(0,o.useCallback)(()=>{var e;null==S||null==(e=S.onExited)||e.call(S),null==_||_()},[null==S?void 0:S.onExited,_]),eP=(0,o.useCallback)(()=>{var e;null==S||null==(e=S.onEntered)||e.call(S),null==A||A()},[null==S?void 0:S.onEntered,A]);return(0,n.jsxs)(O,{value:{returnFocus:ec,disabled:eu,controlled:ej.controlled,reference:ek,floating:eO,x:ej.floating.x,y:ej.floating.y,arrowX:null==(s=ej.floating)||null==(r=s.middlewareData)||null==(t=r.arrow)?void 0:t.x,arrowY:null==(p=ej.floating)||null==(u=p.middlewareData)||null==(l=u.arrow)?void 0:l.y,opened:ej.opened,arrowRef:ew,transitionProps:{...S,onExited:eD,onEntered:eP},width:E,withArrow:I,arrowSize:R,arrowOffset:T,arrowRadius:L,arrowPosition:M,placement:ej.floating.placement,trapFocus:U,withinPortal:F,portalProps:$,zIndex:J,radius:K,shadow:Z,closeOnEscape:q,onClose:ej.onClose,onToggle:ej.onToggle,getTargetId:()=>"".concat(eE,"-target"),getDropdownId:()=>"".concat(eE,"-dropdown"),withRoles:el,targetProps:ev,__staticSelector:et,classNames:z,styles:B,unstyled:N,variant:ed,keepMounted:ef,getStyles:ey,resolvedStyles:eb,floatingStrategy:ep},children:[m,eg&&(0,n.jsx)(k.e,{transition:"fade",mounted:ej.opened,duration:(null==S?void 0:S.duration)||250,exitDuration:(null==S?void 0:S.exitDuration)||250,children:e=>(0,n.jsx)(j,{withinPortal:F,children:(0,n.jsx)(x,{...em,...ey("overlay",{className:null==em?void 0:em.className,style:[e,null==em?void 0:em.style]})})})})]})}el.Target=et,el.Dropdown=K,el.displayName="@mantine/core/Popover",el.extend=e=>e},866:(e,t,r)=>{"use strict";r.d(t,{v:()=>o});var n=r(2115);function o(e){return!Array.isArray(e)&&null!==e&&"object"==typeof e&&e.type!==n.Fragment}},1187:(e,t,r)=>{"use strict";function n(e){var t;let r="string"==typeof e&&e.includes("var(--mantine-scale)")?null==(t=e.match(/^calc\((.*?)\)$/))?void 0:t[1].split("*")[0].trim():e;return"number"==typeof r?r:"string"==typeof r?r.includes("calc")||r.includes("var")?r:r.includes("px")?Number(r.replace("px","")):r.includes("rem")?16*Number(r.replace("rem","")):r.includes("em")?16*Number(r.replace("em","")):Number(r):NaN}r.d(t,{px:()=>n})},1220:(e,t,r)=>{"use strict";r.d(t,{B:()=>h});var n=r(5155);r(2115);var o=r(6204),i=r(8918),a=r(3664),s=r(862),l=r(311),u=r(6960),c={root:"m_6d731127"};let d={gap:"md",align:"stretch",justify:"flex-start"},f=(0,i.V)((e,t)=>{let{gap:r,align:n,justify:i}=t;return{root:{"--stack-gap":(0,o.GY)(r),"--stack-align":n,"--stack-justify":i}}}),h=(0,u.P9)((e,t)=>{let r=(0,a.Y)("Stack",d,e),{classNames:o,className:i,style:u,styles:h,unstyled:p,vars:g,align:m,justify:v,gap:y,variant:b,...w}=r,x=(0,s.I)({name:"Stack",props:r,classes:c,className:i,style:u,classNames:o,styles:h,unstyled:p,vars:g,varsResolver:f});return(0,n.jsx)(l.a,{ref:t,...x("root"),variant:b,...w})});h.classes=c,h.displayName="@mantine/core/Stack"},1513:(e,t,r)=>{"use strict";r.d(t,{B:()=>a});var n=r(2115),o=r(3141);let i=n["useId".toString()]||(()=>void 0);function a(e){let t=function(){let e=i();return e?"mantine-".concat(e.replace(/:/g,"")):""}(),[r,a]=(0,n.useState)(t);return((0,o.o)(()=>{a(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mantine-";return"".concat(e).concat(Math.random().toString(36).slice(2,11))}())},[]),"string"==typeof e)?e:"undefined"==typeof window?t:r}},1587:(e,t,r)=>{"use strict";r.d(t,{s:()=>m});var n=r(5155),o=r(1526);r(2115);var i=r(3131),a=r(3664),s=r(862),l=r(8570),u=r(6325),c=r(6390),d=r(311),f=r(4511);let h={gap:{type:"spacing",property:"gap"},rowGap:{type:"spacing",property:"rowGap"},columnGap:{type:"spacing",property:"columnGap"},align:{type:"identity",property:"alignItems"},justify:{type:"identity",property:"justifyContent"},wrap:{type:"identity",property:"flexWrap"},direction:{type:"identity",property:"flexDirection"}};var p={root:"m_8bffd616"};let g={},m=(0,f.v)((e,t)=>{let r=(0,a.Y)("Flex",g,e),{classNames:f,className:m,style:v,styles:y,unstyled:b,vars:w,gap:x,rowGap:C,columnGap:S,align:_,justify:A,wrap:E,direction:j,...k}=r,O=(0,s.I)({name:"Flex",classes:p,props:r,className:m,style:v,classNames:f,styles:y,unstyled:b,vars:w}),D=(0,i.xd)(),P=(0,c.C)(),I=(0,u.X)({styleProps:{gap:x,rowGap:C,columnGap:S,align:_,justify:A,wrap:E,direction:j},theme:D,data:h});return(0,n.jsxs)(n.Fragment,{children:[I.hasResponsiveStyles&&(0,n.jsx)(l.K,{selector:".".concat(P),styles:I.styles,media:I.media}),(0,n.jsx)(d.a,{ref:t,...O("root",{className:P,style:(0,o.J)(I.inlineStyles)}),...k})]})});m.classes=p,m.displayName="@mantine/core/Flex"},1616:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(!e(t[o],r[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(i=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(r,i[o]))return!1;for(o=n;0!=o--;){var n,o,i,a=i[o];if(!e(t[a],r[a]))return!1}return!0}return t!=t&&r!=r}},1659:(e,t,r)=>{"use strict";r.d(t,{m:()=>C});var n=r(2115);let o="undefined"!=typeof window?n.useLayoutEffect:n.useEffect;function i(e,t){o(()=>{if(e)return window.addEventListener(e,t),()=>window.removeEventListener(e,t)},[e])}function a(e){return null===e||"object"!=typeof e?{}:Object.keys(e).reduce((t,r)=>{let n=e[r];return null!=n&&!1!==n&&(t[r]=n),t},{})}function s(e,t){if(null===t||"object"!=typeof t)return{};let r={...t};return Object.keys(t).forEach(t=>{t.includes("".concat(String(e),"."))&&delete r[t]}),r}function l(e,t){return parseInt(e.substring(t.length+1).split(".")[0],10)}function u(e,t,r,n){if(void 0===t)return r;let o="".concat(String(e)),i=r;-1===n&&(i=s("".concat(o,".").concat(t),i));let a={...i},u=new Set;return Object.entries(i).filter(e=>{let[r]=e;if(!r.startsWith("".concat(o,".")))return!1;let n=l(r,o);return!Number.isNaN(n)&&n>=t}).forEach(e=>{let[t,r]=e,i=l(t,o),s=t.replace("".concat(o,".").concat(i),"".concat(o,".").concat(i+n));a[s]=r,u.add(s),u.has(t)||delete a[t]}),a}function c(e){return"string"!=typeof e?[]:e.split(".")}function d(e,t){let r=c(e);if(0===r.length||"object"!=typeof t||null===t)return;let n=t[r[0]];for(let e=1;e<r.length&&null!=n;e+=1)n=n[r[e]];return n}function f(e,t,r){"object"==typeof r.value&&(r.value=h(r.value)),r.enumerable&&!r.get&&!r.set&&r.configurable&&r.writable&&"__proto__"!==t?e[t]=r.value:Object.defineProperty(e,t,r)}function h(e){if("object"!=typeof e)return e;var t,r,n,o=0,i=Object.prototype.toString.call(e);if("[object Object]"===i?n=Object.create(e.__proto__||null):"[object Array]"===i?n=Array(e.length):"[object Set]"===i?(n=new Set,e.forEach(function(e){n.add(h(e))})):"[object Map]"===i?(n=new Map,e.forEach(function(e,t){n.set(h(t),h(e))})):"[object Date]"===i?n=new Date(+e):"[object RegExp]"===i?n=new RegExp(e.source,e.flags):"[object DataView]"===i?n=new e.constructor(h(e.buffer)):"[object ArrayBuffer]"===i?n=e.slice(0):"Array]"===i.slice(-6)&&(n=new e.constructor(e)),n){for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)f(n,r[o],Object.getOwnPropertyDescriptor(e,r[o]));for(o=0,r=Object.getOwnPropertyNames(e);o<r.length;o++)Object.hasOwnProperty.call(n,t=r[o])&&n[t]===e[t]||f(n,t,Object.getOwnPropertyDescriptor(e,t))}return n||e}function p(e,t,r){let n=c(e);if(0===n.length)return r;let o=h(r);if(1===n.length)return o[n[0]]=t,o;let i=o[n[0]];for(let e=1;e<n.length-1;e+=1){if(void 0===i)return o;i=i[n[e]]}return i[n[n.length-1]]=t,o}var g=r(1616);function m(e,t){let r=Object.keys(e);if("string"==typeof t){let n=r.filter(e=>e.startsWith("".concat(t,".")));return e[t]||n.some(t=>e[t])||!1}return r.some(t=>e[t])}function v(e,t){return e?"".concat(e,"-").concat(t.toString()):t.toString()}function y(e){let t=a(e);return{hasErrors:Object.keys(t).length>0,errors:t}}function b(e,t){return"function"==typeof e?y(e(t)):y(function e(t,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return"object"!=typeof t||null===t?o:Object.keys(t).reduce((o,i)=>{let a=t[i],s="".concat(""===n?"":"".concat(n,".")).concat(i),l=d(s,r),u=!1;return"function"==typeof a&&(o[s]=a(l,r,s)),"object"==typeof a&&Array.isArray(l)&&(u=!0,l.forEach((t,n)=>e(a,r,"".concat(s,".").concat(n),o))),"object"!=typeof a||"object"!=typeof l||null===l||u||e(a,r,s,o),o},o)}(e,t))}function w(e,t,r){if("string"!=typeof e)return{hasError:!1,error:null};let n=b(t,r),o=Object.keys(n.errors).find(t=>e.split(".").every((e,r)=>e===t.split(".")[r]));return{hasError:!!o,error:o?n.errors[o]:null}}function x(e,t){return!!t&&("boolean"==typeof t?t:!!Array.isArray(t)&&t.includes(e.replace(/[.][0-9]+/g,".".concat("__MANTINE_FORM_INDEX__"))))}function C(){let{name:e,mode:t="controlled",initialValues:r,initialErrors:o={},initialDirty:l={},initialTouched:c={},clearInputErrorOnChange:f=!0,validateInputOnChange:h=!1,validateInputOnBlur:y=!1,onValuesChange:C,transformValues:S=e=>e,enhanceGetInputProps:_,validate:A,onSubmitPreventDefault:E="always",touchTrigger:j="change"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},k=function(e){let[t,r]=(0,n.useState)(a(e)),o=(0,n.useRef)(t),i=(0,n.useCallback)(e=>{r(t=>{let r=a("function"==typeof e?e(t):e);return o.current=r,r})},[]),s=(0,n.useCallback)(()=>i({}),[]),l=(0,n.useCallback)(e=>{void 0!==o.current[e]&&i(t=>{let r={...t};return delete r[e],r})},[t]),u=(0,n.useCallback)((e,t)=>{null==t||!1===t?l(e):o.current[e]!==t&&i(r=>({...r,[e]:t}))},[t]);return{errorsState:t,setErrors:i,clearErrors:s,setFieldError:u,clearFieldError:l}}(o),O=function(e){let{initialValues:t,onValuesChange:r,mode:o}=e,i=(0,n.useRef)(!1),[a,s]=(0,n.useState)(t||{}),l=(0,n.useRef)(a),u=(0,n.useRef)(a),c=(0,n.useCallback)(e=>{let{values:t,subscribers:n,updateState:o=!0,mergeWithPreviousValues:i=!0}=e,a=l.current,u=t instanceof Function?t(l.current):t,c=i?{...a,...u}:u;l.current=c,o&&s(c),null==r||r(c,a),null==n||n.filter(Boolean).forEach(e=>e({updatedValues:c,previousValues:a}))},[r]),f=(0,n.useCallback)(e=>{let t=d(e.path,l.current),r=e.value instanceof Function?e.value(t):e.value;if(t!==r){var n;let t=l.current,o=p(e.path,r,l.current);c({values:o,updateState:e.updateState}),null==(n=e.subscribers)||n.filter(Boolean).forEach(r=>r({path:e.path,updatedValues:o,previousValues:t}))}},[c]),h=(0,n.useCallback)(e=>{u.current=e},[]),g=(0,n.useCallback)((e,t)=>{i.current||(i.current=!0,c({values:e,updateState:"controlled"===o}),h(e),t())},[c]),m=(0,n.useCallback)(()=>{c({values:u.current,updateState:!0,mergeWithPreviousValues:!1})},[c]),v=(0,n.useCallback)(()=>l.current,[]),y=(0,n.useCallback)(()=>u.current,[]);return{initialized:i,stateValues:a,refValues:l,valuesSnapshot:u,setValues:c,setFieldValue:f,resetValues:m,setValuesSnapshot:h,initialize:g,getValues:v,getValuesSnapshot:y}}({initialValues:r,onValuesChange:C,mode:t}),D=function(e){let{initialDirty:t,initialTouched:r,mode:o,$values:i}=e,[a,l]=(0,n.useState)(r),[u,c]=(0,n.useState)(t),f=(0,n.useRef)(r),h=(0,n.useRef)(t),p=(0,n.useCallback)(e=>{let t="function"==typeof e?e(f.current):e;f.current=t,"controlled"===o&&l(t)},[]),v=(0,n.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r="function"==typeof e?e(h.current):e;h.current=r,("controlled"===o||t)&&c(r)},[]),y=(0,n.useCallback)(()=>p({}),[]),b=(0,n.useCallback)(e=>{let t=e?{...e,...i.refValues.current}:i.refValues.current;i.setValuesSnapshot(t),v({})},[]),w=(0,n.useCallback)((e,t)=>{p(r=>m(r,e)===t?r:{...r,[e]:t})},[]),x=(0,n.useCallback)((e,t,r)=>{v(r=>m(r,e)===t?r:{...r,[e]:t},r)},[]),C=(0,n.useCallback)((e,t)=>{let r=m(h.current,e),n=!g(d(e,i.getValuesSnapshot()),t),o=s(e,h.current);o[e]=n,v(o,r!==n)},[]),S=(0,n.useCallback)(e=>m(f.current,e),[]),_=(0,n.useCallback)(e=>v(t=>{if("string"!=typeof e)return t;let r=s(e,t);return(delete r[e],g(r,t))?t:r}),[]),A=(0,n.useCallback)(e=>{if(e){let t=d(e,h.current);return"boolean"==typeof t?t:!g(d(e,i.refValues.current),d(e,i.valuesSnapshot.current))}return Object.keys(h.current).length>0?m(h.current):!g(i.refValues.current,i.valuesSnapshot.current)},[]),E=(0,n.useCallback)(()=>h.current,[]),j=(0,n.useCallback)(()=>f.current,[]);return{touchedState:a,dirtyState:u,touchedRef:f,dirtyRef:h,setTouched:p,setDirty:v,resetDirty:b,resetTouched:y,isTouched:S,setFieldTouched:w,setFieldDirty:x,setTouchedState:l,setDirtyState:c,clearFieldDirty:_,isDirty:A,getDirty:E,getTouched:j,setCalculatedFieldDirty:C}}({initialDirty:l,initialTouched:c,$values:O,mode:t}),P=function(e){let{$values:t,$errors:r,$status:o}=e,i=(0,n.useCallback)((e,n)=>{o.clearFieldDirty(e),r.setErrors(t=>(function(e,t,r){let{from:n,to:o}=t,i="".concat(e,".").concat(n),a="".concat(e,".").concat(o),s={...r};return Object.keys(r).every(e=>{let t,r;if(e.startsWith(i)&&(t=e,r=e.replace(i,a)),e.startsWith(a)&&(t=e.replace(a,i),r=e),t&&r){let e=s[t],n=s[r];return void 0===n?delete s[t]:s[t]=n,void 0===e?delete s[r]:s[r]=e,!1}return!0}),s})(e,n,t)),t.setValues({values:function(e,t,r){let{from:n,to:o}=t,i=d(e,r);if(!Array.isArray(i))return r;let a=[...i],s=i[n];return a.splice(n,1),a.splice(o,0,s),p(e,a,r)}(e,n,t.refValues.current),updateState:!0})},[]),a=(0,n.useCallback)((e,n)=>{o.clearFieldDirty(e),r.setErrors(t=>u(e,n,t,-1)),t.setValues({values:function(e,t,r){let n=d(e,r);return Array.isArray(n)?p(e,n.filter((e,r)=>r!==t),r):r}(e,n,t.refValues.current),updateState:!0})},[]);return{reorderListItem:i,removeListItem:a,insertListItem:(0,n.useCallback)((e,n,i)=>{o.clearFieldDirty(e),r.setErrors(t=>u(e,i,t,1)),t.setValues({values:function(e,t,r,n){let o=d(e,n);if(!Array.isArray(o))return n;let i=[...o];return i.splice("number"==typeof r?r:i.length,0,t),p(e,i,n)}(e,n,i,t.refValues.current),updateState:!0})},[]),replaceListItem:(0,n.useCallback)((e,r,n)=>{o.clearFieldDirty(e),t.setValues({values:function(e,t,r,n){let o=d(e,n);if(!Array.isArray(o)||o.length<=r)return n;let i=[...o];return i[r]=t,p(e,i,n)}(e,n,r,t.refValues.current),updateState:!0})},[])}}({$values:O,$errors:k,$status:D}),I=function(e){let{$status:t}=e,r=(0,n.useRef)({}),o=(0,n.useCallback)((e,t)=>{(0,n.useEffect)(()=>(r.current[e]=r.current[e]||[],r.current[e].push(t),()=>{r.current[e]=r.current[e].filter(e=>e!==t)}),[t])},[]),i=(0,n.useCallback)(e=>r.current[e]?r.current[e].map(r=>n=>r({previousValue:d(e,n.previousValues),value:d(e,n.updatedValues),touched:t.isTouched(e),dirty:t.isDirty(e)})):[],[]);return{subscribers:r,watch:o,getFieldSubscribers:i}}({$status:D}),[R,T]=(0,n.useState)(0),[L,M]=(0,n.useState)({}),[N,z]=(0,n.useState)(!1),B=(0,n.useCallback)(()=>{O.resetValues(),k.clearErrors(),D.resetDirty(),D.resetTouched(),"uncontrolled"===t&&T(e=>e+1)},[]),Y=(0,n.useCallback)(e=>{f&&k.clearErrors(),"uncontrolled"===t&&T(e=>e+1),Object.keys(I.subscribers.current).forEach(t=>{d(t,O.refValues.current)!==d(t,e)&&I.getFieldSubscribers(t).forEach(t=>t({previousValues:e,updatedValues:O.refValues.current}))})},[f]),F=(0,n.useCallback)(e=>{let r=O.refValues.current;O.initialize(e,()=>"uncontrolled"===t&&T(e=>e+1)),Y(r)},[Y]),$=(0,n.useCallback)((e,r,n)=>{let o=x(e,h),i=r instanceof Function?r(d(e,O.refValues.current)):r;D.setCalculatedFieldDirty(e,i),"change"===j&&D.setFieldTouched(e,!0),!o&&f&&k.clearFieldError(e),O.setFieldValue({path:e,value:r,updateState:"controlled"===t,subscribers:[...I.getFieldSubscribers(e),o?t=>{let r=w(e,A,t.updatedValues);r.hasError?k.setFieldError(e,r.error):k.clearFieldError(e)}:null,(null==n?void 0:n.forceUpdate)!==!1&&"controlled"!==t?()=>M(t=>({...t,[e]:(t[e]||0)+1})):null]})},[C,A]),q=(0,n.useCallback)(e=>{let r=O.refValues.current;O.setValues({values:e,updateState:"controlled"===t}),Y(r)},[C,Y]),V=(0,n.useCallback)(()=>{let e=b(A,O.refValues.current);return k.setErrors(e.errors),e},[A]),U=(0,n.useCallback)(e=>{let t=w(e,A,O.refValues.current);return t.hasError?k.setFieldError(e,t.error):k.clearFieldError(e),t},[A]),H=(0,n.useCallback)(e=>{e.preventDefault(),B()},[]),G=(0,n.useCallback)(e=>e?!w(e,A,O.refValues.current).hasError:!b(A,O.refValues.current).hasErrors,[A]),W=(0,n.useCallback)(t=>document.querySelector('[data-path="'.concat(v(e,t),'"]')),[]),X={watch:I.watch,initialized:O.initialized.current,values:O.stateValues,getValues:O.getValues,setInitialValues:O.setValuesSnapshot,initialize:F,setValues:q,setFieldValue:$,submitting:N,setSubmitting:z,errors:k.errorsState,setErrors:k.setErrors,setFieldError:k.setFieldError,clearFieldError:k.clearFieldError,clearErrors:k.clearErrors,resetDirty:D.resetDirty,setTouched:D.setTouched,setDirty:D.setDirty,isTouched:D.isTouched,resetTouched:D.resetTouched,isDirty:D.isDirty,getTouched:D.getTouched,getDirty:D.getDirty,reorderListItem:P.reorderListItem,insertListItem:P.insertListItem,removeListItem:P.removeListItem,replaceListItem:P.replaceListItem,reset:B,validate:V,validateField:U,getInputProps:function(r){var n;let{type:o="input",withError:i=!0,withFocus:a=!0,...s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l={onChange:(n=e=>$(r,e,{forceUpdate:!1}),e=>{if(e)if("function"==typeof e)n(e);else if("object"==typeof e&&"nativeEvent"in e){let{currentTarget:t}=e;t instanceof HTMLInputElement?"checkbox"===t.type?n(t.checked):n(t.value):(t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement)&&n(t.value)}else n(e);else n(e)}),"data-path":v(e,r)};return i&&(l.error=k.errorsState[r]),"checkbox"===o?l["controlled"===t?"checked":"defaultChecked"]=d(r,O.refValues.current):l["controlled"===t?"value":"defaultValue"]=d(r,O.refValues.current),a&&(l.onFocus=()=>D.setFieldTouched(r,!0),l.onBlur=()=>{if(x(r,y)){let e=w(r,A,O.refValues.current);e.hasError?k.setFieldError(r,e.error):k.clearFieldError(r)}}),Object.assign(l,null==_?void 0:_({inputProps:l,field:r,options:{type:o,withError:i,withFocus:a,...s},form:X}))},onSubmit:(e,t)=>r=>{"always"===E&&(null==r||r.preventDefault());let n=V();if(n.hasErrors)"validation-failed"===E&&(null==r||r.preventDefault()),null==t||t(n.errors,O.refValues.current,r);else{let t=null==e?void 0:e(S(O.refValues.current),r);t instanceof Promise&&(z(!0),t.finally(()=>z(!1)))}},onReset:H,isValid:G,getTransformedValues:e=>S(e||O.refValues.current),key:e=>"".concat(R,"-").concat(e,"-").concat(L[e]||0),getInputNode:W};return e&&function(e){if(!/^[0-9a-zA-Z-]+$/.test(e))throw Error('[@mantine/use-form] Form name "'.concat(e,'" is invalid, it should contain only letters, numbers and dashes'))}(e),i("mantine-form:".concat(e,":set-field-value"),e=>X.setFieldValue(e.detail.path,e.detail.value)),i("mantine-form:".concat(e,":set-values"),e=>X.setValues(e.detail)),i("mantine-form:".concat(e,":set-initial-values"),e=>X.setInitialValues(e.detail)),i("mantine-form:".concat(e,":set-errors"),e=>X.setErrors(e.detail)),i("mantine-form:".concat(e,":set-field-error"),e=>X.setFieldError(e.detail.path,e.detail.error)),i("mantine-form:".concat(e,":clear-field-error"),e=>X.clearFieldError(e.detail)),i("mantine-form:".concat(e,":clear-errors"),X.clearErrors),i("mantine-form:".concat(e,":reset"),X.reset),i("mantine-form:".concat(e,":validate"),X.validate),i("mantine-form:".concat(e,":validate-field"),e=>X.validateField(e.detail)),i("mantine-form:".concat(e,":reorder-list-item"),e=>X.reorderListItem(e.detail.path,e.detail.payload)),i("mantine-form:".concat(e,":remove-list-item"),e=>X.removeListItem(e.detail.path,e.detail.index)),i("mantine-form:".concat(e,":insert-list-item"),e=>X.insertListItem(e.detail.path,e.detail.item,e.detail.index)),i("mantine-form:".concat(e,":set-dirty"),e=>X.setDirty(e.detail)),i("mantine-form:".concat(e,":set-touched"),e=>X.setTouched(e.detail)),i("mantine-form:".concat(e,":reset-dirty"),e=>X.resetDirty(e.detail)),i("mantine-form:".concat(e,":reset-touched"),X.resetTouched),X}},2200:(e,t,r)=>{"use strict";r.d(t,{x:()=>o});var n=r(2115);function o(e){var t;let r=n.version;return"string"!=typeof n.version||r.startsWith("18.")?null==e?void 0:e.ref:null==e||null==(t=e.props)?void 0:t.ref}},2213:(e,t,r)=>{"use strict";r.d(t,{e:()=>d});var n=r(5155);let o=e=>({in:{opacity:1,transform:"scale(1)"},out:{opacity:0,transform:"scale(.9) translateY(".concat("bottom"===e?10:-10,"px)")},transitionProperty:"transform, opacity"}),i={fade:{in:{opacity:1},out:{opacity:0},transitionProperty:"opacity"},"fade-up":{in:{opacity:1,transform:"translateY(0)"},out:{opacity:0,transform:"translateY(30px)"},transitionProperty:"opacity, transform"},"fade-down":{in:{opacity:1,transform:"translateY(0)"},out:{opacity:0,transform:"translateY(-30px)"},transitionProperty:"opacity, transform"},"fade-left":{in:{opacity:1,transform:"translateX(0)"},out:{opacity:0,transform:"translateX(30px)"},transitionProperty:"opacity, transform"},"fade-right":{in:{opacity:1,transform:"translateX(0)"},out:{opacity:0,transform:"translateX(-30px)"},transitionProperty:"opacity, transform"},scale:{in:{opacity:1,transform:"scale(1)"},out:{opacity:0,transform:"scale(0)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"scale-y":{in:{opacity:1,transform:"scaleY(1)"},out:{opacity:0,transform:"scaleY(0)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"scale-x":{in:{opacity:1,transform:"scaleX(1)"},out:{opacity:0,transform:"scaleX(0)"},common:{transformOrigin:"left"},transitionProperty:"transform, opacity"},"skew-up":{in:{opacity:1,transform:"translateY(0) skew(0deg, 0deg)"},out:{opacity:0,transform:"translateY(-20px) skew(-10deg, -5deg)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"skew-down":{in:{opacity:1,transform:"translateY(0) skew(0deg, 0deg)"},out:{opacity:0,transform:"translateY(20px) skew(-10deg, -5deg)"},common:{transformOrigin:"bottom"},transitionProperty:"transform, opacity"},"rotate-left":{in:{opacity:1,transform:"translateY(0) rotate(0deg)"},out:{opacity:0,transform:"translateY(20px) rotate(-5deg)"},common:{transformOrigin:"bottom"},transitionProperty:"transform, opacity"},"rotate-right":{in:{opacity:1,transform:"translateY(0) rotate(0deg)"},out:{opacity:0,transform:"translateY(20px) rotate(5deg)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"slide-down":{in:{opacity:1,transform:"translateY(0)"},out:{opacity:0,transform:"translateY(-100%)"},common:{transformOrigin:"top"},transitionProperty:"transform, opacity"},"slide-up":{in:{opacity:1,transform:"translateY(0)"},out:{opacity:0,transform:"translateY(100%)"},common:{transformOrigin:"bottom"},transitionProperty:"transform, opacity"},"slide-left":{in:{opacity:1,transform:"translateX(0)"},out:{opacity:0,transform:"translateX(100%)"},common:{transformOrigin:"left"},transitionProperty:"transform, opacity"},"slide-right":{in:{opacity:1,transform:"translateX(0)"},out:{opacity:0,transform:"translateX(-100%)"},common:{transformOrigin:"right"},transitionProperty:"transform, opacity"},pop:{...o("bottom"),common:{transformOrigin:"center center"}},"pop-bottom-left":{...o("bottom"),common:{transformOrigin:"bottom left"}},"pop-bottom-right":{...o("bottom"),common:{transformOrigin:"bottom right"}},"pop-top-left":{...o("top"),common:{transformOrigin:"top left"}},"pop-top-right":{...o("top"),common:{transformOrigin:"top right"}}},a={entering:"in",entered:"in",exiting:"out",exited:"out","pre-exiting":"out","pre-entering":"out"};var s=r(2115),l=r(7650),u=r(4237),c=r(3131);function d(e){let{keepMounted:t,transition:r="fade",duration:o=250,exitDuration:d=o,mounted:f,children:h,timingFunction:p="ease",onExit:g,onEntered:m,onEnter:v,onExited:y,enterDelay:b,exitDelay:w}=e,{transitionDuration:x,transitionStatus:C,transitionTimingFunction:S}=function(e){let{duration:t,exitDuration:r,timingFunction:n,mounted:o,onEnter:i,onExit:a,onEntered:d,onExited:f,enterDelay:h,exitDelay:p}=e,g=(0,c.xd)(),m=function(e,t){let{getInitialValueInEffect:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{getInitialValueInEffect:!0},[n,o]=(0,s.useState)(r?t:!!("undefined"!=typeof window&&"matchMedia"in window)&&window.matchMedia(e).matches),i=(0,s.useRef)(null);return(0,s.useEffect)(()=>{if("matchMedia"in window){i.current=window.matchMedia(e),o(i.current.matches);var t=i.current,r=e=>o(e.matches);try{return t.addEventListener("change",r),()=>t.removeEventListener("change",r)}catch(e){return t.addListener(r),()=>t.removeListener(r)}}},[e]),n}("(prefers-reduced-motion: reduce)",void 0,void 0),v=!!g.respectReducedMotion&&m,[y,b]=(0,s.useState)(v?0:t),[w,x]=(0,s.useState)(o?"entered":"exited"),C=(0,s.useRef)(-1),S=(0,s.useRef)(-1),_=(0,s.useRef)(-1),A=e=>{let n=e?i:a,o=e?d:f;window.clearTimeout(C.current);let s=v?0:e?t:r;b(s),0===s?("function"==typeof n&&n(),"function"==typeof o&&o(),x(e?"entered":"exited")):_.current=requestAnimationFrame(()=>{l.flushSync(()=>{x(e?"pre-entering":"pre-exiting")}),_.current=requestAnimationFrame(()=>{"function"==typeof n&&n(),x(e?"entering":"exiting"),C.current=window.setTimeout(()=>{"function"==typeof o&&o(),x(e?"entered":"exited")},s)})})};return(0,u.C)(()=>{(e=>{if(window.clearTimeout(S.current),"number"!=typeof(e?h:p))return A(e);S.current=window.setTimeout(()=>{A(e)},e?h:p)})(o)},[o]),(0,s.useEffect)(()=>()=>{window.clearTimeout(C.current),cancelAnimationFrame(_.current)},[]),{transitionDuration:y,transitionStatus:w,transitionTimingFunction:n||"ease"}}({mounted:f,exitDuration:d,duration:o,timingFunction:p,onExit:g,onEntered:m,onEnter:v,onExited:y,enterDelay:b,exitDelay:w});return 0===x?f?(0,n.jsx)(n.Fragment,{children:h({})}):t?h({display:"none"}):null:"exited"===C?t?h({display:"none"}):null:(0,n.jsx)(n.Fragment,{children:h(function(e){let{transition:t,state:r,duration:n,timingFunction:o}=e,s={transitionDuration:"".concat(n,"ms"),transitionTimingFunction:o};return"string"==typeof t?t in i?{transitionProperty:i[t].transitionProperty,...s,...i[t].common,...i[t][a[r]]}:{}:{transitionProperty:t.transitionProperty,...s,...t.common,...t[a[r]]}}({transition:r,duration:x,state:C,timingFunction:S}))})}d.displayName="@mantine/core/Transition"},3141:(e,t,r)=>{"use strict";r.d(t,{o:()=>o});var n=r(2115);let o="undefined"!=typeof document?n.useLayoutEffect:n.useEffect},3191:(e,t,r)=>{"use strict";r.d(t,{M:()=>_});var n=r(5155);r(2115);var o=r(6204),i=r(8918),a=r(3664),s=r(862),l=r(311),u=r(4511),c=r(3347),d=r(2213),f=r(3608),h=r(5903),p=r(6960),g={root:"m_8d3f4000",icon:"m_8d3afb97",loader:"m_302b9fb1",group:"m_1a0f1b21",groupSection:"m_437b6484"};let m={orientation:"horizontal"},v=(0,i.V)((e,t)=>{let{borderWidth:r}=t;return{group:{"--ai-border-width":(0,h.D)(r)}}}),y=(0,p.P9)((e,t)=>{let r=(0,a.Y)("ActionIconGroup",m,e),{className:o,style:i,classNames:u,styles:c,unstyled:d,orientation:f,vars:h,borderWidth:p,variant:y,mod:b,...w}=(0,a.Y)("ActionIconGroup",m,e),x=(0,s.I)({name:"ActionIconGroup",props:r,classes:g,className:o,style:i,classNames:u,styles:c,unstyled:d,vars:h,varsResolver:v,rootSelector:"group"});return(0,n.jsx)(l.a,{...x("group"),ref:t,variant:y,mod:[{"data-orientation":f},b],role:"group",...w})});y.classes=g,y.displayName="@mantine/core/ActionIconGroup";let b={},w=(0,i.V)((e,t)=>{let{radius:r,color:n,gradient:i,variant:a,autoContrast:s,size:l}=t,u=e.variantColorResolver({color:n||e.primaryColor,theme:e,gradient:i,variant:a||"filled",autoContrast:s});return{groupSection:{"--section-height":(0,o.YC)(l,"section-height"),"--section-padding-x":(0,o.YC)(l,"section-padding-x"),"--section-fz":(0,o.ny)(l),"--section-radius":void 0===r?void 0:(0,o.nJ)(r),"--section-bg":n||a?u.background:void 0,"--section-color":u.color,"--section-bd":n||a?u.border:void 0}}}),x=(0,p.P9)((e,t)=>{let r=(0,a.Y)("ActionIconGroupSection",b,e),{className:o,style:i,classNames:u,styles:c,unstyled:d,vars:f,variant:h,gradient:p,radius:m,autoContrast:v,...y}=(0,a.Y)("ActionIconGroupSection",b,e),x=(0,s.I)({name:"ActionIconGroupSection",props:r,classes:g,className:o,style:i,classNames:u,styles:c,unstyled:d,vars:f,varsResolver:w,rootSelector:"groupSection"});return(0,n.jsx)(l.a,{...x("groupSection"),ref:t,variant:h,...y})});x.classes=g,x.displayName="@mantine/core/ActionIconGroupSection";let C={},S=(0,i.V)((e,t)=>{let{size:r,radius:n,variant:i,gradient:a,color:s,autoContrast:l}=t,u=e.variantColorResolver({color:s||e.primaryColor,theme:e,gradient:a,variant:i||"filled",autoContrast:l});return{root:{"--ai-size":(0,o.YC)(r,"ai-size"),"--ai-radius":void 0===n?void 0:(0,o.nJ)(n),"--ai-bg":s||i?u.background:void 0,"--ai-hover":s||i?u.hover:void 0,"--ai-hover-color":s||i?u.hoverColor:void 0,"--ai-color":u.color,"--ai-bd":s||i?u.border:void 0}}}),_=(0,u.v)((e,t)=>{let r=(0,a.Y)("ActionIcon",C,e),{className:o,unstyled:i,variant:u,classNames:h,styles:p,style:m,loading:v,loaderProps:y,size:b,color:w,radius:x,__staticSelector:_,gradient:A,vars:E,children:j,disabled:k,"data-disabled":O,autoContrast:D,mod:P,...I}=r,R=(0,s.I)({name:["ActionIcon",_],props:r,className:o,style:m,classes:g,classNames:h,styles:p,unstyled:i,vars:E,varsResolver:S});return(0,n.jsxs)(f.N,{...R("root",{active:!k&&!v&&!O}),...I,unstyled:i,variant:u,size:b,disabled:k||v,ref:t,mod:[{loading:v,disabled:k||O},P],children:[(0,n.jsx)(d.e,{mounted:!!v,transition:"slide-down",duration:150,children:e=>(0,n.jsx)(l.a,{component:"span",...R("loader",{style:e}),"aria-hidden":!0,children:(0,n.jsx)(c.a,{color:"var(--ai-color)",size:"calc(var(--ai-size) * 0.55)",...y})})}),(0,n.jsx)(l.a,{component:"span",mod:{loading:v},...R("icon"),children:j})]})});_.classes=g,_.displayName="@mantine/core/ActionIcon",_.Group=y,_.GroupSection=x},3288:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e?"base"in e?e.base:void 0:e}r.d(t,{D:()=>n})},3304:(e,t,r)=>{"use strict";r.d(t,{jH:()=>i}),r(5155);var n=r(2115);let o=(0,n.createContext)({dir:"ltr",toggleDirection:()=>{},setDirection:()=>{}});function i(){return(0,n.useContext)(o)}},3347:(e,t,r)=>{"use strict";r.d(t,{a:()=>w});var n=r(5155),o=r(2115),i=r(6204),a=r(8918),s=r(1180),l=r(3664),u=r(862),c=r(311),d=r(6960),f=r(2596),h={root:"m_5ae2e3c",barsLoader:"m_7a2bd4cd",bar:"m_870bb79","bars-loader-animation":"m_5d2b3b9d",dotsLoader:"m_4e3f22d7",dot:"m_870c4af","loader-dots-animation":"m_aac34a1",ovalLoader:"m_b34414df","oval-loader-animation":"m_f8e89c4b"};let p=(0,o.forwardRef)((e,t)=>{let{className:r,...o}=e;return(0,n.jsxs)(c.a,{component:"span",className:(0,f.A)(h.barsLoader,r),...o,ref:t,children:[(0,n.jsx)("span",{className:h.bar}),(0,n.jsx)("span",{className:h.bar}),(0,n.jsx)("span",{className:h.bar})]})});p.displayName="@mantine/core/Bars";let g=(0,o.forwardRef)((e,t)=>{let{className:r,...o}=e;return(0,n.jsxs)(c.a,{component:"span",className:(0,f.A)(h.dotsLoader,r),...o,ref:t,children:[(0,n.jsx)("span",{className:h.dot}),(0,n.jsx)("span",{className:h.dot}),(0,n.jsx)("span",{className:h.dot})]})});g.displayName="@mantine/core/Dots";let m=(0,o.forwardRef)((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(c.a,{component:"span",className:(0,f.A)(h.ovalLoader,r),...o,ref:t})});m.displayName="@mantine/core/Oval";let v={bars:p,oval:m,dots:g},y={loaders:v,type:"oval"},b=(0,a.V)((e,t)=>{let{size:r,color:n}=t;return{root:{"--loader-size":(0,i.YC)(r,"loader-size"),"--loader-color":n?(0,s.r)(n,e):void 0}}}),w=(0,d.P9)((e,t)=>{let r=(0,l.Y)("Loader",y,e),{size:o,color:i,type:a,vars:s,className:d,style:f,classNames:p,styles:g,unstyled:m,loaders:v,variant:w,children:x,...C}=r,S=(0,u.I)({name:"Loader",props:r,classes:h,className:d,style:f,classNames:p,styles:g,unstyled:m,vars:s,varsResolver:b});return x?(0,n.jsx)(c.a,{...S("root"),ref:t,...C,children:x}):(0,n.jsx)(c.a,{...S("root"),ref:t,component:v[a],variant:w,size:o,...C})});w.defaultLoaders=v,w.classes=h,w.displayName="@mantine/core/Loader"},3608:(e,t,r)=>{"use strict";r.d(t,{N:()=>c});var n=r(5155);r(2115);var o=r(3664),i=r(862),a=r(311),s=r(4511),l={root:"m_87cf2631"};let u={__staticSelector:"UnstyledButton"},c=(0,s.v)((e,t)=>{let r=(0,o.Y)("UnstyledButton",u,e),{className:s,component:c="button",__staticSelector:d,unstyled:f,classNames:h,styles:p,style:g,...m}=r,v=(0,i.I)({name:d,props:r,classes:l,className:s,style:g,classNames:h,styles:p,unstyled:f});return(0,n.jsx)(a.a,{...v("root",{focusable:!0}),component:c,ref:t,type:"button"===c?"button":void 0,...m})});c.classes=l,c.displayName="@mantine/core/UnstyledButton"},3746:(e,t,r)=>{"use strict";r.d(t,{I:()=>i}),r(2115),r(5155);var n=r(3664),o=r(9537);function i(e,t,r){let i=(0,n.Y)(e,t,r),{label:a,description:s,error:l,required:u,classNames:c,styles:d,className:f,unstyled:h,__staticSelector:p,__stylesApiProps:g,errorProps:m,labelProps:v,descriptionProps:y,wrapperProps:b,id:w,size:x,style:C,inputContainer:S,inputWrapperOrder:_,withAsterisk:A,variant:E,vars:j,mod:k,...O}=i,{styleProps:D,rest:P}=(0,o.j)(O),I={label:a,description:s,error:l,required:u,classNames:c,className:f,__staticSelector:p,__stylesApiProps:g||i,errorProps:m,labelProps:v,descriptionProps:y,unstyled:h,styles:d,size:x,style:C,inputContainer:S,inputWrapperOrder:_,withAsterisk:A,variant:E,id:w,mod:k,...b};return{...P,classNames:c,styles:d,unstyled:h,wrapperProps:{...I,...D},inputProps:{required:u,classNames:c,styles:d,unstyled:h,size:x,__staticSelector:p,__stylesApiProps:g||i,error:l,variant:E,id:w}}}},3751:(e,t,r)=>{"use strict";r.d(t,{h:()=>g});var n=r(5155);r(2115);var o=r(8918),i=r(3664),a=r(862),s=r(311),l=r(6960),u=r(5903);let c=["h1","h2","h3","h4","h5","h6"],d=["xs","sm","md","lg","xl"];var f={root:"m_8a5d1357"};let h={order:1},p=(0,o.V)((e,t)=>{let{order:r,size:n,lineClamp:o,textWrap:i}=t,a=function(e,t){let r=void 0!==t?t:"h".concat(e);return c.includes(r)?{fontSize:"var(--mantine-".concat(r,"-font-size)"),fontWeight:"var(--mantine-".concat(r,"-font-weight)"),lineHeight:"var(--mantine-".concat(r,"-line-height)")}:d.includes(r)?{fontSize:"var(--mantine-font-size-".concat(r,")"),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}:{fontSize:(0,u.D)(r),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}}(r,n);return{root:{"--title-fw":a.fontWeight,"--title-lh":a.lineHeight,"--title-fz":a.fontSize,"--title-line-clamp":"number"==typeof o?o.toString():void 0,"--title-text-wrap":i}}}),g=(0,l.P9)((e,t)=>{let r=(0,i.Y)("Title",h,e),{classNames:o,className:l,style:u,styles:c,unstyled:d,order:g,vars:m,size:v,variant:y,lineClamp:b,textWrap:w,mod:x,...C}=r,S=(0,a.I)({name:"Title",props:r,classes:f,className:l,style:u,classNames:o,styles:c,unstyled:d,vars:m,varsResolver:p});return[1,2,3,4,5,6].includes(g)?(0,n.jsx)(s.a,{...S("root"),component:"h".concat(g),variant:y,ref:t,mod:[{order:g,"data-line-clamp":"number"==typeof b},x],size:v,...C}):null});g.classes=f,g.displayName="@mantine/core/Title"},4225:(e,t,r)=>{"use strict";r.d(t,{O:()=>l});var n=r(5155);r(2115);var o=r(4511),i=r(8993),a=r(3746);let s={__staticSelector:"InputBase",withAria:!0},l=(0,o.v)((e,t)=>{let{inputProps:r,wrapperProps:o,...l}=(0,a.I)("InputBase",s,e);return(0,n.jsx)(i.p.Wrapper,{...o,children:(0,n.jsx)(i.p,{...r,...l,ref:t})})});l.classes={...i.p.classes,...i.p.Wrapper.classes},l.displayName="@mantine/core/InputBase"},4237:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var n=r(2115);function o(e,t){let r=(0,n.useRef)(!1);(0,n.useEffect)(()=>()=>{r.current=!1},[]),(0,n.useEffect)(()=>{if(r.current)return e();r.current=!0},t)}},4511:(e,t,r)=>{"use strict";r.d(t,{v:()=>a});var n=r(5155),o=r(2115),i=r(6960);function a(e){let t=(0,o.forwardRef)(e);return t.withProps=e=>{let r=(0,o.forwardRef)((r,o)=>(0,n.jsx)(t,{...e,...r,ref:o}));return r.extend=t.extend,r.displayName="WithProps(".concat(t.displayName,")"),r},t.extend=i.D_,t}},4534:(e,t,r)=>{"use strict";r.d(t,{l:()=>eU});var n=r(5155),o=r(2115),i=r(1513),a=r(7613),s=r(6028),l=r(3664),u=r(6960),c=r(5903),d=r(6204),f=r(8918),h=r(862),p=r(728),g=r(6970);let[m,v]=(0,g.F)("Combobox component was not found in tree");var y=r(311),b={dropdown:"m_88b62a41",search:"m_985517d8",options:"m_b2821a6e",option:"m_92253aa5",empty:"m_2530cd1d",header:"m_858f94bd",footer:"m_82b967cb",group:"m_254f3e4f",groupLabel:"m_2bb2e9e5",chevron:"m_2943220b",optionsDropdownOption:"m_390b5f4",optionsDropdownCheckIcon:"m_8ee53fc2"};let w={error:null},x=(0,f.V)((e,t)=>{let{size:r}=t;return{chevron:{"--combobox-chevron-size":(0,d.YC)(r,"combobox-chevron-size")}}}),C=(0,u.P9)((e,t)=>{let r=(0,l.Y)("ComboboxChevron",w,e),{size:o,error:i,style:a,className:s,classNames:u,styles:c,unstyled:d,vars:f,mod:p,...g}=r,m=(0,h.I)({name:"ComboboxChevron",classes:b,props:r,style:a,className:s,classNames:u,styles:c,unstyled:d,vars:f,varsResolver:x,rootSelector:"chevron"});return(0,n.jsx)(y.a,{component:"svg",...g,...m("chevron"),size:o,viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",mod:["combobox-chevron",{error:i},p],ref:t,children:(0,n.jsx)("path",{d:"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})});C.classes=b,C.displayName="@mantine/core/ComboboxChevron";var S=r(8993);let _=(0,o.forwardRef)((e,t)=>{let{size:r,onMouseDown:o,onClick:i,onClear:a,...s}=e;return(0,n.jsx)(S.p.ClearButton,{ref:t,tabIndex:-1,"aria-hidden":!0,...s,onMouseDown:e=>{e.preventDefault(),null==o||o(e)},onClick:e=>{a(),null==i||i(e)}})});_.displayName="@mantine/core/ComboboxClearButton";let A={},E=(0,u.P9)((e,t)=>{let{classNames:r,styles:o,className:i,style:a,hidden:s,...u}=(0,l.Y)("ComboboxDropdown",A,e),c=v();return(0,n.jsx)(p.A.Dropdown,{...u,ref:t,role:"presentation","data-hidden":s||void 0,...c.getStyles("dropdown",{className:i,style:a,classNames:r,styles:o})})});E.classes=b,E.displayName="@mantine/core/ComboboxDropdown";var j=r(866);let k={refProp:"ref"},O=(0,u.P9)((e,t)=>{let{children:r,refProp:o}=(0,l.Y)("ComboboxDropdownTarget",k,e);if(v(),!(0,j.v)(r))throw Error("Combobox.DropdownTarget component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");return(0,n.jsx)(p.A.Target,{ref:t,refProp:o,children:r})});O.displayName="@mantine/core/ComboboxDropdownTarget";let D={},P=(0,u.P9)((e,t)=>{let{classNames:r,className:o,style:i,styles:a,vars:s,...u}=(0,l.Y)("ComboboxEmpty",D,e),c=v();return(0,n.jsx)(y.a,{ref:t,...c.getStyles("empty",{className:o,classNames:r,styles:a,style:i}),...u})});P.classes=b,P.displayName="@mantine/core/ComboboxEmpty";var I=r(8551),R=r(2200);function T(e){let{onKeyDown:t,withKeyboardNavigation:r,withAriaAttributes:n,withExpandedAttribute:i,targetType:a,autoComplete:s}=e,l=v(),[u,c]=(0,o.useState)(null);return{...n?{"aria-haspopup":"listbox","aria-expanded":i&&!!(l.store.listId&&l.store.dropdownOpened)||void 0,"aria-controls":l.store.dropdownOpened?l.store.listId:void 0,"aria-activedescendant":l.store.dropdownOpened&&u||void 0,autoComplete:s,"data-expanded":l.store.dropdownOpened||void 0,"data-mantine-stop-propagation":l.store.dropdownOpened||void 0}:{},onKeyDown:e=>{if((null==t||t(e),!l.readOnly&&r)&&!e.nativeEvent.isComposing){if("ArrowDown"===e.nativeEvent.code&&(e.preventDefault(),l.store.dropdownOpened?c(l.store.selectNextOption()):(l.store.openDropdown("keyboard"),c(l.store.selectActiveOption()),l.store.updateSelectedOptionIndex("selected",{scrollIntoView:!0}))),"ArrowUp"===e.nativeEvent.code&&(e.preventDefault(),l.store.dropdownOpened?c(l.store.selectPreviousOption()):(l.store.openDropdown("keyboard"),c(l.store.selectActiveOption()),l.store.updateSelectedOptionIndex("selected",{scrollIntoView:!0}))),"Enter"===e.nativeEvent.code||"NumpadEnter"===e.nativeEvent.code){if(229===e.nativeEvent.keyCode)return;let t=l.store.getSelectedOptionIndex();l.store.dropdownOpened&&-1!==t?(e.preventDefault(),l.store.clickSelectedOption()):"button"===a&&(e.preventDefault(),l.store.openDropdown("keyboard"))}"Escape"===e.nativeEvent.code&&l.store.closeDropdown("keyboard"),"Space"===e.nativeEvent.code&&"button"===a&&(e.preventDefault(),l.store.toggleDropdown("keyboard"))}}}}let L={refProp:"ref",targetType:"input",withKeyboardNavigation:!0,withAriaAttributes:!0,withExpandedAttribute:!1,autoComplete:"off"},M=(0,u.P9)((e,t)=>{let{children:r,refProp:n,withKeyboardNavigation:i,withAriaAttributes:a,withExpandedAttribute:s,targetType:u,autoComplete:c,...d}=(0,l.Y)("ComboboxEventsTarget",L,e);if(!(0,j.v)(r))throw Error("Combobox.EventsTarget component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");let f=v(),h=T({targetType:u,withAriaAttributes:a,withKeyboardNavigation:i,withExpandedAttribute:s,onKeyDown:r.props.onKeyDown,autoComplete:c});return(0,o.cloneElement)(r,{...h,...d,[n]:(0,I.pc)(t,f.store.targetRef,(0,R.x)(r))})});M.displayName="@mantine/core/ComboboxEventsTarget";let N={},z=(0,u.P9)((e,t)=>{let{classNames:r,className:o,style:i,styles:a,vars:s,...u}=(0,l.Y)("ComboboxFooter",N,e),c=v();return(0,n.jsx)(y.a,{ref:t,...c.getStyles("footer",{className:o,classNames:r,style:i,styles:a}),...u,onMouseDown:e=>{e.preventDefault()}})});z.classes=b,z.displayName="@mantine/core/ComboboxFooter";let B={},Y=(0,u.P9)((e,t)=>{let{classNames:r,className:o,style:i,styles:a,vars:s,children:u,label:c,...d}=(0,l.Y)("ComboboxGroup",B,e),f=v();return(0,n.jsxs)(y.a,{ref:t,...f.getStyles("group",{className:o,classNames:r,style:i,styles:a}),...d,children:[c&&(0,n.jsx)("div",{...f.getStyles("groupLabel",{classNames:r,styles:a}),children:c}),u]})});Y.classes=b,Y.displayName="@mantine/core/ComboboxGroup";let F={},$=(0,u.P9)((e,t)=>{let{classNames:r,className:o,style:i,styles:a,vars:s,...u}=(0,l.Y)("ComboboxHeader",F,e),c=v();return(0,n.jsx)(y.a,{ref:t,...c.getStyles("header",{className:o,classNames:r,style:i,styles:a}),...u,onMouseDown:e=>{e.preventDefault()}})});function q(e){let{value:t,valuesDivider:r=",",...o}=e;return(0,n.jsx)("input",{type:"hidden",value:Array.isArray(t)?t.join(r):t||"",...o})}$.classes=b,$.displayName="@mantine/core/ComboboxHeader",q.displayName="@mantine/core/ComboboxHiddenInput";let V={},U=(0,u.P9)((e,t)=>{let r=(0,l.Y)("ComboboxOption",V,e),{classNames:i,className:a,style:s,styles:u,vars:c,onClick:d,id:f,active:h,onMouseDown:p,onMouseOver:g,disabled:m,selected:b,mod:w,...x}=r,C=v(),S=(0,o.useId)();return(0,n.jsx)(y.a,{...C.getStyles("option",{className:a,classNames:i,styles:u,style:s}),...x,ref:t,id:f||S,mod:["combobox-option",{"combobox-active":h,"combobox-disabled":m,"combobox-selected":b},w],role:"option",onClick:e=>{if(m)e.preventDefault();else{var t;null==(t=C.onOptionSubmit)||t.call(C,r.value,r),null==d||d(e)}},onMouseDown:e=>{e.preventDefault(),null==p||p(e)},onMouseOver:e=>{C.resetSelectionOnOptionHover&&C.store.resetSelectedOption(),null==g||g(e)}})});U.classes=b,U.displayName="@mantine/core/ComboboxOption";let H={},G=(0,u.P9)((e,t)=>{let{classNames:r,className:a,style:s,styles:u,id:c,onMouseDown:d,labelledBy:f,...h}=(0,l.Y)("ComboboxOptions",H,e),p=v(),g=(0,i.B)(c);return(0,o.useEffect)(()=>{p.store.setListId(g)},[g]),(0,n.jsx)(y.a,{ref:t,...p.getStyles("options",{className:a,style:s,classNames:r,styles:u}),...h,id:g,role:"listbox","aria-labelledby":f,onMouseDown:e=>{e.preventDefault(),null==d||d(e)}})});G.classes=b,G.displayName="@mantine/core/ComboboxOptions";let W={withAriaAttributes:!0,withKeyboardNavigation:!0},X=(0,u.P9)((e,t)=>{let{classNames:r,styles:o,unstyled:i,vars:a,withAriaAttributes:s,onKeyDown:u,withKeyboardNavigation:c,size:d,...f}=(0,l.Y)("ComboboxSearch",W,e),h=v(),p=h.getStyles("search"),g=T({targetType:"input",withAriaAttributes:s,withKeyboardNavigation:c,withExpandedAttribute:!1,onKeyDown:u,autoComplete:"off"});return(0,n.jsx)(S.p,{ref:(0,I.pc)(t,h.store.searchRef),classNames:[{input:p.className},r],styles:[{input:p.style},o],size:d||h.size,...g,...f,__staticSelector:"Combobox"})});X.classes=b,X.displayName="@mantine/core/ComboboxSearch";let J={refProp:"ref",targetType:"input",withKeyboardNavigation:!0,withAriaAttributes:!0,withExpandedAttribute:!1,autoComplete:"off"},K=(0,u.P9)((e,t)=>{let{children:r,refProp:i,withKeyboardNavigation:a,withAriaAttributes:s,withExpandedAttribute:u,targetType:c,autoComplete:d,...f}=(0,l.Y)("ComboboxTarget",J,e);if(!(0,j.v)(r))throw Error("Combobox.Target component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");let h=v(),g=T({targetType:c,withAriaAttributes:s,withKeyboardNavigation:a,withExpandedAttribute:u,onKeyDown:r.props.onKeyDown,autoComplete:d}),m=(0,o.cloneElement)(r,{...g,...f});return(0,n.jsx)(p.A.Target,{ref:(0,I.pc)(t,h.store.targetRef),children:m})});function Z(){let{defaultOpened:e,opened:t,onOpenedChange:r,onDropdownClose:n,onDropdownOpen:i,loop:s=!0,scrollBehavior:l="instant"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[u,c]=(0,a.Z)({value:t,defaultValue:e,finalValue:!1,onChange:r}),d=(0,o.useRef)(null),f=(0,o.useRef)(-1),h=(0,o.useRef)(null),p=(0,o.useRef)(null),g=(0,o.useRef)(-1),m=(0,o.useRef)(-1),v=(0,o.useRef)(-1),y=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";u||(c(!0),null==i||i(e))},[c,i,u]),b=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";u&&(c(!1),null==n||n(e))},[c,n,u]),w=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";u?b(e):y(e)},[b,y,u]),x=(0,o.useCallback)(()=>{let e=document.querySelector("#".concat(d.current," [data-combobox-selected]"));null==e||e.removeAttribute("data-combobox-selected"),null==e||e.removeAttribute("aria-selected")},[]),C=(0,o.useCallback)(e=>{let t=document.getElementById(d.current),r=null==t?void 0:t.querySelectorAll("[data-combobox-option]");if(!r)return null;let n=e>=r.length?0:e<0?r.length-1:e;return(f.current=n,(null==r?void 0:r[n])&&!r[n].hasAttribute("data-combobox-disabled"))?(x(),r[n].setAttribute("data-combobox-selected","true"),r[n].setAttribute("aria-selected","true"),r[n].scrollIntoView({block:"nearest",behavior:l}),r[n].id):null},[l,x]),S=(0,o.useCallback)(()=>{let e=document.querySelector("#".concat(d.current," [data-combobox-active]"));return e?C(Array.from(document.querySelectorAll("#".concat(d.current," [data-combobox-option]"))).findIndex(t=>t===e)):C(0)},[C]),_=(0,o.useCallback)(()=>C(function(e,t,r){for(let r=e+1;r<t.length;r+=1)if(!t[r].hasAttribute("data-combobox-disabled"))return r;if(r){for(let e=0;e<t.length;e+=1)if(!t[e].hasAttribute("data-combobox-disabled"))return e}return e}(f.current,document.querySelectorAll("#".concat(d.current," [data-combobox-option]")),s)),[C,s]),A=(0,o.useCallback)(()=>C(function(e,t,r){for(let r=e-1;r>=0;r-=1)if(!t[r].hasAttribute("data-combobox-disabled"))return r;if(r){for(let e=t.length-1;e>-1;e-=1)if(!t[e].hasAttribute("data-combobox-disabled"))return e}return e}(f.current,document.querySelectorAll("#".concat(d.current," [data-combobox-option]")),s)),[C,s]),E=(0,o.useCallback)(()=>C(function(e){for(let t=0;t<e.length;t+=1)if(!e[t].hasAttribute("data-combobox-disabled"))return t;return -1}(document.querySelectorAll("#".concat(d.current," [data-combobox-option]")))),[C]),j=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"selected",t=arguments.length>1?arguments[1]:void 0;v.current=window.setTimeout(()=>{let r=document.querySelectorAll("#".concat(d.current," [data-combobox-option]")),n=Array.from(r).findIndex(t=>t.hasAttribute("data-combobox-".concat(e)));if(f.current=n,null==t?void 0:t.scrollIntoView){var o;null==(o=r[n])||o.scrollIntoView({block:"nearest",behavior:l})}},0)},[]),k=(0,o.useCallback)(()=>{f.current=-1,x()},[x]),O=(0,o.useCallback)(()=>{let e=document.querySelectorAll("#".concat(d.current," [data-combobox-option]")),t=null==e?void 0:e[f.current];null==t||t.click()},[]),D=(0,o.useCallback)(e=>{d.current=e},[]),P=(0,o.useCallback)(()=>{g.current=window.setTimeout(()=>h.current.focus(),0)},[]),I=(0,o.useCallback)(()=>{m.current=window.setTimeout(()=>p.current.focus(),0)},[]),R=(0,o.useCallback)(()=>f.current,[]);return(0,o.useEffect)(()=>()=>{window.clearTimeout(g.current),window.clearTimeout(m.current),window.clearTimeout(v.current)},[]),{dropdownOpened:u,openDropdown:y,closeDropdown:b,toggleDropdown:w,selectedOptionIndex:f.current,getSelectedOptionIndex:R,selectOption:C,selectFirstOption:E,selectActiveOption:S,selectNextOption:_,selectPreviousOption:A,resetSelectedOption:k,updateSelectedOptionIndex:j,listId:d.current,setListId:D,clickSelectedOption:O,searchRef:h,focusSearchInput:P,targetRef:p,focusTarget:I}}K.displayName="@mantine/core/ComboboxTarget";let Q={keepMounted:!0,withinPortal:!0,resetSelectionOnOptionHover:!1,width:"target",transitionProps:{transition:"fade",duration:0}},ee=(0,f.V)((e,t)=>{let{size:r,dropdownPadding:n}=t;return{options:{"--combobox-option-fz":(0,d.ny)(r),"--combobox-option-padding":(0,d.YC)(r,"combobox-option-padding")},dropdown:{"--combobox-padding":void 0===n?void 0:(0,c.D)(n),"--combobox-option-fz":(0,d.ny)(r),"--combobox-option-padding":(0,d.YC)(r,"combobox-option-padding")}}});function et(e){let t=(0,l.Y)("Combobox",Q,e),{classNames:r,styles:o,unstyled:i,children:a,store:s,vars:u,onOptionSubmit:c,onClose:d,size:f,dropdownPadding:g,resetSelectionOnOptionHover:v,__staticSelector:y,readOnly:w,...x}=t,C=Z(),S=s||C,_=(0,h.I)({name:y||"Combobox",classes:b,props:t,classNames:r,styles:o,unstyled:i,vars:u,varsResolver:ee});return(0,n.jsx)(m,{value:{getStyles:_,store:S,onOptionSubmit:c,size:f,resetSelectionOnOptionHover:v,readOnly:w},children:(0,n.jsx)(p.A,{opened:S.dropdownOpened,...x,onChange:e=>!e&&void(null==d||d(),S.closeDropdown()),withRoles:!1,unstyled:i,children:a})})}et.extend=e=>e,et.classes=b,et.displayName="@mantine/core/Combobox",et.Target=K,et.Dropdown=E,et.Options=G,et.Option=U,et.Search=X,et.Empty=P,et.Chevron=C,et.Footer=z,et.Header=$,et.EventsTarget=M,et.DropdownTarget=O,et.Group=Y,et.ClearButton=_,et.HiddenInput=q;var er=r(2596);function en(e){let{size:t,style:r,...o}=e,i=void 0!==t?{width:(0,c.D)(t),height:(0,c.D)(t),...r}:r;return(0,n.jsx)("svg",{viewBox:"0 0 10 7",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:i,"aria-hidden":!0,...o,children:(0,n.jsx)("path",{d:"M4 4.586L1.707 2.293A1 1 0 1 0 .293 3.707l3 3a.997.997 0 0 0 1.414 0l5-5A1 1 0 1 0 8.293.293L4 4.586z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})}let[eo,ei]=(0,g.F)("ScrollArea.Root component was not found in tree");function ea(e){let t=(0,o.useRef)(e);return(0,o.useEffect)(()=>{t.current=e}),(0,o.useMemo)(()=>function(){for(var e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return null==(e=t.current)?void 0:e.call(t,...n)},[])}var es=r(3141);function el(e,t){let r=ea(t);(0,es.o)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}let eu=(0,o.forwardRef)((e,t)=>{let{style:r,...i}=e,a=ei(),[s,l]=(0,o.useState)(0),[u,c]=(0,o.useState)(0),d=!!(s&&u);return el(a.scrollbarX,()=>{var e;let t=(null==(e=a.scrollbarX)?void 0:e.offsetHeight)||0;a.onCornerHeightChange(t),c(t)}),el(a.scrollbarY,()=>{var e;let t=(null==(e=a.scrollbarY)?void 0:e.offsetWidth)||0;a.onCornerWidthChange(t),l(t)}),d?(0,n.jsx)("div",{...i,ref:t,style:{...r,width:s,height:u}}):null}),ec=(0,o.forwardRef)((e,t)=>{let r=ei(),o=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&o?(0,n.jsx)(eu,{...e,ref:t}):null}),ed={scrollHideDelay:1e3,type:"hover"},ef=(0,o.forwardRef)((e,t)=>{let{type:r,scrollHideDelay:i,scrollbars:a,...s}=(0,l.Y)("ScrollAreaRoot",ed,e),[u,c]=(0,o.useState)(null),[d,f]=(0,o.useState)(null),[h,p]=(0,o.useState)(null),[g,m]=(0,o.useState)(null),[v,b]=(0,o.useState)(null),[w,x]=(0,o.useState)(0),[C,S]=(0,o.useState)(0),[_,A]=(0,o.useState)(!1),[E,j]=(0,o.useState)(!1),k=(0,I.pc)(t,e=>c(e));return(0,n.jsx)(eo,{value:{type:r,scrollHideDelay:i,scrollArea:u,viewport:d,onViewportChange:f,content:h,onContentChange:p,scrollbarX:g,onScrollbarXChange:m,scrollbarXEnabled:_,onScrollbarXEnabledChange:A,scrollbarY:v,onScrollbarYChange:b,scrollbarYEnabled:E,onScrollbarYEnabledChange:j,onCornerWidthChange:x,onCornerHeightChange:S},children:(0,n.jsx)(y.a,{...s,ref:k,__vars:{"--sa-corner-width":"xy"!==a?"0px":"".concat(w,"px"),"--sa-corner-height":"xy"!==a?"0px":"".concat(C,"px")}})})});ef.displayName="@mantine/core/ScrollAreaRoot";let eh=()=>{};function ep(e,t){let r="number"==typeof t?t:t.delay,n="number"!=typeof t&&t.flushOnUnmount,i=ea(e),a=(0,o.useRef)(0),s=Object.assign((0,o.useCallback)(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];window.clearTimeout(a.current);let o=()=>{0!==a.current&&(a.current=0,i(...t))};s.flush=o,a.current=window.setTimeout(o,r)},[i,r]),{flush:eh});return(0,o.useEffect)(()=>()=>{window.clearTimeout(a.current),n&&s.flush()},[s,n]),s}var eg=r(3304);function em(e,t){let r=e/t;return Number.isNaN(r)?0:r}function ev(e){let t=em(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function ey(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}function eb(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=ev(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,a=t.content-t.viewport,s=function(e,t){let[r,n]=t;return Math.min(n,Math.max(r,e))}(e,"ltr"===r?[0,a]:[-1*a,0]);return ey([0,a],[0,i-n])(s)}function ew(e){return e?parseInt(e,10):0}function ex(e,t){let{checkForDefaultPrevented:r=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n=>{null==e||e(n),!1!==r&&n.defaultPrevented||null==t||t(n)}}let[eC,eS]=(0,g.F)("ScrollAreaScrollbar was not found in tree"),e_=(0,o.forwardRef)((e,t)=>{let{sizes:r,hasThumb:i,onThumbChange:a,onThumbPointerUp:s,onThumbPointerDown:l,onThumbPositionChange:u,onDragScroll:c,onWheelScroll:d,onResize:f,...h}=e,p=ei(),[g,m]=(0,o.useState)(null),v=(0,I.pc)(t,e=>m(e)),y=(0,o.useRef)(null),b=(0,o.useRef)(""),{viewport:w}=p,x=r.content-r.viewport,C=ea(d),S=ea(u),_=ep(f,10),A=e=>{y.current&&c({x:e.clientX-y.current.left,y:e.clientY-y.current.top})};return(0,o.useEffect)(()=>{let e=e=>{let t=e.target;(null==g?void 0:g.contains(t))&&C(e,x)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[w,g,x,C]),(0,o.useEffect)(S,[r,S]),el(g,_),el(p.content,_),(0,n.jsx)(eC,{value:{scrollbar:g,hasThumb:i,onThumbChange:ea(a),onThumbPointerUp:ea(s),onThumbPositionChange:S,onThumbPointerDown:ea(l)},children:(0,n.jsx)("div",{...h,ref:v,"data-mantine-scrollbar":!0,style:{position:"absolute",...h.style},onPointerDown:ex(e.onPointerDown,e=>{e.preventDefault(),0===e.button&&(e.target.setPointerCapture(e.pointerId),y.current=g.getBoundingClientRect(),b.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",A(e))}),onPointerMove:ex(e.onPointerMove,A),onPointerUp:ex(e.onPointerUp,e=>{e.preventDefault();let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=b.current,y.current=null})})})}),eA=(0,o.forwardRef)((e,t)=>{let{sizes:r,onSizesChange:i,style:a,...s}=e,l=ei(),[u,c]=(0,o.useState)(),d=(0,o.useRef)(null),f=(0,I.pc)(t,d,l.onScrollbarXChange);return(0,o.useEffect)(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,n.jsx)(e_,{"data-orientation":"horizontal",...s,ref:f,sizes:r,style:{...a,"--sa-thumb-width":"".concat(ev(r),"px")},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&l.viewport&&u&&i({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:ew(u.paddingLeft),paddingEnd:ew(u.paddingRight)}})}})});eA.displayName="@mantine/core/ScrollAreaScrollbarX";let eE=(0,o.forwardRef)((e,t)=>{let{sizes:r,onSizesChange:i,style:a,...s}=e,l=ei(),[u,c]=(0,o.useState)(),d=(0,o.useRef)(null),f=(0,I.pc)(t,d,l.onScrollbarYChange);return(0,o.useEffect)(()=>{d.current&&c(window.getComputedStyle(d.current))},[]),(0,n.jsx)(e_,{...s,"data-orientation":"vertical",ref:f,sizes:r,style:{"--sa-thumb-height":"".concat(ev(r),"px"),...a},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&l.viewport&&u&&i({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:ew(u.paddingTop),paddingEnd:ew(u.paddingBottom)}})}})});eE.displayName="@mantine/core/ScrollAreaScrollbarY";let ej=(0,o.forwardRef)((e,t)=>{let{orientation:r="vertical",...i}=e,{dir:a}=(0,eg.jH)(),s=ei(),l=(0,o.useRef)(null),u=(0,o.useRef)(0),[c,d]=(0,o.useState)({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),f=em(c.viewport,c.content),h={...i,sizes:c,onSizesChange:d,hasThumb:!!(f>0&&f<1),onThumbChange:e=>{l.current=e},onThumbPointerUp:()=>{u.current=0},onThumbPointerDown:e=>{u.current=e}},p=(e,t)=>(function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=ev(r),i=t||o/2,a=r.scrollbar.paddingStart+i,s=r.scrollbar.size-r.scrollbar.paddingEnd-(o-i),l=r.content-r.viewport;return ey([a,s],"ltr"===n?[0,l]:[-1*l,0])(e)})(e,u.current,c,t);return"horizontal"===r?(0,n.jsx)(eA,{...h,ref:t,onThumbPositionChange:()=>{if(s.viewport&&l.current){let e=eb(s.viewport.scrollLeft,c,a);l.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{s.viewport&&(s.viewport.scrollLeft=e)},onDragScroll:e=>{s.viewport&&(s.viewport.scrollLeft=p(e,a))}}):"vertical"===r?(0,n.jsx)(eE,{...h,ref:t,onThumbPositionChange:()=>{if(s.viewport&&l.current){let e=eb(s.viewport.scrollTop,c);0===c.scrollbar.size?l.current.style.setProperty("--thumb-opacity","0"):l.current.style.setProperty("--thumb-opacity","1"),l.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{s.viewport&&(s.viewport.scrollTop=e)},onDragScroll:e=>{s.viewport&&(s.viewport.scrollTop=p(e))}}):null});ej.displayName="@mantine/core/ScrollAreaScrollbarVisible";let ek=(0,o.forwardRef)((e,t)=>{let r=ei(),{forceMount:i,...a}=e,[s,l]=(0,o.useState)(!1),u="horizontal"===e.orientation,c=ep(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;l(u?e:t)}},10);return(el(r.viewport,c),el(r.content,c),i||s)?(0,n.jsx)(ej,{"data-state":s?"visible":"hidden",...a,ref:t}):null});ek.displayName="@mantine/core/ScrollAreaScrollbarAuto";let eO=(0,o.forwardRef)((e,t)=>{let{forceMount:r,...i}=e,a=ei(),[s,l]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{let{scrollArea:e}=a,t=0;if(e){let r=()=>{window.clearTimeout(t),l(!0)},n=()=>{t=window.setTimeout(()=>l(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[a.scrollArea,a.scrollHideDelay]),r||s)?(0,n.jsx)(ek,{"data-state":s?"visible":"hidden",...i,ref:t}):null});eO.displayName="@mantine/core/ScrollAreaScrollbarHover";let eD=(0,o.forwardRef)((e,t)=>{let{forceMount:r,...i}=e,a=ei(),s="horizontal"===e.orientation,[l,u]=(0,o.useState)("hidden"),c=ep(()=>u("idle"),100);return((0,o.useEffect)(()=>{if("idle"===l){let e=window.setTimeout(()=>u("hidden"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[l,a.scrollHideDelay]),(0,o.useEffect)(()=>{let{viewport:e}=a,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(u("scrolling"),c()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[a.viewport,s,c]),r||"hidden"!==l)?(0,n.jsx)(ej,{"data-state":"hidden"===l?"hidden":"visible",...i,ref:t,onPointerEnter:ex(e.onPointerEnter,()=>u("interacting")),onPointerLeave:ex(e.onPointerLeave,()=>u("idle"))}):null}),eP=(0,o.forwardRef)((e,t)=>{let{forceMount:r,...i}=e,a=ei(),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:l}=a,u="horizontal"===e.orientation;return(0,o.useEffect)(()=>(u?s(!0):l(!0),()=>{u?s(!1):l(!1)}),[u,s,l]),"hover"===a.type?(0,n.jsx)(eO,{...i,ref:t,forceMount:r}):"scroll"===a.type?(0,n.jsx)(eD,{...i,ref:t,forceMount:r}):"auto"===a.type?(0,n.jsx)(ek,{...i,ref:t,forceMount:r}):"always"===a.type?(0,n.jsx)(ej,{...i,ref:t}):null});eP.displayName="@mantine/core/ScrollAreaScrollbar";let eI=(0,o.forwardRef)((e,t)=>{let{style:r,...i}=e,a=ei(),s=eS(),{onThumbPositionChange:l}=s,u=(0,I.pc)(t,e=>s.onThumbChange(e)),c=(0,o.useRef)(void 0),d=ep(()=>{c.current&&(c.current(),c.current=void 0)},100);return(0,o.useEffect)(()=>{let{viewport:e}=a;if(e){let t=()=>{d(),c.current||(c.current=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},a=r.left!==i.left,s=r.top!==i.top;(a||s)&&t(),r=i,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)}(e,l),l())};return l(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[a.viewport,d,l]),(0,n.jsx)("div",{"data-state":s.hasThumb?"visible":"hidden",...i,ref:u,style:{width:"var(--sa-thumb-width)",height:"var(--sa-thumb-height)",...r},onPointerDownCapture:ex(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;s.onThumbPointerDown({x:r,y:n})}),onPointerUp:ex(e.onPointerUp,s.onThumbPointerUp)})});eI.displayName="@mantine/core/ScrollAreaThumb";let eR=(0,o.forwardRef)((e,t)=>{let{forceMount:r,...o}=e,i=eS();return r||i.hasThumb?(0,n.jsx)(eI,{ref:t,...o}):null});eR.displayName="@mantine/core/ScrollAreaThumb";let eT=(0,o.forwardRef)((e,t)=>{let{children:r,style:o,...i}=e,a=ei(),s=(0,I.pc)(t,a.onViewportChange);return(0,n.jsx)(y.a,{...i,ref:s,style:{overflowX:a.scrollbarXEnabled?"scroll":"hidden",overflowY:a.scrollbarYEnabled?"scroll":"hidden",...o},children:(0,n.jsx)("div",{style:{minWidth:"100%",display:"table"},ref:a.onContentChange,children:r})})});eT.displayName="@mantine/core/ScrollAreaViewport";var eL={root:"m_d57069b5",viewport:"m_c0783ff9",viewportInner:"m_f8f631dd",scrollbar:"m_c44ba933",thumb:"m_d8b5e363",corner:"m_21657268"};let eM={scrollHideDelay:1e3,type:"hover",scrollbars:"xy"},eN=(0,f.V)((e,t)=>{let{scrollbarSize:r}=t;return{root:{"--scrollarea-scrollbar-size":(0,c.D)(r)}}}),ez=(0,u.P9)((e,t)=>{let r=(0,l.Y)("ScrollArea",eM,e),{classNames:i,className:a,style:s,styles:u,unstyled:c,scrollbarSize:d,vars:f,type:p,scrollHideDelay:g,viewportProps:m,viewportRef:v,onScrollPositionChange:y,children:b,offsetScrollbars:w,scrollbars:x,onBottomReached:C,onTopReached:S,..._}=r,[A,E]=(0,o.useState)(!1),j=(0,h.I)({name:"ScrollArea",props:r,classes:eL,className:a,style:s,classNames:i,styles:u,unstyled:c,vars:f,varsResolver:eN});return(0,n.jsxs)(ef,{type:"never"===p?"always":p,scrollHideDelay:g,ref:t,scrollbars:x,...j("root"),..._,children:[(0,n.jsx)(eT,{...m,...j("viewport",{style:null==m?void 0:m.style}),ref:v,"data-offset-scrollbars":!0===w?"xy":w||void 0,"data-scrollbars":x||void 0,onScroll:e=>{var t;null==m||null==(t=m.onScroll)||t.call(m,e),null==y||y({x:e.currentTarget.scrollLeft,y:e.currentTarget.scrollTop});let{scrollTop:r,scrollHeight:n,clientHeight:o}=e.currentTarget;r-(n-o)>=0&&(null==C||C()),0===r&&(null==S||S())},children:b}),("xy"===x||"x"===x)&&(0,n.jsx)(eP,{...j("scrollbar"),orientation:"horizontal","data-hidden":"never"===p||void 0,forceMount:!0,onMouseEnter:()=>E(!0),onMouseLeave:()=>E(!1),children:(0,n.jsx)(eR,{...j("thumb")})}),("xy"===x||"y"===x)&&(0,n.jsx)(eP,{...j("scrollbar"),orientation:"vertical","data-hidden":"never"===p||void 0,forceMount:!0,onMouseEnter:()=>E(!0),onMouseLeave:()=>E(!1),children:(0,n.jsx)(eR,{...j("thumb")})}),(0,n.jsx)(ec,{...j("corner"),"data-hovered":A||void 0,"data-hidden":"never"===p||void 0})]})});ez.displayName="@mantine/core/ScrollArea";let eB=(0,u.P9)((e,t)=>{let{children:r,classNames:o,styles:i,scrollbarSize:a,scrollHideDelay:s,type:u,dir:c,offsetScrollbars:d,viewportRef:f,onScrollPositionChange:h,unstyled:p,variant:g,viewportProps:m,scrollbars:v,style:b,vars:w,onBottomReached:x,onTopReached:C,...S}=(0,l.Y)("ScrollAreaAutosize",eM,e);return(0,n.jsx)(y.a,{...S,ref:t,style:[{display:"flex",overflow:"auto"},b],children:(0,n.jsx)(y.a,{style:{display:"flex",flexDirection:"column",flex:1},children:(0,n.jsx)(ez,{classNames:o,styles:i,scrollHideDelay:s,scrollbarSize:a,type:u,dir:c,offsetScrollbars:d,viewportRef:f,onScrollPositionChange:h,unstyled:p,variant:g,viewportProps:m,vars:w,scrollbars:v,onBottomReached:x,onTopReached:C,children:r})})})});function eY(e){return"group"in e}function eF(e){let{data:t,withCheckIcon:r,value:o,checkIconPosition:i,unstyled:a,renderOption:s}=e;if(!eY(t)){var l;let e=(l=t.value,Array.isArray(o)?o.includes(l):o===l),u=r&&e&&(0,n.jsx)(en,{className:b.optionsDropdownCheckIcon}),c=(0,n.jsxs)(n.Fragment,{children:["left"===i&&u,(0,n.jsx)("span",{children:t.label}),"right"===i&&u]});return(0,n.jsx)(et.Option,{value:t.value,disabled:t.disabled,className:(0,er.A)({[b.optionsDropdownOption]:!a}),"data-reverse":"right"===i||void 0,"data-checked":e||void 0,"aria-selected":e,active:e,children:"function"==typeof s?s({option:t,checked:e}):c})}let u=t.items.map(e=>(0,n.jsx)(eF,{data:e,value:o,unstyled:a,withCheckIcon:r,checkIconPosition:i,renderOption:s},e.value));return(0,n.jsx)(et.Group,{label:t.group,children:u})}function e$(e){let{data:t,hidden:r,hiddenWhenEmpty:o,filter:i,search:a,limit:s,maxDropdownHeight:l,withScrollArea:u=!0,filterOptions:c=!0,withCheckIcon:d=!1,value:f,checkIconPosition:h,nothingFoundMessage:p,unstyled:g,labelId:m,renderOption:v,scrollAreaProps:y,"aria-label":b}=e;!function e(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;if(Array.isArray(t))for(let n of t)if(eY(n))e(n.items,r);else{if(void 0===n.value)throw Error("[@mantine/core] Each option must have value property");if("string"!=typeof n.value)throw Error("[@mantine/core] Option value must be a string, other data formats are not supported, got ".concat(typeof n.value));if(r.has(n.value))throw Error('[@mantine/core] Duplicate options are not supported. Option with value "'.concat(n.value,'" was provided more than once'));r.add(n.value)}}(t);let w="string"==typeof a?(i||function e(t){let{options:r,search:n,limit:o}=t,i=n.trim().toLowerCase(),a=[];for(let t=0;t<r.length;t+=1){let s=r[t];if(a.length===o)break;eY(s)&&a.push({group:s.group,items:e({options:s.items,search:n,limit:o-a.length})}),!eY(s)&&s.label.toLowerCase().includes(i)&&a.push(s)}return a})({options:t,search:c?a:"",limit:null!=s?s:1/0}):t,x=function(e){if(0===e.length)return!0;for(let t of e)if(!("group"in t)||t.items.length>0)return!1;return!0}(w),C=w.map(e=>(0,n.jsx)(eF,{data:e,withCheckIcon:d,value:f,checkIconPosition:h,unstyled:g,renderOption:v},eY(e)?e.group:e.value));return(0,n.jsx)(et.Dropdown,{hidden:r||o&&x,children:(0,n.jsxs)(et.Options,{labelledBy:m,"aria-label":b,children:[u?(0,n.jsx)(ez.Autosize,{mah:null!=l?l:220,type:"scroll",scrollbarSize:"var(--combobox-padding)",offsetScrollbars:"y",...y,children:C}):C,x&&p&&(0,n.jsx)(et.Empty,{children:p})]})})}ez.classes=eL,eB.displayName="@mantine/core/ScrollAreaAutosize",eB.classes=eL,ez.Autosize=eB;var eq=r(4225);let eV={searchable:!1,withCheckIcon:!0,allowDeselect:!0,checkIconPosition:"left"},eU=(0,u.P9)((e,t)=>{let r=(0,l.Y)("Select",eV,e),{classNames:u,styles:c,unstyled:d,vars:f,dropdownOpened:h,defaultDropdownOpened:p,onDropdownClose:g,onDropdownOpen:m,onFocus:v,onBlur:y,onClick:b,onChange:w,data:x,value:C,defaultValue:S,selectFirstOptionOnChange:_,onOptionSubmit:A,comboboxProps:E,readOnly:j,disabled:k,filter:O,limit:D,withScrollArea:P,maxDropdownHeight:I,size:R,searchable:T,rightSection:L,checkIconPosition:M,withCheckIcon:N,nothingFoundMessage:z,name:B,form:Y,searchValue:F,defaultSearchValue:$,onSearchChange:q,allowDeselect:V,error:U,rightSectionPointerEvents:H,id:G,clearable:W,clearButtonProps:X,hiddenInputProps:J,renderOption:K,onClear:Q,autoComplete:ee,scrollAreaProps:er,__defaultRightSection:en,__clearSection:eo,__clearable:ei,...ea}=r,es=(0,o.useMemo)(()=>(function(e){return e?e.map(e=>(function e(t){return"string"==typeof t?{value:t,label:t}:"value"in t&&!("label"in t)?{value:t.value,label:t.value,disabled:t.disabled}:"number"==typeof t?{value:t.toString(),label:t.toString()}:"group"in t?{group:t.group,items:t.items.map(t=>e(t))}:t})(e)):[]})(x),[x]),el=(0,o.useMemo)(()=>(function e(t){return t.reduce((t,r)=>"group"in r?{...t,...e(r.items)}:(t[r.value]=r,t),{})})(es),[es]),eu=(0,i.B)(G),[ec,ed,ef]=(0,a.Z)({value:C,defaultValue:S,finalValue:null,onChange:w}),eh="string"==typeof ec?el[ec]:void 0,ep=function(e){let t=(0,o.useRef)(void 0);return(0,o.useEffect)(()=>{t.current=e},[e]),t.current}(eh),[eg,em]=(0,a.Z)({value:F,defaultValue:$,finalValue:eh?eh.label:"",onChange:q}),ev=Z({opened:h,defaultOpened:p,onDropdownOpen:()=>{null==m||m(),ev.updateSelectedOptionIndex("active",{scrollIntoView:!0})},onDropdownClose:()=>{null==g||g(),ev.resetSelectedOption()}}),{resolvedClassNames:ey,resolvedStyles:eb}=(0,s.Y)({props:r,styles:c,classNames:u});(0,o.useEffect)(()=>{_&&ev.selectFirstOption()},[_,ec]),(0,o.useEffect)(()=>{null===C&&em(""),"string"==typeof C&&eh&&((null==ep?void 0:ep.value)!==eh.value||(null==ep?void 0:ep.label)!==eh.label)&&em(eh.label)},[C,eh]);let ew=(0,n.jsx)(et.ClearButton,{...X,onClear:()=>{ed(null,null),em(""),null==Q||Q()}});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(et,{store:ev,__staticSelector:"Select",classNames:ey,styles:eb,unstyled:d,readOnly:j,onOptionSubmit:e=>{null==A||A(e);let t=V&&el[e].value===ec?null:el[e],r=t?t.value:null;r!==ec&&ed(r,t),ef||em("string"==typeof r&&(null==t?void 0:t.label)||""),ev.closeDropdown()},size:R,...E,children:[(0,n.jsx)(et.Target,{targetType:T?"input":"button",autoComplete:ee,children:(0,n.jsx)(eq.O,{id:eu,ref:t,__defaultRightSection:(0,n.jsx)(et.Chevron,{size:R,error:U,unstyled:d}),__clearSection:ew,__clearable:W&&!!ec&&!k&&!j,rightSection:L,rightSectionPointerEvents:H||(ew?"all":"none"),...ea,size:R,__staticSelector:"Select",disabled:k,readOnly:j||!T,value:eg,onChange:e=>{em(e.currentTarget.value),ev.openDropdown(),_&&ev.selectFirstOption()},onFocus:e=>{T&&ev.openDropdown(),null==v||v(e)},onBlur:e=>{var t;T&&ev.closeDropdown(),em(null!=ec&&(null==(t=el[ec])?void 0:t.label)||""),null==y||y(e)},onClick:e=>{T?ev.openDropdown():ev.toggleDropdown(),null==b||b(e)},classNames:ey,styles:eb,unstyled:d,pointer:!T,error:U})}),(0,n.jsx)(e$,{data:es,hidden:j||k,filter:O,search:eg,limit:D,hiddenWhenEmpty:!z,withScrollArea:P,maxDropdownHeight:I,filterOptions:T&&(null==eh?void 0:eh.label)!==eg,value:ec,checkIconPosition:M,withCheckIcon:N,nothingFoundMessage:z,unstyled:d,labelId:ea.label?"".concat(eu,"-label"):void 0,"aria-label":ea.label?void 0:ea["aria-label"],renderOption:K,scrollAreaProps:er})]}),(0,n.jsx)(et.HiddenInput,{value:ec,name:B,form:Y,disabled:k,...J})]})});eU.classes={...eq.O.classes,...et.classes},eU.displayName="@mantine/core/Select"},4594:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(2115),o={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}},i=((e,t,r,i)=>{let a=(0,n.forwardRef)((r,a)=>{let{color:s="currentColor",size:l=24,stroke:u=2,title:c,className:d,children:f,...h}=r;return(0,n.createElement)("svg",{ref:a,...o[e],width:l,height:l,className:["tabler-icon","tabler-icon-".concat(t),d].join(" "),..."filled"===e?{fill:s}:{strokeWidth:u,stroke:s},...h},[c&&(0,n.createElement)("title",{key:"svg-title"},c),...i.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(f)?f:[f]])});return a.displayName="".concat(r),a})("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]])},4861:(e,t,r)=>{"use strict";r.d(t,{q:()=>o});var n=r(1187);function o(e,t){let r=e.map(e=>({value:e,px:e in t?(0,n.px)(t[e]):(0,n.px)(e)}));return r.sort((e,t)=>e.px-t.px),r}},4945:(e,t,r)=>{"use strict";r.d(t,{BN:()=>h,ER:()=>p,Ej:()=>m,UE:()=>y,UU:()=>g,cY:()=>f,mG:()=>v,we:()=>d});var n=r(6492),o=r(2115),i=r(7650),a="undefined"!=typeof document?o.useLayoutEffect:function(){};function s(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!s(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!s(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function l(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function u(e,t){let r=l(e);return Math.round(t*r)/r}function c(e){let t=o.useRef(e);return a(()=>{t.current=e}),t}function d(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:d=[],platform:f,elements:{reference:h,floating:p}={},transform:g=!0,whileElementsMounted:m,open:v}=e,[y,b]=o.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[w,x]=o.useState(d);s(w,d)||x(d);let[C,S]=o.useState(null),[_,A]=o.useState(null),E=o.useCallback(e=>{e!==D.current&&(D.current=e,S(e))},[]),j=o.useCallback(e=>{e!==P.current&&(P.current=e,A(e))},[]),k=h||C,O=p||_,D=o.useRef(null),P=o.useRef(null),I=o.useRef(y),R=null!=m,T=c(m),L=c(f),M=c(v),N=o.useCallback(()=>{if(!D.current||!P.current)return;let e={placement:t,strategy:r,middleware:w};L.current&&(e.platform=L.current),(0,n.rD)(D.current,P.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};z.current&&!s(I.current,t)&&(I.current=t,i.flushSync(()=>{b(t)}))})},[w,t,r,L,M]);a(()=>{!1===v&&I.current.isPositioned&&(I.current.isPositioned=!1,b(e=>({...e,isPositioned:!1})))},[v]);let z=o.useRef(!1);a(()=>(z.current=!0,()=>{z.current=!1}),[]),a(()=>{if(k&&(D.current=k),O&&(P.current=O),k&&O){if(T.current)return T.current(k,O,N);N()}},[k,O,N,T,R]);let B=o.useMemo(()=>({reference:D,floating:P,setReference:E,setFloating:j}),[E,j]),Y=o.useMemo(()=>({reference:k,floating:O}),[k,O]),F=o.useMemo(()=>{let e={position:r,left:0,top:0};if(!Y.floating)return e;let t=u(Y.floating,y.x),n=u(Y.floating,y.y);return g?{...e,transform:"translate("+t+"px, "+n+"px)",...l(Y.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,g,Y.floating,y.x,y.y]);return o.useMemo(()=>({...y,update:N,refs:B,elements:Y,floatingStyles:F}),[y,N,B,Y,F])}let f=(e,t)=>({...(0,n.cY)(e),options:[e,t]}),h=(e,t)=>({...(0,n.BN)(e),options:[e,t]}),p=(e,t)=>({...(0,n.ER)(e),options:[e,t]}),g=(e,t)=>({...(0,n.UU)(e),options:[e,t]}),m=(e,t)=>({...(0,n.Ej)(e),options:[e,t]}),v=(e,t)=>({...(0,n.mG)(e),options:[e,t]}),y=(e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:r,padding:o}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?(0,n.UE)({element:r.current,padding:o}).fn(t):{}:r?(0,n.UE)({element:r,padding:o}).fn(t):{}}}))(e),options:[e,t]})},6028:(e,t,r)=>{"use strict";r.d(t,{Y:()=>a}),r(2115),r(5155);var n=r(3131),o=r(9787),i=r(4092);function a(e){let{classNames:t,styles:r,props:a,stylesCtx:s}=e,l=(0,n.xd)();return{resolvedClassNames:(0,o.J)({theme:l,classNames:t,props:a,stylesCtx:s||void 0}),resolvedStyles:(0,i.N)({theme:l,styles:r,props:a,stylesCtx:s||void 0})}}},6204:(e,t,r)=>{"use strict";r.d(t,{GY:()=>a,YC:()=>i,dh:()=>c,ks:()=>u,nJ:()=>s,ny:()=>l});var n=r(8772),o=r(5903);function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"size",r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(void 0!==e)return(0,n.t)(e)?r?(0,o.D)(e):e:"var(--".concat(t,"-").concat(e,")")}function a(e){return i(e,"mantine-spacing")}function s(e){return void 0===e?"var(--mantine-radius-default)":i(e,"mantine-radius")}function l(e){return i(e,"mantine-font-size")}function u(e){return i(e,"mantine-line-height",!1)}function c(e){if(e)return i(e,"mantine-shadow",!1)}},6205:function(e,t,r){var n=r(9641).Buffer;e.exports=(()=>{var e={873:(e,t)=>{var r,n,o=function(){var e,t,r=function(e,t){var r=e,n=a[t],o=null,i=0,l=null,m=[],v={},b=function(e,t){o=function(e){for(var t=Array(e),r=0;r<e;r+=1){t[r]=Array(e);for(var n=0;n<e;n+=1)t[r][n]=null}return t}(i=4*r+17),w(0,0),w(i-7,0),w(0,i-7),C(),x(),_(e,t),r>=7&&S(e),null==l&&(l=E(r,n,m)),A(l,t)},w=function(e,t){for(var r=-1;r<=7;r+=1)if(!(e+r<=-1||i<=e+r))for(var n=-1;n<=7;n+=1)t+n<=-1||i<=t+n||(o[e+r][t+n]=0<=r&&r<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=n&&n<=4)},x=function(){for(var e=8;e<i-8;e+=1)null==o[e][6]&&(o[e][6]=e%2==0);for(var t=8;t<i-8;t+=1)null==o[6][t]&&(o[6][t]=t%2==0)},C=function(){for(var e=s.getPatternPosition(r),t=0;t<e.length;t+=1)for(var n=0;n<e.length;n+=1){var i=e[t],a=e[n];if(null==o[i][a])for(var l=-2;l<=2;l+=1)for(var u=-2;u<=2;u+=1)o[i+l][a+u]=-2==l||2==l||-2==u||2==u||0==l&&0==u}},S=function(e){for(var t=s.getBCHTypeNumber(r),n=0;n<18;n+=1){var a=!e&&1==(t>>n&1);o[Math.floor(n/3)][n%3+i-8-3]=a}for(n=0;n<18;n+=1)a=!e&&1==(t>>n&1),o[n%3+i-8-3][Math.floor(n/3)]=a},_=function(e,t){for(var r=n<<3|t,a=s.getBCHTypeInfo(r),l=0;l<15;l+=1){var u=!e&&1==(a>>l&1);l<6?o[l][8]=u:l<8?o[l+1][8]=u:o[i-15+l][8]=u}for(l=0;l<15;l+=1)u=!e&&1==(a>>l&1),l<8?o[8][i-l-1]=u:l<9?o[8][15-l-1+1]=u:o[8][15-l-1]=u;o[i-8][8]=!e},A=function(e,t){for(var r=-1,n=i-1,a=7,l=0,u=s.getMaskFunction(t),c=i-1;c>0;c-=2)for(6==c&&(c-=1);;){for(var d=0;d<2;d+=1)if(null==o[n][c-d]){var f=!1;l<e.length&&(f=1==(e[l]>>>a&1)),u(n,c-d)&&(f=!f),o[n][c-d]=f,-1==(a-=1)&&(l+=1,a=7)}if((n+=r)<0||i<=n){n-=r,r=-r;break}}},E=function(e,t,r){for(var n=c.getRSBlocks(e,t),o=d(),i=0;i<r.length;i+=1){var a=r[i];o.put(a.getMode(),4),o.put(a.getLength(),s.getLengthInBits(a.getMode(),e)),a.write(o)}var l=0;for(i=0;i<n.length;i+=1)l+=n[i].dataCount;if(o.getLengthInBits()>8*l)throw"code length overflow. ("+o.getLengthInBits()+">"+8*l+")";for(o.getLengthInBits()+4<=8*l&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(!1);for(;!(o.getLengthInBits()>=8*l||(o.put(236,8),o.getLengthInBits()>=8*l));)o.put(17,8);return function(e,t){for(var r=0,n=0,o=0,i=Array(t.length),a=Array(t.length),l=0;l<t.length;l+=1){var c=t[l].dataCount,d=t[l].totalCount-c;n=Math.max(n,c),o=Math.max(o,d),i[l]=Array(c);for(var f=0;f<i[l].length;f+=1)i[l][f]=255&e.getBuffer()[f+r];r+=c;var h=s.getErrorCorrectPolynomial(d),p=u(i[l],h.getLength()-1).mod(h);for(a[l]=Array(h.getLength()-1),f=0;f<a[l].length;f+=1){var g=f+p.getLength()-a[l].length;a[l][f]=g>=0?p.getAt(g):0}}var m=0;for(f=0;f<t.length;f+=1)m+=t[f].totalCount;var v=Array(m),y=0;for(f=0;f<n;f+=1)for(l=0;l<t.length;l+=1)f<i[l].length&&(v[y]=i[l][f],y+=1);for(f=0;f<o;f+=1)for(l=0;l<t.length;l+=1)f<a[l].length&&(v[y]=a[l][f],y+=1);return v}(o,n)};v.addData=function(e,t){var r=null;switch(t=t||"Byte"){case"Numeric":r=f(e);break;case"Alphanumeric":r=h(e);break;case"Byte":r=p(e);break;case"Kanji":r=g(e);break;default:throw"mode:"+t}m.push(r),l=null},v.isDark=function(e,t){if(e<0||i<=e||t<0||i<=t)throw e+","+t;return o[e][t]},v.getModuleCount=function(){return i},v.make=function(){if(r<1){for(var e=1;e<40;e++){for(var t=c.getRSBlocks(e,n),o=d(),i=0;i<m.length;i++){var a=m[i];o.put(a.getMode(),4),o.put(a.getLength(),s.getLengthInBits(a.getMode(),e)),a.write(o)}var l=0;for(i=0;i<t.length;i++)l+=t[i].dataCount;if(o.getLengthInBits()<=8*l)break}r=e}b(!1,function(){for(var e=0,t=0,r=0;r<8;r+=1){b(!0,r);var n=s.getLostPoint(v);(0==r||e>n)&&(e=n,t=r)}return t}())},v.createTableTag=function(e,t){e=e||2;var r="";r+='<table style=" border-width: 0px; border-style: none; border-collapse: collapse; padding: 0px; margin: '+(t=void 0===t?4*e:t)+'px;"><tbody>';for(var n=0;n<v.getModuleCount();n+=1){r+="<tr>";for(var o=0;o<v.getModuleCount();o+=1)r+='<td style=" border-width: 0px; border-style: none; border-collapse: collapse; padding: 0px; margin: 0px; width: '+e+"px; height: "+e+"px; background-color: "+(v.isDark(n,o)?"#000000":"#ffffff")+';"/>';r+="</tr>"}return(r+="</tbody>")+"</table>"},v.createSvgTag=function(e,t,r,n){var o={};"object"==typeof arguments[0]&&(e=(o=arguments[0]).cellSize,t=o.margin,r=o.alt,n=o.title),e=e||2,t=void 0===t?4*e:t,(r="string"==typeof r?{text:r}:r||{}).text=r.text||null,r.id=r.text?r.id||"qrcode-description":null,(n="string"==typeof n?{text:n}:n||{}).text=n.text||null,n.id=n.text?n.id||"qrcode-title":null;var i,a,s,l,u=v.getModuleCount()*e+2*t,c="";for(l="l"+e+",0 0,"+e+" -"+e+",0 0,-"+e+"z ",c+='<svg version="1.1" xmlns="http://www.w3.org/2000/svg"',c+=o.scalable?"":' width="'+u+'px" height="'+u+'px"',c+=' viewBox="0 0 '+u+" "+u+'" ',c+=' preserveAspectRatio="xMinYMin meet"',c+=n.text||r.text?' role="img" aria-labelledby="'+j([n.id,r.id].join(" ").trim())+'"':"",c+=">",c+=n.text?'<title id="'+j(n.id)+'">'+j(n.text)+"</title>":"",c+=r.text?'<description id="'+j(r.id)+'">'+j(r.text)+"</description>":"",c+='<rect width="100%" height="100%" fill="white" cx="0" cy="0"/>',c+='<path d="',a=0;a<v.getModuleCount();a+=1)for(s=a*e+t,i=0;i<v.getModuleCount();i+=1)v.isDark(a,i)&&(c+="M"+(i*e+t)+","+s+l);return(c+='" stroke="transparent" fill="black"/>')+"</svg>"},v.createDataURL=function(e,t){e=e||2,t=void 0===t?4*e:t;var r=v.getModuleCount()*e+2*t,n=t,o=r-t;return y(r,r,function(t,r){if(n<=t&&t<o&&n<=r&&r<o){var i=Math.floor((t-n)/e),a=Math.floor((r-n)/e);return+!v.isDark(a,i)}return 1})},v.createImgTag=function(e,t,r){e=e||2,t=void 0===t?4*e:t;var n=v.getModuleCount()*e+2*t,o="";return o+="<img",o+=' src="',o+=v.createDataURL(e,t),o+='"',o+=' width="',o+=n,o+='"',o+=' height="',o+=n,o+='"',r&&(o+=' alt="',o+=j(r),o+='"'),o+"/>"};var j=function(e){for(var t="",r=0;r<e.length;r+=1){var n=e.charAt(r);switch(n){case"<":t+="&lt;";break;case">":t+="&gt;";break;case"&":t+="&amp;";break;case'"':t+="&quot;";break;default:t+=n}}return t};return v.createASCII=function(e,t){if((e=e||1)<2)return function(e){e=void 0===e?2:e;var t,r,n,o,i,a=+v.getModuleCount()+2*e,s=e,l=a-e,u={"██":"█","█ ":"▀"," █":"▄","  ":" "},c={"██":"▀","█ ":"▀"," █":" ","  ":" "},d="";for(t=0;t<a;t+=2){for(n=Math.floor((t-s)/1),o=Math.floor((t+1-s)/1),r=0;r<a;r+=1)i="█",s<=r&&r<l&&s<=t&&t<l&&v.isDark(n,Math.floor((r-s)/1))&&(i=" "),s<=r&&r<l&&s<=t+1&&t+1<l&&v.isDark(o,Math.floor((r-s)/1))?i+=" ":i+="█",d+=e<1&&t+1>=l?c[i]:u[i];d+="\n"}return a%2&&e>0?d.substring(0,d.length-a-1)+Array(a+1).join("▀"):d.substring(0,d.length-1)}(t);e-=1,t=void 0===t?2*e:t;var r,n,o,i,a=v.getModuleCount()*e+2*t,s=t,l=a-t,u=Array(e+1).join("██"),c=Array(e+1).join("  "),d="",f="";for(r=0;r<a;r+=1){for(o=Math.floor((r-s)/e),f="",n=0;n<a;n+=1)i=1,s<=n&&n<l&&s<=r&&r<l&&v.isDark(o,Math.floor((n-s)/e))&&(i=0),f+=i?u:c;for(o=0;o<e;o+=1)d+=f+"\n"}return d.substring(0,d.length-1)},v.renderTo2dContext=function(e,t){t=t||2;for(var r=v.getModuleCount(),n=0;n<r;n++)for(var o=0;o<r;o++)e.fillStyle=v.isDark(n,o)?"black":"white",e.fillRect(n*t,o*t,t,t)},v};r.stringToBytes=(r.stringToBytesFuncs={default:function(e){for(var t=[],r=0;r<e.length;r+=1){var n=e.charCodeAt(r);t.push(255&n)}return t}}).default,r.createStringToBytes=function(e,t){var r=function(){for(var r=v(e),n=function(){var e=r.read();if(-1==e)throw"eof";return e},o=0,i={};;){var a=r.read();if(-1==a)break;var s=n(),l=n()<<8|n();i[String.fromCharCode(a<<8|s)]=l,o+=1}if(o!=t)throw o+" != "+t;return i}();return function(e){for(var t=[],n=0;n<e.length;n+=1){var o=e.charCodeAt(n);if(o<128)t.push(o);else{var i=r[e.charAt(n)];"number"==typeof i?(255&i)==i?t.push(i):(t.push(i>>>8),t.push(255&i)):t.push(63)}}return t}};var n,o,i,a={L:1,M:0,Q:3,H:2},s=(n=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],i=function(e){for(var t=0;0!=e;)t+=1,e>>>=1;return t},(o={}).getBCHTypeInfo=function(e){for(var t=e<<10;i(t)-i(1335)>=0;)t^=1335<<i(t)-i(1335);return 21522^(e<<10|t)},o.getBCHTypeNumber=function(e){for(var t=e<<12;i(t)-i(7973)>=0;)t^=7973<<i(t)-i(7973);return e<<12|t},o.getPatternPosition=function(e){return n[e-1]},o.getMaskFunction=function(e){switch(e){case 0:return function(e,t){return(e+t)%2==0};case 1:return function(e,t){return e%2==0};case 2:return function(e,t){return t%3==0};case 3:return function(e,t){return(e+t)%3==0};case 4:return function(e,t){return(Math.floor(e/2)+Math.floor(t/3))%2==0};case 5:return function(e,t){return e*t%2+e*t%3==0};case 6:return function(e,t){return(e*t%2+e*t%3)%2==0};case 7:return function(e,t){return(e*t%3+(e+t)%2)%2==0};default:throw"bad maskPattern:"+e}},o.getErrorCorrectPolynomial=function(e){for(var t=u([1],0),r=0;r<e;r+=1)t=t.multiply(u([1,l.gexp(r)],0));return t},o.getLengthInBits=function(e,t){if(1<=t&&t<10)switch(e){case 1:return 10;case 2:return 9;case 4:case 8:return 8;default:throw"mode:"+e}if(t<27)switch(e){case 1:return 12;case 2:return 11;case 4:return 16;case 8:return 10;default:throw"mode:"+e}if(!(t<41))throw"type:"+t;switch(e){case 1:return 14;case 2:return 13;case 4:return 16;case 8:return 12;default:throw"mode:"+e}},o.getLostPoint=function(e){for(var t=e.getModuleCount(),r=0,n=0;n<t;n+=1)for(var o=0;o<t;o+=1){for(var i=0,a=e.isDark(n,o),s=-1;s<=1;s+=1)if(!(n+s<0||t<=n+s))for(var l=-1;l<=1;l+=1)o+l<0||t<=o+l||0==s&&0==l||a==e.isDark(n+s,o+l)&&(i+=1);i>5&&(r+=3+i-5)}for(n=0;n<t-1;n+=1)for(o=0;o<t-1;o+=1){var u=0;e.isDark(n,o)&&(u+=1),e.isDark(n+1,o)&&(u+=1),e.isDark(n,o+1)&&(u+=1),e.isDark(n+1,o+1)&&(u+=1),0!=u&&4!=u||(r+=3)}for(n=0;n<t;n+=1)for(o=0;o<t-6;o+=1)e.isDark(n,o)&&!e.isDark(n,o+1)&&e.isDark(n,o+2)&&e.isDark(n,o+3)&&e.isDark(n,o+4)&&!e.isDark(n,o+5)&&e.isDark(n,o+6)&&(r+=40);for(o=0;o<t;o+=1)for(n=0;n<t-6;n+=1)e.isDark(n,o)&&!e.isDark(n+1,o)&&e.isDark(n+2,o)&&e.isDark(n+3,o)&&e.isDark(n+4,o)&&!e.isDark(n+5,o)&&e.isDark(n+6,o)&&(r+=40);var c=0;for(o=0;o<t;o+=1)for(n=0;n<t;n+=1)e.isDark(n,o)&&(c+=1);return r+Math.abs(100*c/t/t-50)/5*10},o),l=function(){for(var e=Array(256),t=Array(256),r=0;r<8;r+=1)e[r]=1<<r;for(r=8;r<256;r+=1)e[r]=e[r-4]^e[r-5]^e[r-6]^e[r-8];for(r=0;r<255;r+=1)t[e[r]]=r;return{glog:function(e){if(e<1)throw"glog("+e+")";return t[e]},gexp:function(t){for(;t<0;)t+=255;for(;t>=256;)t-=255;return e[t]}}}();function u(e,t){if(void 0===e.length)throw e.length+"/"+t;var r=function(){for(var r=0;r<e.length&&0==e[r];)r+=1;for(var n=Array(e.length-r+t),o=0;o<e.length-r;o+=1)n[o]=e[o+r];return n}(),n={getAt:function(e){return r[e]},getLength:function(){return r.length},multiply:function(e){for(var t=Array(n.getLength()+e.getLength()-1),r=0;r<n.getLength();r+=1)for(var o=0;o<e.getLength();o+=1)t[r+o]^=l.gexp(l.glog(n.getAt(r))+l.glog(e.getAt(o)));return u(t,0)},mod:function(e){if(n.getLength()-e.getLength()<0)return n;for(var t=l.glog(n.getAt(0))-l.glog(e.getAt(0)),r=Array(n.getLength()),o=0;o<n.getLength();o+=1)r[o]=n.getAt(o);for(o=0;o<e.getLength();o+=1)r[o]^=l.gexp(l.glog(e.getAt(o))+t);return u(r,0).mod(e)}};return n}var c=(e=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],t=function(e,t){var r={};return r.totalCount=e,r.dataCount=t,r},{getRSBlocks:function(r,n){var o=function(t,r){switch(r){case a.L:return e[4*(t-1)+0];case a.M:return e[4*(t-1)+1];case a.Q:return e[4*(t-1)+2];case a.H:return e[4*(t-1)+3];default:return}}(r,n);if(void 0===o)throw"bad rs block @ typeNumber:"+r+"/errorCorrectionLevel:"+n;for(var i=o.length/3,s=[],l=0;l<i;l+=1)for(var u=o[3*l+0],c=o[3*l+1],d=o[3*l+2],f=0;f<u;f+=1)s.push(t(c,d));return s}}),d=function(){var e=[],t=0,r={getBuffer:function(){return e},getAt:function(t){return 1==(e[Math.floor(t/8)]>>>7-t%8&1)},put:function(e,t){for(var n=0;n<t;n+=1)r.putBit(1==(e>>>t-n-1&1))},getLengthInBits:function(){return t},putBit:function(r){var n=Math.floor(t/8);e.length<=n&&e.push(0),r&&(e[n]|=128>>>t%8),t+=1}};return r},f=function(e){var t=function(e){for(var t=0,n=0;n<e.length;n+=1)t=10*t+r(e.charAt(n));return t},r=function(e){if("0"<=e&&e<="9")return e.charCodeAt(0)-48;throw"illegal char :"+e};return{getMode:function(){return 1},getLength:function(t){return e.length},write:function(r){for(var n=0;n+2<e.length;)r.put(t(e.substring(n,n+3)),10),n+=3;n<e.length&&(e.length-n==1?r.put(t(e.substring(n,n+1)),4):e.length-n==2&&r.put(t(e.substring(n,n+2)),7))}}},h=function(e){var t=function(e){if("0"<=e&&e<="9")return e.charCodeAt(0)-48;if("A"<=e&&e<="Z")return e.charCodeAt(0)-65+10;switch(e){case" ":return 36;case"$":return 37;case"%":return 38;case"*":return 39;case"+":return 40;case"-":return 41;case".":return 42;case"/":return 43;case":":return 44;default:throw"illegal char :"+e}};return{getMode:function(){return 2},getLength:function(t){return e.length},write:function(r){for(var n=0;n+1<e.length;)r.put(45*t(e.charAt(n))+t(e.charAt(n+1)),11),n+=2;n<e.length&&r.put(t(e.charAt(n)),6)}}},p=function(e){var t=r.stringToBytes(e);return{getMode:function(){return 4},getLength:function(e){return t.length},write:function(e){for(var r=0;r<t.length;r+=1)e.put(t[r],8)}}},g=function(e){var t=r.stringToBytesFuncs.SJIS;if(!t)throw"sjis not supported.";var n=t("友");if(2!=n.length||38726!=(n[0]<<8|n[1]))throw"sjis not supported.";var o=t(e);return{getMode:function(){return 8},getLength:function(e){return~~(o.length/2)},write:function(e){for(var t=0;t+1<o.length;){var r=(255&o[t])<<8|255&o[t+1];if(33088<=r&&r<=40956)r-=33088;else{if(!(57408<=r&&r<=60351))throw"illegal char at "+(t+1)+"/"+r;r-=49472}r=192*(r>>>8&255)+(255&r),e.put(r,13),t+=2}if(t<o.length)throw"illegal char at "+(t+1)}}},m=function(){var e=[],t={writeByte:function(t){e.push(255&t)},writeShort:function(e){t.writeByte(e),t.writeByte(e>>>8)},writeBytes:function(e,r,n){r=r||0,n=n||e.length;for(var o=0;o<n;o+=1)t.writeByte(e[o+r])},writeString:function(e){for(var r=0;r<e.length;r+=1)t.writeByte(e.charCodeAt(r))},toByteArray:function(){return e},toString:function(){var t="";t+="[";for(var r=0;r<e.length;r+=1)r>0&&(t+=","),t+=e[r];return t+"]"}};return t},v=function(e){var t=0,r=0,n=0,o=function(e){if(65<=e&&e<=90)return e-65;if(97<=e&&e<=122)return e-97+26;if(48<=e&&e<=57)return e-48+52;if(43==e)return 62;if(47==e)return 63;throw"c:"+e};return{read:function(){for(;n<8;){if(t>=e.length){if(0==n)return -1;throw"unexpected end of file./"+n}var i=e.charAt(t);if(t+=1,"="==i)return n=0,-1;i.match(/^\s$/)||(r=r<<6|o(i.charCodeAt(0)),n+=6)}var a=r>>>n-8&255;return n-=8,a}}},y=function(e,t,r){for(var n,o,i,a,s,l,u,c,d,f,h=(n=Array(e*t),o=function(e){for(var t=1<<e,r=1+(1<<e),o=e+1,a=i(),s=0;s<t;s+=1)a.add(String.fromCharCode(s));a.add(String.fromCharCode(t)),a.add(String.fromCharCode(r));var l,u,c=m(),d=(l=0,u=0,{write:function(e,t){if(e>>>t!=0)throw"length over";for(;l+t>=8;)c.writeByte(255&(e<<l|u)),t-=8-l,e>>>=8-l,u=0,l=0;u|=e<<l,l+=t},flush:function(){l>0&&c.writeByte(u)}});d.write(t,o);var f=0,h=String.fromCharCode(n[0]);for(f+=1;f<n.length;){var p=String.fromCharCode(n[f]);f+=1,a.contains(h+p)?h+=p:(d.write(a.indexOf(h),o),4095>a.size()&&(a.size()==1<<o&&(o+=1),a.add(h+p)),h=p)}return d.write(a.indexOf(h),o),d.write(r,o),d.flush(),c.toByteArray()},i=function(){var e={},t=0,r={add:function(n){if(r.contains(n))throw"dup key:"+n;e[n]=t,t+=1},size:function(){return t},indexOf:function(t){return e[t]},contains:function(t){return void 0!==e[t]}};return r},{setPixel:function(t,r,o){n[r*e+t]=o},write:function(r){r.writeString("GIF87a"),r.writeShort(e),r.writeShort(t),r.writeByte(128),r.writeByte(0),r.writeByte(0),r.writeByte(0),r.writeByte(0),r.writeByte(0),r.writeByte(255),r.writeByte(255),r.writeByte(255),r.writeString(","),r.writeShort(0),r.writeShort(0),r.writeShort(e),r.writeShort(t),r.writeByte(0);var n=o(2);r.writeByte(2);for(var i=0;n.length-i>255;)r.writeByte(255),r.writeBytes(n,i,255),i+=255;r.writeByte(n.length-i),r.writeBytes(n,i,n.length-i),r.writeByte(0),r.writeString(";")}}),p=0;p<t;p+=1)for(var g=0;g<e;g+=1)h.setPixel(g,p,r(g,p));var v=m();h.write(v);for(var y=(a=0,s=0,l=0,u="",c={},d=function(e){u+=String.fromCharCode(f(63&e))},f=function(e){if(e<0);else{if(e<26)return 65+e;if(e<52)return e-26+97;if(e<62)return e-52+48;if(62==e)return 43;if(63==e)return 47}throw"n:"+e},c.writeByte=function(e){for(a=a<<8|255&e,s+=8,l+=1;s>=6;)d(a>>>s-6),s-=6},c.flush=function(){if(s>0&&(d(a<<6-s),a=0,s=0),l%3!=0)for(var e=3-l%3,t=0;t<e;t+=1)u+="="},c.toString=function(){return u},c),b=v.toByteArray(),w=0;w<b.length;w+=1)y.writeByte(b[w]);return y.flush(),"data:image/gif;base64,"+y};return r}();o.stringToBytesFuncs["UTF-8"]=function(e){for(var t=[],r=0;r<e.length;r++){var n=e.charCodeAt(r);n<128?t.push(n):n<2048?t.push(192|n>>6,128|63&n):n<55296||n>=57344?t.push(224|n>>12,128|n>>6&63,128|63&n):(r++,n=65536+((1023&n)<<10|1023&e.charCodeAt(r)),t.push(240|n>>18,128|n>>12&63,128|n>>6&63,128|63&n))}return t},void 0===(n="function"==typeof(r=function(){return o})?r.apply(t,[]):r)||(e.exports=n)}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var o={};return(()=>{"use strict";r.d(o,{default:()=>j});let e=e=>!!e&&"object"==typeof e&&!Array.isArray(e);function t(r,...n){if(!n.length)return r;let o=n.shift();return void 0!==o&&e(r)&&e(o)?(r=Object.assign({},r),Object.keys(o).forEach(n=>{let i=r[n],a=o[n];Array.isArray(i)&&Array.isArray(a)?r[n]=a:e(i)&&e(a)?r[n]=t(Object.assign({},i),a):r[n]=a}),t(r,...n)):r}function i(e,t){let r=document.createElement("a");r.download=t,r.href=e,document.body.appendChild(r),r.click(),document.body.removeChild(r)}let a={L:.07,M:.15,Q:.25,H:.3};class s{constructor({svg:e,type:t,window:r}){this._svg=e,this._type=t,this._window=r}draw(e,t,r,n){let o;switch(this._type){case"dots":o=this._drawDot;break;case"classy":o=this._drawClassy;break;case"classy-rounded":o=this._drawClassyRounded;break;case"rounded":o=this._drawRounded;break;case"extra-rounded":o=this._drawExtraRounded;break;default:o=this._drawSquare}o.call(this,{x:e,y:t,size:r,getNeighbor:n})}_rotateFigure({x:e,y:t,size:r,rotation:n=0,draw:o}){var i;o(),null==(i=this._element)||i.setAttribute("transform",`rotate(${180*n/Math.PI},${e+r/2},${t+r/2})`)}_basicDot(e){let{size:t,x:r,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","circle"),this._element.setAttribute("cx",String(r+t/2)),this._element.setAttribute("cy",String(n+t/2)),this._element.setAttribute("r",String(t/2))}}))}_basicSquare(e){let{size:t,x:r,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect"),this._element.setAttribute("x",String(r)),this._element.setAttribute("y",String(n)),this._element.setAttribute("width",String(t)),this._element.setAttribute("height",String(t))}}))}_basicSideRounded(e){let{size:t,x:r,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${r} ${n}v ${t}h `+t/2+`a ${t/2} ${t/2}, 0, 0, 0, 0 ${-t}`)}}))}_basicCornerRounded(e){let{size:t,x:r,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${r} ${n}v ${t}h ${t}v `+-t/2+`a ${t/2} ${t/2}, 0, 0, 0, ${-t/2} ${-t/2}`)}}))}_basicCornerExtraRounded(e){let{size:t,x:r,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${r} ${n}v ${t}h ${t}a ${t} ${t}, 0, 0, 0, ${-t} ${-t}`)}}))}_basicCornersRounded(e){let{size:t,x:r,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${r} ${n}v `+t/2+`a ${t/2} ${t/2}, 0, 0, 0, ${t/2} ${t/2}h `+t/2+"v "+-t/2+`a ${t/2} ${t/2}, 0, 0, 0, ${-t/2} ${-t/2}`)}}))}_drawDot({x:e,y:t,size:r}){this._basicDot({x:e,y:t,size:r,rotation:0})}_drawSquare({x:e,y:t,size:r}){this._basicSquare({x:e,y:t,size:r,rotation:0})}_drawRounded({x:e,y:t,size:r,getNeighbor:n}){let o=n?+n(-1,0):0,i=n?+n(1,0):0,a=n?+n(0,-1):0,s=n?+n(0,1):0,l=o+i+a+s;if(0!==l)if(l>2||o&&i||a&&s)this._basicSquare({x:e,y:t,size:r,rotation:0});else{if(2===l){let n=0;return o&&a?n=Math.PI/2:a&&i?n=Math.PI:i&&s&&(n=-Math.PI/2),void this._basicCornerRounded({x:e,y:t,size:r,rotation:n})}if(1===l){let n=0;return a?n=Math.PI/2:i?n=Math.PI:s&&(n=-Math.PI/2),void this._basicSideRounded({x:e,y:t,size:r,rotation:n})}}else this._basicDot({x:e,y:t,size:r,rotation:0})}_drawExtraRounded({x:e,y:t,size:r,getNeighbor:n}){let o=n?+n(-1,0):0,i=n?+n(1,0):0,a=n?+n(0,-1):0,s=n?+n(0,1):0,l=o+i+a+s;if(0!==l)if(l>2||o&&i||a&&s)this._basicSquare({x:e,y:t,size:r,rotation:0});else{if(2===l){let n=0;return o&&a?n=Math.PI/2:a&&i?n=Math.PI:i&&s&&(n=-Math.PI/2),void this._basicCornerExtraRounded({x:e,y:t,size:r,rotation:n})}if(1===l){let n=0;return a?n=Math.PI/2:i?n=Math.PI:s&&(n=-Math.PI/2),void this._basicSideRounded({x:e,y:t,size:r,rotation:n})}}else this._basicDot({x:e,y:t,size:r,rotation:0})}_drawClassy({x:e,y:t,size:r,getNeighbor:n}){let o=n?+n(-1,0):0,i=n?+n(1,0):0,a=n?+n(0,-1):0,s=n?+n(0,1):0;0!==o+i+a+s?o||a?i||s?this._basicSquare({x:e,y:t,size:r,rotation:0}):this._basicCornerRounded({x:e,y:t,size:r,rotation:Math.PI/2}):this._basicCornerRounded({x:e,y:t,size:r,rotation:-Math.PI/2}):this._basicCornersRounded({x:e,y:t,size:r,rotation:Math.PI/2})}_drawClassyRounded({x:e,y:t,size:r,getNeighbor:n}){let o=n?+n(-1,0):0,i=n?+n(1,0):0,a=n?+n(0,-1):0,s=n?+n(0,1):0;0!==o+i+a+s?o||a?i||s?this._basicSquare({x:e,y:t,size:r,rotation:0}):this._basicCornerExtraRounded({x:e,y:t,size:r,rotation:Math.PI/2}):this._basicCornerExtraRounded({x:e,y:t,size:r,rotation:-Math.PI/2}):this._basicCornersRounded({x:e,y:t,size:r,rotation:Math.PI/2})}}let l={dot:"dot",square:"square",extraRounded:"extra-rounded"},u=Object.values(l);class c{constructor({svg:e,type:t,window:r}){this._svg=e,this._type=t,this._window=r}draw(e,t,r,n){let o;switch(this._type){case l.square:o=this._drawSquare;break;case l.extraRounded:o=this._drawExtraRounded;break;default:o=this._drawDot}o.call(this,{x:e,y:t,size:r,rotation:n})}_rotateFigure({x:e,y:t,size:r,rotation:n=0,draw:o}){var i;o(),null==(i=this._element)||i.setAttribute("transform",`rotate(${180*n/Math.PI},${e+r/2},${t+r/2})`)}_basicDot(e){let{size:t,x:r,y:n}=e,o=t/7;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("clip-rule","evenodd"),this._element.setAttribute("d",`M ${r+t/2} ${n}a ${t/2} ${t/2} 0 1 0 0.1 0zm 0 ${o}a ${t/2-o} ${t/2-o} 0 1 1 -0.1 0Z`)}}))}_basicSquare(e){let{size:t,x:r,y:n}=e,o=t/7;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("clip-rule","evenodd"),this._element.setAttribute("d",`M ${r} ${n}v ${t}h ${t}v `+-t+"z"+`M ${r+o} ${n+o}h `+(t-2*o)+"v "+(t-2*o)+"h "+(2*o-t)+"z")}}))}_basicExtraRounded(e){let{size:t,x:r,y:n}=e,o=t/7;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("clip-rule","evenodd"),this._element.setAttribute("d",`M ${r} ${n+2.5*o}v `+2*o+`a ${2.5*o} ${2.5*o}, 0, 0, 0, ${2.5*o} ${2.5*o}h `+2*o+`a ${2.5*o} ${2.5*o}, 0, 0, 0, ${2.5*o} ${-(2.5*o)}v `+-2*o+`a ${2.5*o} ${2.5*o}, 0, 0, 0, ${-(2.5*o)} ${-(2.5*o)}h `+-2*o+`a ${2.5*o} ${2.5*o}, 0, 0, 0, ${-(2.5*o)} ${2.5*o}`+`M ${r+2.5*o} ${n+o}h `+2*o+`a ${1.5*o} ${1.5*o}, 0, 0, 1, ${1.5*o} ${1.5*o}v `+2*o+`a ${1.5*o} ${1.5*o}, 0, 0, 1, ${-(1.5*o)} ${1.5*o}h `+-2*o+`a ${1.5*o} ${1.5*o}, 0, 0, 1, ${-(1.5*o)} ${-(1.5*o)}v `+-2*o+`a ${1.5*o} ${1.5*o}, 0, 0, 1, ${1.5*o} ${-(1.5*o)}`)}}))}_drawDot({x:e,y:t,size:r,rotation:n}){this._basicDot({x:e,y:t,size:r,rotation:n})}_drawSquare({x:e,y:t,size:r,rotation:n}){this._basicSquare({x:e,y:t,size:r,rotation:n})}_drawExtraRounded({x:e,y:t,size:r,rotation:n}){this._basicExtraRounded({x:e,y:t,size:r,rotation:n})}}let d={dot:"dot",square:"square"},f=Object.values(d);class h{constructor({svg:e,type:t,window:r}){this._svg=e,this._type=t,this._window=r}draw(e,t,r,n){(this._type===d.square?this._drawSquare:this._drawDot).call(this,{x:e,y:t,size:r,rotation:n})}_rotateFigure({x:e,y:t,size:r,rotation:n=0,draw:o}){var i;o(),null==(i=this._element)||i.setAttribute("transform",`rotate(${180*n/Math.PI},${e+r/2},${t+r/2})`)}_basicDot(e){let{size:t,x:r,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","circle"),this._element.setAttribute("cx",String(r+t/2)),this._element.setAttribute("cy",String(n+t/2)),this._element.setAttribute("r",String(t/2))}}))}_basicSquare(e){let{size:t,x:r,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect"),this._element.setAttribute("x",String(r)),this._element.setAttribute("y",String(n)),this._element.setAttribute("width",String(t)),this._element.setAttribute("height",String(t))}}))}_drawDot({x:e,y:t,size:r,rotation:n}){this._basicDot({x:e,y:t,size:r,rotation:n})}_drawSquare({x:e,y:t,size:r,rotation:n}){this._basicSquare({x:e,y:t,size:r,rotation:n})}}let p="circle",g=[[1,1,1,1,1,1,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,1,1,1,1,1,1]],m=[[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]];class v{constructor(e,t){this._roundSize=e=>this._options.dotsOptions.roundSize?Math.floor(e):e,this._window=t,this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","svg"),this._element.setAttribute("width",String(e.width)),this._element.setAttribute("height",String(e.height)),this._element.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),e.dotsOptions.roundSize||this._element.setAttribute("shape-rendering","crispEdges"),this._element.setAttribute("viewBox",`0 0 ${e.width} ${e.height}`),this._defs=this._window.document.createElementNS("http://www.w3.org/2000/svg","defs"),this._element.appendChild(this._defs),this._imageUri=e.image,this._instanceId=v.instanceCount++,this._options=e}get width(){return this._options.width}get height(){return this._options.height}getElement(){return this._element}async drawQR(e){let t=e.getModuleCount(),r=Math.min(this._options.width,this._options.height)-2*this._options.margin,n=this._options.shape===p?r/Math.sqrt(2):r,o=this._roundSize(n/t),i={hideXDots:0,hideYDots:0,width:0,height:0};if(this._qr=e,this._options.image){if(await this.loadImage(),!this._image)return;let{imageOptions:e,qrOptions:r}=this._options,n=Math.floor(e.imageSize*a[r.errorCorrectionLevel]*t*t);i=function({originalHeight:e,originalWidth:t,maxHiddenDots:r,maxHiddenAxisDots:n,dotSize:o}){let i={x:0,y:0},a={x:0,y:0};if(e<=0||t<=0||r<=0||o<=0)return{height:0,width:0,hideYDots:0,hideXDots:0};let s=e/t;return i.x=Math.floor(Math.sqrt(r/s)),i.x<=0&&(i.x=1),n&&n<i.x&&(i.x=n),i.x%2==0&&i.x--,a.x=i.x*o,i.y=1+2*Math.ceil((i.x*s-1)/2),a.y=Math.round(a.x*s),(i.y*i.x>r||n&&n<i.y)&&(n&&n<i.y?(i.y=n,i.y%2==0&&i.x--):i.y-=2,a.y=i.y*o,i.x=1+2*Math.ceil((i.y/s-1)/2),a.x=Math.round(a.y/s)),{height:a.y,width:a.x,hideYDots:i.y,hideXDots:i.x}}({originalWidth:this._image.width,originalHeight:this._image.height,maxHiddenDots:n,maxHiddenAxisDots:t-14,dotSize:o})}this.drawBackground(),this.drawDots((e,r)=>{var n,o,a,s,l,u;return!(this._options.imageOptions.hideBackgroundDots&&e>=(t-i.hideYDots)/2&&e<(t+i.hideYDots)/2&&r>=(t-i.hideXDots)/2&&r<(t+i.hideXDots)/2||(null==(n=g[e])?void 0:n[r])||(null==(o=g[e-t+7])?void 0:o[r])||(null==(a=g[e])?void 0:a[r-t+7])||(null==(s=m[e])?void 0:s[r])||(null==(l=m[e-t+7])?void 0:l[r])||(null==(u=m[e])?void 0:u[r-t+7]))}),this.drawCorners(),this._options.image&&await this.drawImage({width:i.width,height:i.height,count:t,dotSize:o})}drawBackground(){var e,t,r;let n=this._element,o=this._options;if(n){let n=null==(e=o.backgroundOptions)?void 0:e.gradient,i=null==(t=o.backgroundOptions)?void 0:t.color,a=o.height,s=o.width;if(n||i){let e=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect");this._backgroundClipPath=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),this._backgroundClipPath.setAttribute("id",`clip-path-background-color-${this._instanceId}`),this._defs.appendChild(this._backgroundClipPath),(null==(r=o.backgroundOptions)?void 0:r.round)&&(a=s=Math.min(o.width,o.height),e.setAttribute("rx",String(a/2*o.backgroundOptions.round))),e.setAttribute("x",String(this._roundSize((o.width-s)/2))),e.setAttribute("y",String(this._roundSize((o.height-a)/2))),e.setAttribute("width",String(s)),e.setAttribute("height",String(a)),this._backgroundClipPath.appendChild(e),this._createColor({options:n,color:i,additionalRotation:0,x:0,y:0,height:o.height,width:o.width,name:`background-color-${this._instanceId}`})}}}drawDots(e){var t,r;if(!this._qr)throw"QR code is not defined";let n=this._options,o=this._qr.getModuleCount();if(o>n.width||o>n.height)throw"The canvas is too small.";let i=Math.min(n.width,n.height)-2*n.margin,a=n.shape===p?i/Math.sqrt(2):i,l=this._roundSize(a/o),u=this._roundSize((n.width-o*l)/2),c=this._roundSize((n.height-o*l)/2),d=new s({svg:this._element,type:n.dotsOptions.type,window:this._window});this._dotsClipPath=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),this._dotsClipPath.setAttribute("id",`clip-path-dot-color-${this._instanceId}`),this._defs.appendChild(this._dotsClipPath),this._createColor({options:null==(t=n.dotsOptions)?void 0:t.gradient,color:n.dotsOptions.color,additionalRotation:0,x:0,y:0,height:n.height,width:n.width,name:`dot-color-${this._instanceId}`});for(let t=0;t<o;t++)for(let n=0;n<o;n++)e&&!e(t,n)||(null==(r=this._qr)?void 0:r.isDark(t,n))&&(d.draw(u+n*l,c+t*l,l,(r,i)=>!(n+r<0||t+i<0||n+r>=o||t+i>=o)&&!(e&&!e(t+i,n+r))&&!!this._qr&&this._qr.isDark(t+i,n+r)),d._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(d._element));if(n.shape===p){let e=this._roundSize((i/l-o)/2),t=o+2*e,r=u-e*l,n=c-e*l,a=[],s=this._roundSize(t/2);for(let r=0;r<t;r++){a[r]=[];for(let n=0;n<t;n++)r>=e-1&&r<=t-e&&n>=e-1&&n<=t-e||Math.sqrt((r-s)*(r-s)+(n-s)*(n-s))>s?a[r][n]=0:a[r][n]=+!!this._qr.isDark(n-2*e<0?n:n>=o?n-2*e:n-e,r-2*e<0?r:r>=o?r-2*e:r-e)}for(let e=0;e<t;e++)for(let o=0;o<t;o++)a[e][o]&&(d.draw(r+o*l,n+e*l,l,(t,r)=>{var n;return!!(null==(n=a[e+r])?void 0:n[o+t])}),d._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(d._element))}}drawCorners(){if(!this._qr)throw"QR code is not defined";let e=this._element,t=this._options;if(!e)throw"Element code is not defined";let r=this._qr.getModuleCount(),n=Math.min(t.width,t.height)-2*t.margin,o=t.shape===p?n/Math.sqrt(2):n,i=this._roundSize(o/r),a=7*i,l=3*i,d=this._roundSize((t.width-r*i)/2),v=this._roundSize((t.height-r*i)/2);[[0,0,0],[1,0,Math.PI/2],[0,1,-Math.PI/2]].forEach(([e,n,o])=>{var p,y,b,w,x,C,S,_,A,E,j,k,O,D;let P=d+e*i*(r-7),I=v+n*i*(r-7),R=this._dotsClipPath,T=this._dotsClipPath;if(((null==(p=t.cornersSquareOptions)?void 0:p.gradient)||(null==(y=t.cornersSquareOptions)?void 0:y.color))&&((R=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath")).setAttribute("id",`clip-path-corners-square-color-${e}-${n}-${this._instanceId}`),this._defs.appendChild(R),this._cornersSquareClipPath=this._cornersDotClipPath=T=R,this._createColor({options:null==(b=t.cornersSquareOptions)?void 0:b.gradient,color:null==(w=t.cornersSquareOptions)?void 0:w.color,additionalRotation:o,x:P,y:I,height:a,width:a,name:`corners-square-color-${e}-${n}-${this._instanceId}`})),(null==(x=t.cornersSquareOptions)?void 0:x.type)&&u.includes(t.cornersSquareOptions.type)){let e=new c({svg:this._element,type:t.cornersSquareOptions.type,window:this._window});e.draw(P,I,a,o),e._element&&R&&R.appendChild(e._element)}else{let e=new s({svg:this._element,type:(null==(C=t.cornersSquareOptions)?void 0:C.type)||t.dotsOptions.type,window:this._window});for(let t=0;t<g.length;t++)for(let r=0;r<g[t].length;r++)(null==(S=g[t])?void 0:S[r])&&(e.draw(P+r*i,I+t*i,i,(e,n)=>{var o;return!!(null==(o=g[t+n])?void 0:o[r+e])}),e._element&&R&&R.appendChild(e._element))}if(((null==(_=t.cornersDotOptions)?void 0:_.gradient)||(null==(A=t.cornersDotOptions)?void 0:A.color))&&((T=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath")).setAttribute("id",`clip-path-corners-dot-color-${e}-${n}-${this._instanceId}`),this._defs.appendChild(T),this._cornersDotClipPath=T,this._createColor({options:null==(E=t.cornersDotOptions)?void 0:E.gradient,color:null==(j=t.cornersDotOptions)?void 0:j.color,additionalRotation:o,x:P+2*i,y:I+2*i,height:l,width:l,name:`corners-dot-color-${e}-${n}-${this._instanceId}`})),(null==(k=t.cornersDotOptions)?void 0:k.type)&&f.includes(t.cornersDotOptions.type)){let e=new h({svg:this._element,type:t.cornersDotOptions.type,window:this._window});e.draw(P+2*i,I+2*i,l,o),e._element&&T&&T.appendChild(e._element)}else{let e=new s({svg:this._element,type:(null==(O=t.cornersDotOptions)?void 0:O.type)||t.dotsOptions.type,window:this._window});for(let t=0;t<m.length;t++)for(let r=0;r<m[t].length;r++)(null==(D=m[t])?void 0:D[r])&&(e.draw(P+r*i,I+t*i,i,(e,n)=>{var o;return!!(null==(o=m[t+n])?void 0:o[r+e])}),e._element&&T&&T.appendChild(e._element))}})}loadImage(){return new Promise((e,t)=>{var r;let n=this._options;if(!n.image)return t("Image is not defined");if(null==(r=n.nodeCanvas)?void 0:r.loadImage)n.nodeCanvas.loadImage(n.image).then(t=>{var r,o;if(this._image=t,this._options.imageOptions.saveAsBlob){let e=null==(r=n.nodeCanvas)?void 0:r.createCanvas(this._image.width,this._image.height);null==(o=null==e?void 0:e.getContext("2d"))||o.drawImage(t,0,0),this._imageUri=null==e?void 0:e.toDataURL()}e()}).catch(t);else{let t=new this._window.Image;"string"==typeof n.imageOptions.crossOrigin&&(t.crossOrigin=n.imageOptions.crossOrigin),this._image=t,t.onload=async()=>{this._options.imageOptions.saveAsBlob&&(this._imageUri=await async function(e,t){return new Promise(r=>{let n=new t.XMLHttpRequest;n.onload=function(){let e=new t.FileReader;e.onloadend=function(){r(e.result)},e.readAsDataURL(n.response)},n.open("GET",e),n.responseType="blob",n.send()})}(n.image||"",this._window)),e()},t.src=n.image}})}async drawImage({width:e,height:t,count:r,dotSize:n}){let o=this._options,i=this._roundSize((o.width-r*n)/2),a=this._roundSize((o.height-r*n)/2),s=i+this._roundSize(o.imageOptions.margin+(r*n-e)/2),l=a+this._roundSize(o.imageOptions.margin+(r*n-t)/2),u=e-2*o.imageOptions.margin,c=t-2*o.imageOptions.margin,d=this._window.document.createElementNS("http://www.w3.org/2000/svg","image");d.setAttribute("href",this._imageUri||""),d.setAttribute("x",String(s)),d.setAttribute("y",String(l)),d.setAttribute("width",`${u}px`),d.setAttribute("height",`${c}px`),this._element.appendChild(d)}_createColor({options:e,color:t,additionalRotation:r,x:n,y:o,height:i,width:a,name:s}){let l=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect");if(l.setAttribute("x",String(n)),l.setAttribute("y",String(o)),l.setAttribute("height",String(i)),l.setAttribute("width",String(a)),l.setAttribute("clip-path",`url('#clip-path-${s}')`),e){let t;if("radial"===e.type)(t=this._window.document.createElementNS("http://www.w3.org/2000/svg","radialGradient")).setAttribute("id",s),t.setAttribute("gradientUnits","userSpaceOnUse"),t.setAttribute("fx",String(n+a/2)),t.setAttribute("fy",String(o+i/2)),t.setAttribute("cx",String(n+a/2)),t.setAttribute("cy",String(o+i/2)),t.setAttribute("r",String((a>i?a:i)/2));else{let l=((e.rotation||0)+r)%(2*Math.PI),u=(l+2*Math.PI)%(2*Math.PI),c=n+a/2,d=o+i/2,f=n+a/2,h=o+i/2;u>=0&&u<=.25*Math.PI||u>1.75*Math.PI&&u<=2*Math.PI?(c-=a/2,d-=i/2*Math.tan(l),f+=a/2,h+=i/2*Math.tan(l)):u>.25*Math.PI&&u<=.75*Math.PI?(d-=i/2,c-=a/2/Math.tan(l),h+=i/2,f+=a/2/Math.tan(l)):u>.75*Math.PI&&u<=1.25*Math.PI?(c+=a/2,d+=i/2*Math.tan(l),f-=a/2,h-=i/2*Math.tan(l)):u>1.25*Math.PI&&u<=1.75*Math.PI&&(d+=i/2,c+=a/2/Math.tan(l),h-=i/2,f-=a/2/Math.tan(l)),(t=this._window.document.createElementNS("http://www.w3.org/2000/svg","linearGradient")).setAttribute("id",s),t.setAttribute("gradientUnits","userSpaceOnUse"),t.setAttribute("x1",String(Math.round(c))),t.setAttribute("y1",String(Math.round(d))),t.setAttribute("x2",String(Math.round(f))),t.setAttribute("y2",String(Math.round(h)))}e.colorStops.forEach(({offset:e,color:r})=>{let n=this._window.document.createElementNS("http://www.w3.org/2000/svg","stop");n.setAttribute("offset",100*e+"%"),n.setAttribute("stop-color",r),t.appendChild(n)}),l.setAttribute("fill",`url('#${s}')`),this._defs.appendChild(t)}else t&&l.setAttribute("fill",t);this._element.appendChild(l)}}v.instanceCount=0;let y="canvas",b={};for(let e=0;e<=40;e++)b[e]=e;let w={type:y,shape:"square",width:300,height:300,data:"",margin:0,qrOptions:{typeNumber:b[0],mode:void 0,errorCorrectionLevel:"Q"},imageOptions:{saveAsBlob:!0,hideBackgroundDots:!0,imageSize:.4,crossOrigin:void 0,margin:0},dotsOptions:{type:"square",color:"#000",roundSize:!0},backgroundOptions:{round:0,color:"#fff"}};function x(e){let t=Object.assign({},e);if(!t.colorStops||!t.colorStops.length)throw"Field 'colorStops' is required in gradient";return t.rotation?t.rotation=Number(t.rotation):t.rotation=0,t.colorStops=t.colorStops.map(e=>Object.assign(Object.assign({},e),{offset:Number(e.offset)})),t}function C(e){let t=Object.assign({},e);return t.width=Number(t.width),t.height=Number(t.height),t.margin=Number(t.margin),t.imageOptions=Object.assign(Object.assign({},t.imageOptions),{hideBackgroundDots:!!t.imageOptions.hideBackgroundDots,imageSize:Number(t.imageOptions.imageSize),margin:Number(t.imageOptions.margin)}),t.margin>Math.min(t.width,t.height)&&(t.margin=Math.min(t.width,t.height)),t.dotsOptions=Object.assign({},t.dotsOptions),t.dotsOptions.gradient&&(t.dotsOptions.gradient=x(t.dotsOptions.gradient)),t.cornersSquareOptions&&(t.cornersSquareOptions=Object.assign({},t.cornersSquareOptions),t.cornersSquareOptions.gradient&&(t.cornersSquareOptions.gradient=x(t.cornersSquareOptions.gradient))),t.cornersDotOptions&&(t.cornersDotOptions=Object.assign({},t.cornersDotOptions),t.cornersDotOptions.gradient&&(t.cornersDotOptions.gradient=x(t.cornersDotOptions.gradient))),t.backgroundOptions&&(t.backgroundOptions=Object.assign({},t.backgroundOptions),t.backgroundOptions.gradient&&(t.backgroundOptions.gradient=x(t.backgroundOptions.gradient))),t}var S=r(873),_=r.n(S);function A(e){if(!e)throw Error("Extension must be defined");"."===e[0]&&(e=e.substring(1));let t={bmp:"image/bmp",gif:"image/gif",ico:"image/vnd.microsoft.icon",jpeg:"image/jpeg",jpg:"image/jpeg",png:"image/png",svg:"image/svg+xml",tif:"image/tiff",tiff:"image/tiff",webp:"image/webp",pdf:"application/pdf"}[e.toLowerCase()];if(!t)throw Error(`Extension "${e}" is not supported`);return t}class E{constructor(e){(null==e?void 0:e.jsdom)?this._window=new e.jsdom("",{resources:"usable"}).window:this._window=window,this._options=e?C(t(w,e)):w,this.update()}static _clearContainer(e){e&&(e.innerHTML="")}_setupSvg(){if(!this._qr)return;let e=new v(this._options,this._window);this._svg=e.getElement(),this._svgDrawingPromise=e.drawQR(this._qr).then(()=>{var t;this._svg&&(null==(t=this._extension)||t.call(this,e.getElement(),this._options))})}_setupCanvas(){var e,t;this._qr&&((null==(e=this._options.nodeCanvas)?void 0:e.createCanvas)?(this._nodeCanvas=this._options.nodeCanvas.createCanvas(this._options.width,this._options.height),this._nodeCanvas.width=this._options.width,this._nodeCanvas.height=this._options.height):(this._domCanvas=document.createElement("canvas"),this._domCanvas.width=this._options.width,this._domCanvas.height=this._options.height),this._setupSvg(),this._canvasDrawingPromise=null==(t=this._svgDrawingPromise)?void 0:t.then(()=>{var e;if(!this._svg)return;let t=this._svg,r=btoa((new this._window.XMLSerializer).serializeToString(t)),n=`data:${A("svg")};base64,${r}`;if(null==(e=this._options.nodeCanvas)?void 0:e.loadImage)return this._options.nodeCanvas.loadImage(n).then(e=>{var t,r;e.width=this._options.width,e.height=this._options.height,null==(r=null==(t=this._nodeCanvas)?void 0:t.getContext("2d"))||r.drawImage(e,0,0)});{let e=new this._window.Image;return new Promise(t=>{e.onload=()=>{var r,n;null==(n=null==(r=this._domCanvas)?void 0:r.getContext("2d"))||n.drawImage(e,0,0),t()},e.src=n})}}))}async _getElement(e="png"){if(!this._qr)throw"QR code is empty";return"svg"===e.toLowerCase()?(this._svg&&this._svgDrawingPromise||this._setupSvg(),await this._svgDrawingPromise,this._svg):((this._domCanvas||this._nodeCanvas)&&this._canvasDrawingPromise||this._setupCanvas(),await this._canvasDrawingPromise,this._domCanvas||this._nodeCanvas)}update(e){E._clearContainer(this._container),this._options=e?C(t(this._options,e)):this._options,this._options.data&&(this._qr=_()(this._options.qrOptions.typeNumber,this._options.qrOptions.errorCorrectionLevel),this._qr.addData(this._options.data,this._options.qrOptions.mode||function(e){switch(!0){case/^[0-9]*$/.test(e):return"Numeric";case/^[0-9A-Z $%*+\-./:]*$/.test(e):return"Alphanumeric";default:return"Byte"}}(this._options.data)),this._qr.make(),this._options.type===y?this._setupCanvas():this._setupSvg(),this.append(this._container))}append(e){if(e){if("function"!=typeof e.appendChild)throw"Container should be a single DOM node";this._options.type===y?this._domCanvas&&e.appendChild(this._domCanvas):this._svg&&e.appendChild(this._svg),this._container=e}}applyExtension(e){if(!e)throw"Extension function should be defined.";this._extension=e,this.update()}deleteExtension(){this._extension=void 0,this.update()}async getRawData(e="png"){if(!this._qr)throw"QR code is empty";let t=await this._getElement(e),r=A(e);if(!t)return null;if("svg"===e.toLowerCase()){let e=`<?xml version="1.0" standalone="no"?>\r
${(new this._window.XMLSerializer).serializeToString(t)}`;return"undefined"==typeof Blob||this._options.jsdom?n.from(e):new Blob([e],{type:r})}return new Promise(e=>{if("toBuffer"in t)if("image/png"===r)e(t.toBuffer(r));else if("image/jpeg"===r)e(t.toBuffer(r));else{if("application/pdf"!==r)throw Error("Unsupported extension");e(t.toBuffer(r))}else"toBlob"in t&&t.toBlob(e,r,1)})}async download(e){if(!this._qr)throw"QR code is empty";if("undefined"==typeof Blob)throw"Cannot download in Node.js, call getRawData instead.";let t="png",r="qr";"string"==typeof e?(t=e,console.warn("Extension is deprecated as argument for 'download' method, please pass object { name: '...', extension: '...' } as argument")):"object"==typeof e&&null!==e&&(e.name&&(r=e.name),e.extension&&(t=e.extension));let n=await this._getElement(t);if(n)if("svg"===t.toLowerCase()){let e=(new XMLSerializer).serializeToString(n);e='<?xml version="1.0" standalone="no"?>\r\n'+e,i(`data:${A(t)};charset=utf-8,${encodeURIComponent(e)}`,`${r}.svg`)}else i(n.toDataURL(A(t)),`${r}.${t}`)}}let j=E})(),o.default})()},6301:(e,t,r)=>{"use strict";function n(){return"undefined"!=typeof window}function o(e){return s(e)?(e.nodeName||"").toLowerCase():"#document"}function i(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function a(e){var t;return null==(t=(s(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function s(e){return!!n()&&(e instanceof Node||e instanceof i(e).Node)}function l(e){return!!n()&&(e instanceof Element||e instanceof i(e).Element)}function u(e){return!!n()&&(e instanceof HTMLElement||e instanceof i(e).HTMLElement)}function c(e){return!!n()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof i(e).ShadowRoot)}r.d(t,{$4:()=>j,CP:()=>E,L9:()=>A,Lv:()=>p,Tc:()=>C,Tf:()=>m,ZU:()=>f,_m:()=>k,ep:()=>a,eu:()=>_,gJ:()=>x,mq:()=>o,sQ:()=>w,sb:()=>u,v9:()=>function e(t,r,n){var o;void 0===r&&(r=[]),void 0===n&&(n=!0);let a=function e(t){let r=j(t);return _(r)?t.ownerDocument?t.ownerDocument.body:t.body:u(r)&&f(r)?r:e(r)}(t),s=a===(null==(o=t.ownerDocument)?void 0:o.body),l=i(a);if(s){let t=k(l);return r.concat(l,l.visualViewport||[],f(a)?a:[],t&&n?e(t):[])}return r.concat(a,e(a,[],n))},vq:()=>l,zk:()=>i});let d=new Set(["inline","contents"]);function f(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=A(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!d.has(o)}let h=new Set(["table","td","th"]);function p(e){return h.has(o(e))}let g=[":popover-open",":modal"];function m(e){return g.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let v=["transform","translate","scale","rotate","perspective"],y=["transform","translate","scale","rotate","perspective","filter"],b=["paint","layout","strict","content"];function w(e){let t=C(),r=l(e)?A(e):e;return v.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||y.some(e=>(r.willChange||"").includes(e))||b.some(e=>(r.contain||"").includes(e))}function x(e){let t=j(e);for(;u(t)&&!_(t);){if(w(t))return t;if(m(t))break;t=j(t)}return null}function C(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let S=new Set(["html","body","#document"]);function _(e){return S.has(o(e))}function A(e){return i(e).getComputedStyle(e)}function E(e){return l(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function j(e){if("html"===o(e))return e;let t=e.assignedSlot||e.parentNode||c(e)&&e.host||a(e);return c(t)?t.host:t}function k(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}},6492:(e,t,r)=>{"use strict";r.d(t,{UE:()=>Q,ll:()=>W,rD:()=>er,UU:()=>K,mG:()=>ee,ER:()=>et,cY:()=>X,BN:()=>J,Ej:()=>Z});let n=Math.min,o=Math.max,i=Math.round,a=Math.floor,s=e=>({x:e,y:e}),l={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function c(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function f(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function p(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function m(e){return g.has(d(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>u[e])}let y=["left","right"],b=["right","left"],w=["top","bottom"],x=["bottom","top"];function C(e){return e.replace(/left|right|bottom|top/g,e=>l[e])}function S(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function _(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function A(e,t,r){let n,{reference:o,floating:i}=e,a=m(t),s=h(m(t)),l=p(s),u=d(t),c="y"===a,g=o.x+o.width/2-i.width/2,v=o.y+o.height/2-i.height/2,y=o[l]/2-i[l]/2;switch(u){case"top":n={x:g,y:o.y-i.height};break;case"bottom":n={x:g,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:v};break;case"left":n={x:o.x-i.width,y:v};break;default:n={x:o.x,y:o.y}}switch(f(t)){case"start":n[s]-=y*(r&&c?-1:1);break;case"end":n[s]+=y*(r&&c?-1:1)}return n}let E=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:a}=r,s=i.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=A(u,n,l),f=n,h={},p=0;for(let r=0;r<s.length;r++){let{name:i,fn:g}=s[r],{x:m,y:v,data:y,reset:b}=await g({x:c,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=m?m:c,d=null!=v?v:d,h={...h,[i]:{...h[i],...y}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=A(u,f,l)),r=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:h}};async function j(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:h=!1,padding:p=0}=c(t,e),g=S(p),m=s[h?"floating"===f?"reference":"floating":f],v=_(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(m)))||r?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:d,strategy:l})),y="floating"===f?{x:n,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),w=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},x=_(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:b,strategy:l}):y);return{top:(v.top-x.top+g.top)/w.y,bottom:(x.bottom-v.bottom+g.bottom)/w.y,left:(v.left-x.left+g.left)/w.x,right:(x.right-v.right+g.right)/w.x}}function k(e){let t=n(...e.map(e=>e.left)),r=n(...e.map(e=>e.top));return{x:t,y:r,width:o(...e.map(e=>e.right))-t,height:o(...e.map(e=>e.bottom))-r}}let O=new Set(["left","top"]);async function D(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),a=d(r),s=f(r),l="y"===m(r),u=O.has(a)?-1:1,h=i&&l?-1:1,p=c(t,e),{mainAxis:g,crossAxis:v,alignmentAxis:y}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return s&&"number"==typeof y&&(v="end"===s?-1*y:y),l?{x:v*h,y:g*u}:{x:g*u,y:v*h}}var P=r(6301);function I(e){let t=(0,P.L9)(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=(0,P.sb)(e),a=o?e.offsetWidth:r,s=o?e.offsetHeight:n,l=i(r)!==a||i(n)!==s;return l&&(r=a,n=s),{width:r,height:n,$:l}}function R(e){return(0,P.vq)(e)?e:e.contextElement}function T(e){let t=R(e);if(!(0,P.sb)(t))return s(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:a}=I(t),l=(a?i(r.width):r.width)/n,u=(a?i(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let L=s(0);function M(e){let t=(0,P.zk)(e);return(0,P.Tc)()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:L}function N(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),a=R(e),l=s(1);t&&(n?(0,P.vq)(n)&&(l=T(n)):l=T(e));let u=(void 0===(o=r)&&(o=!1),n&&(!o||n===(0,P.zk)(a))&&o)?M(a):s(0),c=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,h=i.height/l.y;if(a){let e=(0,P.zk)(a),t=n&&(0,P.vq)(n)?(0,P.zk)(n):n,r=e,o=(0,P._m)(r);for(;o&&n&&t!==r;){let e=T(o),t=o.getBoundingClientRect(),n=(0,P.L9)(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,h*=e.y,c+=i,d+=a,r=(0,P.zk)(o),o=(0,P._m)(r)}}return _({width:f,height:h,x:c,y:d})}function z(e,t){let r=(0,P.CP)(e).scrollLeft;return t?t.left+r:N((0,P.ep)(e)).left+r}function B(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:z(e,n)),y:n.top+t.scrollTop}}let Y=new Set(["absolute","fixed"]);function F(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=(0,P.zk)(e),n=(0,P.ep)(e),o=r.visualViewport,i=n.clientWidth,a=n.clientHeight,s=0,l=0;if(o){i=o.width,a=o.height;let e=(0,P.Tc)();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:s,y:l}}(e,r);else if("document"===t)n=function(e){let t=(0,P.ep)(e),r=(0,P.CP)(e),n=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=o(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),s=-r.scrollLeft+z(e),l=-r.scrollTop;return"rtl"===(0,P.L9)(n).direction&&(s+=o(t.clientWidth,n.clientWidth)-i),{width:i,height:a,x:s,y:l}}((0,P.ep)(e));else if((0,P.vq)(t))n=function(e,t){let r=N(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=(0,P.sb)(e)?T(e):s(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:n*i.y}}(t,r);else{let r=M(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return _(n)}function $(e){return"static"===(0,P.L9)(e).position}function q(e,t){if(!(0,P.sb)(e)||"fixed"===(0,P.L9)(e).position)return null;if(t)return t(e);let r=e.offsetParent;return(0,P.ep)(e)===r&&(r=r.ownerDocument.body),r}function V(e,t){let r=(0,P.zk)(e);if((0,P.Tf)(e))return r;if(!(0,P.sb)(e)){let t=(0,P.$4)(e);for(;t&&!(0,P.eu)(t);){if((0,P.vq)(t)&&!$(t))return t;t=(0,P.$4)(t)}return r}let n=q(e,t);for(;n&&(0,P.Lv)(n)&&$(n);)n=q(n,t);return n&&(0,P.eu)(n)&&$(n)&&!(0,P.sQ)(n)?r:n||(0,P.gJ)(e)||r}let U=async function(e){let t=this.getOffsetParent||V,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=(0,P.sb)(t),o=(0,P.ep)(t),i="fixed"===r,a=N(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=s(0);if(n||!n&&!i)if(("body"!==(0,P.mq)(t)||(0,P.ZU)(o))&&(l=(0,P.CP)(t)),n){let e=N(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=z(o));i&&!n&&o&&(u.x=z(o));let c=!o||n||i?s(0):B(o,l);return{x:a.left+l.scrollLeft-u.x-c.x,y:a.top+l.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},H={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,a=(0,P.ep)(n),l=!!t&&(0,P.Tf)(t.floating);if(n===a||l&&i)return r;let u={scrollLeft:0,scrollTop:0},c=s(1),d=s(0),f=(0,P.sb)(n);if((f||!f&&!i)&&(("body"!==(0,P.mq)(n)||(0,P.ZU)(a))&&(u=(0,P.CP)(n)),(0,P.sb)(n))){let e=N(n);c=T(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let h=!a||f||i?s(0):B(a,u,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-u.scrollLeft*c.x+d.x+h.x,y:r.y*c.y-u.scrollTop*c.y+d.y+h.y}},getDocumentElement:P.ep,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:i,strategy:a}=e,s=[..."clippingAncestors"===r?(0,P.Tf)(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=(0,P.v9)(e,[],!1).filter(e=>(0,P.vq)(e)&&"body"!==(0,P.mq)(e)),o=null,i="fixed"===(0,P.L9)(e).position,a=i?(0,P.$4)(e):e;for(;(0,P.vq)(a)&&!(0,P.eu)(a);){let t=(0,P.L9)(a),r=(0,P.sQ)(a);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&Y.has(o.position)||(0,P.ZU)(a)&&!r&&function e(t,r){let n=(0,P.$4)(t);return!(n===r||!(0,P.vq)(n)||(0,P.eu)(n))&&("fixed"===(0,P.L9)(n).position||e(n,r))}(e,a))?n=n.filter(e=>e!==a):o=t,a=(0,P.$4)(a)}return t.set(e,n),n}(t,this._c):[].concat(r),i],l=s[0],u=s.reduce((e,r)=>{let i=F(t,r,a);return e.top=o(i.top,e.top),e.right=n(i.right,e.right),e.bottom=n(i.bottom,e.bottom),e.left=o(i.left,e.left),e},F(t,l,a));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:V,getElementRects:U,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=I(e);return{width:t,height:r}},getScale:T,isElement:P.vq,isRTL:function(e){return"rtl"===(0,P.L9)(e).direction}};function G(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function W(e,t,r,i){let s;void 0===i&&(i={});let{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=i,h=R(e),p=l||u?[...h?(0,P.v9)(h):[],...(0,P.v9)(t)]:[];p.forEach(e=>{l&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let g=h&&d?function(e,t){let r,i=null,s=(0,P.ep)(e);function l(){var e;clearTimeout(r),null==(e=i)||e.disconnect(),i=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:h,top:p,width:g,height:m}=f;if(c||t(),!g||!m)return;let v=a(p),y=a(s.clientWidth-(h+g)),b={rootMargin:-v+"px "+-y+"px "+-a(s.clientHeight-(p+m))+"px "+-a(h)+"px",threshold:o(0,n(1,d))||1},w=!0;function x(t){let n=t[0].intersectionRatio;if(n!==d){if(!w)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||G(f,e.getBoundingClientRect())||u(),w=!1}try{i=new IntersectionObserver(x,{...b,root:s.ownerDocument})}catch(e){i=new IntersectionObserver(x,b)}i.observe(e)}(!0),l}(h,r):null,m=-1,v=null;c&&(v=new ResizeObserver(e=>{let[n]=e;n&&n.target===h&&v&&(v.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),r()}),h&&!f&&v.observe(h),v.observe(t));let y=f?N(e):null;return f&&function t(){let n=N(e);y&&!G(y,n)&&r(),y=n,s=requestAnimationFrame(t)}(),r(),()=>{var e;p.forEach(e=>{l&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==g||g(),null==(e=v)||e.disconnect(),v=null,f&&cancelAnimationFrame(s)}}let X=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:a,middlewareData:s}=t,l=await D(t,e);return a===(null==(r=s.offset)?void 0:r.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:a}}}}},J=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:i,placement:a}=t,{mainAxis:s=!0,crossAxis:l=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...f}=c(e,t),p={x:r,y:i},g=await j(t,f),v=m(d(a)),y=h(v),b=p[y],w=p[v];if(s){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",r=b+g[e],i=b-g[t];b=o(r,n(b,i))}if(l){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=w+g[e],i=w-g[t];w=o(r,n(w,i))}let x=u.fn({...t,[y]:b,[v]:w});return{...x,data:{x:x.x-r,y:x.y-i,enabled:{[y]:s,[v]:l}}}}}},K=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,a;let{placement:s,middlewareData:l,rects:u,initialPlacement:g,platform:S,elements:_}=t,{mainAxis:A=!0,crossAxis:E=!0,fallbackPlacements:k,fallbackStrategy:O="bestFit",fallbackAxisSideDirection:D="none",flipAlignment:P=!0,...I}=c(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let R=d(s),T=m(g),L=d(g)===g,M=await (null==S.isRTL?void 0:S.isRTL(_.floating)),N=k||(L||!P?[C(g)]:function(e){let t=C(e);return[v(e),t,v(t)]}(g)),z="none"!==D;!k&&z&&N.push(...function(e,t,r,n){let o=f(e),i=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?b:y;return t?y:b;case"left":case"right":return t?w:x;default:return[]}}(d(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(v)))),i}(g,P,D,M));let B=[g,...N],Y=await j(t,I),F=[],$=(null==(n=l.flip)?void 0:n.overflows)||[];if(A&&F.push(Y[R]),E){let e=function(e,t,r){void 0===r&&(r=!1);let n=f(e),o=h(m(e)),i=p(o),a="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=C(a)),[a,C(a)]}(s,u,M);F.push(Y[e[0]],Y[e[1]])}if($=[...$,{placement:s,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=l.flip)?void 0:o.index)||0)+1,t=B[e];if(t&&("alignment"!==E||T===m(t)||$.every(e=>e.overflows[0]>0&&m(e.placement)===T)))return{data:{index:e,overflows:$},reset:{placement:t}};let r=null==(i=$.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(O){case"bestFit":{let e=null==(a=$.filter(e=>{if(z){let t=m(e.placement);return t===T||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(r=e);break}case"initialPlacement":r=g}if(s!==r)return{reset:{placement:r}}}return{}}}},Z=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,i;let a,s,{placement:l,rects:u,platform:h,elements:p}=t,{apply:g=()=>{},...v}=c(e,t),y=await j(t,v),b=d(l),w=f(l),x="y"===m(l),{width:C,height:S}=u.floating;"top"===b||"bottom"===b?(a=b,s=w===(await (null==h.isRTL?void 0:h.isRTL(p.floating))?"start":"end")?"left":"right"):(s=b,a="end"===w?"top":"bottom");let _=S-y.top-y.bottom,A=C-y.left-y.right,E=n(S-y[a],_),k=n(C-y[s],A),O=!t.middlewareData.shift,D=E,P=k;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(P=A),null!=(i=t.middlewareData.shift)&&i.enabled.y&&(D=_),O&&!w){let e=o(y.left,0),t=o(y.right,0),r=o(y.top,0),n=o(y.bottom,0);x?P=C-2*(0!==e||0!==t?e+t:o(y.left,y.right)):D=S-2*(0!==r||0!==n?r+n:o(y.top,y.bottom))}await g({...t,availableWidth:P,availableHeight:D});let I=await h.getDimensions(p.floating);return C!==I.width||S!==I.height?{reset:{rects:!0}}:{}}}},Q=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:i,placement:a,rects:s,platform:l,elements:u,middlewareData:d}=t,{element:g,padding:v=0}=c(e,t)||{};if(null==g)return{};let y=S(v),b={x:r,y:i},w=h(m(a)),x=p(w),C=await l.getDimensions(g),_="y"===w,A=_?"clientHeight":"clientWidth",E=s.reference[x]+s.reference[w]-b[w]-s.floating[x],j=b[w]-s.reference[w],k=await (null==l.getOffsetParent?void 0:l.getOffsetParent(g)),O=k?k[A]:0;O&&await (null==l.isElement?void 0:l.isElement(k))||(O=u.floating[A]||s.floating[x]);let D=O/2-C[x]/2-1,P=n(y[_?"top":"left"],D),I=n(y[_?"bottom":"right"],D),R=O-C[x]-I,T=O/2-C[x]/2+(E/2-j/2),L=o(P,n(T,R)),M=!d.arrow&&null!=f(a)&&T!==L&&s.reference[x]/2-(T<P?P:I)-C[x]/2<0,N=M?T<P?T-P:T-R:0;return{[w]:b[w]+N,data:{[w]:L,centerOffset:T-L-N,...M&&{alignmentOffset:N}},reset:M}}}),ee=function(e){return void 0===e&&(e={}),{name:"inline",options:e,async fn(t){let{placement:r,elements:i,rects:a,platform:s,strategy:l}=t,{padding:u=2,x:f,y:h}=c(e,t),p=Array.from(await (null==s.getClientRects?void 0:s.getClientRects(i.reference))||[]),g=function(e){let t=e.slice().sort((e,t)=>e.y-t.y),r=[],n=null;for(let e=0;e<t.length;e++){let o=t[e];!n||o.y-n.y>n.height/2?r.push([o]):r[r.length-1].push(o),n=o}return r.map(e=>_(k(e)))}(p),v=_(k(p)),y=S(u),b=await s.getElementRects({reference:{getBoundingClientRect:function(){if(2===g.length&&g[0].left>g[1].right&&null!=f&&null!=h)return g.find(e=>f>e.left-y.left&&f<e.right+y.right&&h>e.top-y.top&&h<e.bottom+y.bottom)||v;if(g.length>=2){if("y"===m(r)){let e=g[0],t=g[g.length-1],n="top"===d(r),o=e.top,i=t.bottom,a=n?e.left:t.left,s=n?e.right:t.right;return{top:o,bottom:i,left:a,right:s,width:s-a,height:i-o,x:a,y:o}}let e="left"===d(r),t=o(...g.map(e=>e.right)),i=n(...g.map(e=>e.left)),a=g.filter(r=>e?r.left===i:r.right===t),s=a[0].top,l=a[a.length-1].bottom;return{top:s,bottom:l,left:i,right:t,width:t-i,height:l-s,x:i,y:s}}return v}},floating:i.floating,strategy:l});return a.reference.x!==b.reference.x||a.reference.y!==b.reference.y||a.reference.width!==b.reference.width||a.reference.height!==b.reference.height?{reset:{rects:b}}:{}}}},et=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:a}=t,{offset:s=0,mainAxis:l=!0,crossAxis:u=!0}=c(e,t),f={x:r,y:n},p=m(o),g=h(p),v=f[g],y=f[p],b=c(s,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(l){let e="y"===g?"height":"width",t=i.reference[g]-i.floating[e]+w.mainAxis,r=i.reference[g]+i.reference[e]-w.mainAxis;v<t?v=t:v>r&&(v=r)}if(u){var x,C;let e="y"===g?"width":"height",t=O.has(d(o)),r=i.reference[p]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[p])||0)+(t?0:w.crossAxis),n=i.reference[p]+i.reference[e]+(t?0:(null==(C=a.offset)?void 0:C[p])||0)-(t?w.crossAxis:0);y<r?y=r:y>n&&(y=n)}return{[g]:v,[p]:y}}}},er=(e,t,r)=>{let n=new Map,o={platform:H,...r},i={...o.platform,_c:n};return E(e,t,{...o,platform:i})}},6707:(e,t,r)=>{"use strict";r.d(t,{$:()=>A});var n=r(5155),o=r(5903);r(2115);var i=r(6204),a=r(8918),s=r(3664),l=r(862),u=r(311),c=r(4511),d=r(3347),f=r(2213),h=r(3608),p=r(6960),g={root:"m_77c9d27d",inner:"m_80f1301b",label:"m_811560b9",section:"m_a74036a",loader:"m_a25b86ee",group:"m_80d6d844",groupSection:"m_70be2a01"};let m={orientation:"horizontal"},v=(0,a.V)((e,t)=>{let{borderWidth:r}=t;return{group:{"--button-border-width":(0,o.D)(r)}}}),y=(0,p.P9)((e,t)=>{let r=(0,s.Y)("ButtonGroup",m,e),{className:o,style:i,classNames:a,styles:c,unstyled:d,orientation:f,vars:h,borderWidth:p,variant:y,mod:b,...w}=(0,s.Y)("ButtonGroup",m,e),x=(0,l.I)({name:"ButtonGroup",props:r,classes:g,className:o,style:i,classNames:a,styles:c,unstyled:d,vars:h,varsResolver:v,rootSelector:"group"});return(0,n.jsx)(u.a,{...x("group"),ref:t,variant:y,mod:[{"data-orientation":f},b],role:"group",...w})});y.classes=g,y.displayName="@mantine/core/ButtonGroup";let b={},w=(0,a.V)((e,t)=>{let{radius:r,color:n,gradient:o,variant:a,autoContrast:s,size:l}=t,u=e.variantColorResolver({color:n||e.primaryColor,theme:e,gradient:o,variant:a||"filled",autoContrast:s});return{groupSection:{"--section-height":(0,i.YC)(l,"section-height"),"--section-padding-x":(0,i.YC)(l,"section-padding-x"),"--section-fz":(null==l?void 0:l.includes("compact"))?(0,i.ny)(l.replace("compact-","")):(0,i.ny)(l),"--section-radius":void 0===r?void 0:(0,i.nJ)(r),"--section-bg":n||a?u.background:void 0,"--section-color":u.color,"--section-bd":n||a?u.border:void 0}}}),x=(0,p.P9)((e,t)=>{let r=(0,s.Y)("ButtonGroupSection",b,e),{className:o,style:i,classNames:a,styles:c,unstyled:d,vars:f,variant:h,gradient:p,radius:m,autoContrast:v,...y}=(0,s.Y)("ButtonGroupSection",b,e),x=(0,l.I)({name:"ButtonGroupSection",props:r,classes:g,className:o,style:i,classNames:a,styles:c,unstyled:d,vars:f,varsResolver:w,rootSelector:"groupSection"});return(0,n.jsx)(u.a,{...x("groupSection"),ref:t,variant:h,...y})});x.classes=g,x.displayName="@mantine/core/ButtonGroupSection";let C={in:{opacity:1,transform:"translate(-50%, calc(-50% + ".concat((0,o.D)(1),"))")},out:{opacity:0,transform:"translate(-50%, -200%)"},common:{transformOrigin:"center"},transitionProperty:"transform, opacity"},S={},_=(0,a.V)((e,t)=>{let{radius:r,color:n,gradient:o,variant:a,size:s,justify:l,autoContrast:u}=t,c=e.variantColorResolver({color:n||e.primaryColor,theme:e,gradient:o,variant:a||"filled",autoContrast:u});return{root:{"--button-justify":l,"--button-height":(0,i.YC)(s,"button-height"),"--button-padding-x":(0,i.YC)(s,"button-padding-x"),"--button-fz":(null==s?void 0:s.includes("compact"))?(0,i.ny)(s.replace("compact-","")):(0,i.ny)(s),"--button-radius":void 0===r?void 0:(0,i.nJ)(r),"--button-bg":n||a?c.background:void 0,"--button-hover":n||a?c.hover:void 0,"--button-color":c.color,"--button-bd":n||a?c.border:void 0,"--button-hover-color":n||a?c.hoverColor:void 0}}}),A=(0,c.v)((e,t)=>{let r=(0,s.Y)("Button",S,e),{style:o,vars:i,className:a,color:c,disabled:p,children:m,leftSection:v,rightSection:y,fullWidth:b,variant:w,radius:x,loading:A,loaderProps:E,gradient:j,classNames:k,styles:O,unstyled:D,"data-disabled":P,autoContrast:I,mod:R,...T}=r,L=(0,l.I)({name:"Button",props:r,classes:g,className:a,style:o,classNames:k,styles:O,unstyled:D,vars:i,varsResolver:_}),M=!!v,N=!!y;return(0,n.jsxs)(h.N,{ref:t,...L("root",{active:!p&&!A&&!P}),unstyled:D,variant:w,disabled:p||A,mod:[{disabled:p||P,loading:A,block:b,"with-left-section":M,"with-right-section":N},R],...T,children:[(0,n.jsx)(f.e,{mounted:!!A,transition:C,duration:150,children:e=>(0,n.jsx)(u.a,{component:"span",...L("loader",{style:e}),"aria-hidden":!0,children:(0,n.jsx)(d.a,{color:"var(--button-color)",size:"calc(var(--button-height) / 1.8)",...E})})}),(0,n.jsxs)("span",{...L("inner"),children:[v&&(0,n.jsx)(u.a,{component:"span",...L("section"),mod:{position:"left"},children:v}),(0,n.jsx)(u.a,{component:"span",mod:{loading:A},...L("label"),children:m}),y&&(0,n.jsx)(u.a,{component:"span",...L("section"),mod:{position:"right"},children:y})]})]})});A.classes=g,A.displayName="@mantine/core/Button",A.Group=y,A.GroupSection=x},6970:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(5155),o=r(2115);function i(e){let t=(0,o.createContext)(null);return[e=>{let{children:r,value:o}=e;return(0,n.jsx)(t.Provider,{value:o,children:r})},()=>{let r=(0,o.useContext)(t);if(null===r)throw Error(e);return r}]}},7613:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2115);function o(e){let{value:t,defaultValue:r,finalValue:o,onChange:i=()=>{}}=e,[a,s]=(0,n.useState)(void 0!==r?r:o);return void 0!==t?[t,i,!0]:[a,function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];s(e),null==i||i(e,...r)},!1]}},8141:(e,t,r)=>{"use strict";r.d(t,{o:()=>c});var n=r(5155);r(2115);var o=r(3664),i=r(862),a=r(311),s=r(4511),l={root:"m_4451eb3a"};let u={},c=(0,s.v)((e,t)=>{let r=(0,o.Y)("Center",u,e),{classNames:s,className:c,style:d,styles:f,unstyled:h,vars:p,inline:g,mod:m,...v}=r,y=(0,i.I)({name:"Center",props:r,classes:l,className:c,style:d,classNames:s,styles:f,unstyled:h,vars:p});return(0,n.jsx)(a.a,{ref:t,mod:[{inline:g},m],...y("root"),...v})});c.classes=l,c.displayName="@mantine/core/Center"},8551:(e,t,r)=>{"use strict";r.d(t,{bl:()=>o,pc:()=>i});var n=r(2115);function o(e,t){if("function"==typeof e)return e(t);"object"==typeof e&&null!==e&&"current"in e&&(e.current=t)}function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.useCallback)(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=new Map;return e=>{if(t.forEach(t=>{let r=o(t,e);r&&n.set(t,r)}),n.size>0)return()=>{t.forEach(e=>{let t=n.get(e);t?t():o(e,null)}),n.clear()}}}(...t),t)}},8593:(e,t,r)=>{"use strict";r.d(t,{k:()=>l});var n=r(5155);r(2115);var o=r(3664),i=r(6960),a=r(4225);let s={},l=(0,i.P9)((e,t)=>{let r=(0,o.Y)("TextInput",s,e);return(0,n.jsx)(a.O,{component:"input",ref:t,...r,__staticSelector:"TextInput"})});l.classes=a.O.classes,l.displayName="@mantine/core/TextInput"},8887:(e,t,r)=>{"use strict";r.d(t,{E:()=>g});var n=r(5155);r(2115);var o=r(6204),i=r(8918),a=r(1180),s=r(8512),l=r(3664),u=r(862),c=r(311),d=r(4511),f={root:"m_b6d8b162"};let h={inherit:!1},p=(0,i.V)((e,t)=>{let{variant:r,lineClamp:n,gradient:i,size:l,color:u}=t;return{root:{"--text-fz":(0,o.ny)(l),"--text-lh":(0,o.ks)(l),"--text-gradient":"gradient"===r?(0,s.v)(i,e):void 0,"--text-line-clamp":"number"==typeof n?n.toString():void 0,"--text-color":u?(0,a.r)(u,e):void 0}}}),g=(0,d.v)((e,t)=>{let r=(0,l.Y)("Text",h,e),{lineClamp:o,truncate:i,inline:a,inherit:s,gradient:d,span:g,__staticSelector:m,vars:v,className:y,style:b,classNames:w,styles:x,unstyled:C,variant:S,mod:_,size:A,...E}=r,j=(0,u.I)({name:["Text",m],props:r,classes:f,className:y,style:b,classNames:w,styles:x,unstyled:C,vars:v,varsResolver:p});return(0,n.jsx)(c.a,{...j("root",{focusable:!0}),ref:t,component:g?"span":"p",variant:S,mod:[{"data-truncate":function(e){return"start"===e?"start":"end"===e||e?"end":void 0}(i),"data-line-clamp":"number"==typeof o,"data-inline":a,"data-inherit":s},_],size:A,...E})});g.classes=f,g.displayName="@mantine/core/Text"},8918:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{V:()=>n})},8993:(e,t,r)=>{"use strict";r.d(t,{p:()=>H});var n=r(5155),o=r(5903),i=r(2115),a=r(6204),s=r(8918),l=r(3664),u=r(862),c=r(9537),d=r(311),f=r(4511),h=r(9830);let[p,g]=(0,h.e)({size:"sm"});var m=r(6028),v=r(6960),y=r(3608);let b=(0,i.forwardRef)((e,t)=>{let{size:r="var(--cb-icon-size, 70%)",style:o,...i}=e;return(0,n.jsx)("svg",{viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{...o,width:r,height:r},ref:t,...i,children:(0,n.jsx)("path",{d:"M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})});b.displayName="@mantine/core/CloseIcon";var w={root:"m_86a44da5","root--subtle":"m_220c80f2"};let x={variant:"subtle"},C=(0,s.V)((e,t)=>{let{size:r,radius:n,iconSize:i}=t;return{root:{"--cb-size":(0,a.YC)(r,"cb-size"),"--cb-radius":void 0===n?void 0:(0,a.nJ)(n),"--cb-icon-size":(0,o.D)(i)}}}),S=(0,f.v)((e,t)=>{let r=(0,l.Y)("CloseButton",x,e),{iconSize:o,children:i,vars:a,radius:s,className:c,classNames:d,style:f,styles:h,unstyled:p,"data-disabled":g,disabled:m,variant:v,icon:S,mod:_,__staticSelector:A,...E}=r,j=(0,u.I)({name:A||"CloseButton",props:r,className:c,style:f,classes:w,classNames:d,styles:h,unstyled:p,vars:a,varsResolver:C});return(0,n.jsxs)(y.N,{ref:t,...E,unstyled:p,variant:v,disabled:m,mod:[{disabled:m||g},_],...j("root",{variant:v,active:!m&&!g}),children:[S||(0,n.jsx)(b,{}),i]})});S.classes=w,S.displayName="@mantine/core/CloseButton";let _={},A=(0,v.P9)((e,t)=>{let r=(0,l.Y)("InputClearButton",_,e),{size:o,variant:i,vars:a,classNames:s,styles:u,...c}=r,d=g(),{resolvedClassNames:f,resolvedStyles:h}=(0,m.Y)({classNames:s,styles:u,props:r});return(0,n.jsx)(S,{variant:i||"transparent",ref:t,size:o||(null==d?void 0:d.size)||"sm",classNames:f,styles:h,__staticSelector:"InputClearButton",...c})});A.displayName="@mantine/core/InputClearButton";let[E,j]=(0,h.e)({offsetBottom:!1,offsetTop:!1,describedBy:void 0,getStyles:null,inputId:void 0,labelId:void 0});var k={wrapper:"m_6c018570",input:"m_8fb7ebe7",section:"m_82577fc2",placeholder:"m_88bacfd0",root:"m_46b77525",label:"m_8fdc1311",required:"m_78a94662",error:"m_8f816625",description:"m_fe47ce59"};let O={},D=(0,s.V)((e,t)=>{let{size:r}=t;return{description:{"--input-description-size":void 0===r?void 0:"calc(".concat((0,a.ny)(r)," - ").concat((0,o.D)(2),")")}}}),P=(0,v.P9)((e,t)=>{let r=(0,l.Y)("InputDescription",O,e),{classNames:o,className:i,style:a,styles:s,unstyled:c,vars:f,size:h,__staticSelector:p,__inheritStyles:g=!0,variant:m,...v}=(0,l.Y)("InputDescription",O,r),y=j(),b=(0,u.I)({name:["InputWrapper",p],props:r,classes:k,className:i,style:a,classNames:o,styles:s,unstyled:c,rootSelector:"description",vars:f,varsResolver:D}),w=g&&(null==y?void 0:y.getStyles)||b;return(0,n.jsx)(d.a,{component:"p",ref:t,variant:m,size:h,...w("description",(null==y?void 0:y.getStyles)?{className:i,style:a}:void 0),...v})});P.classes=k,P.displayName="@mantine/core/InputDescription";let I={},R=(0,s.V)((e,t)=>{let{size:r}=t;return{error:{"--input-error-size":void 0===r?void 0:"calc(".concat((0,a.ny)(r)," - ").concat((0,o.D)(2),")")}}}),T=(0,v.P9)((e,t)=>{let r=(0,l.Y)("InputError",I,e),{classNames:o,className:i,style:a,styles:s,unstyled:c,vars:f,size:h,__staticSelector:p,__inheritStyles:g=!0,variant:m,...v}=r,y=(0,u.I)({name:["InputWrapper",p],props:r,classes:k,className:i,style:a,classNames:o,styles:s,unstyled:c,rootSelector:"error",vars:f,varsResolver:R}),b=j(),w=g&&(null==b?void 0:b.getStyles)||y;return(0,n.jsx)(d.a,{component:"p",ref:t,variant:m,size:h,...w("error",(null==b?void 0:b.getStyles)?{className:i,style:a}:void 0),...v})});T.classes=k,T.displayName="@mantine/core/InputError";let L={labelElement:"label"},M=(0,s.V)((e,t)=>{let{size:r}=t;return{label:{"--input-label-size":(0,a.ny)(r),"--input-asterisk-color":void 0}}}),N=(0,v.P9)((e,t)=>{let r=(0,l.Y)("InputLabel",L,e),{classNames:o,className:i,style:a,styles:s,unstyled:c,vars:f,labelElement:h,size:p,required:g,htmlFor:m,onMouseDown:v,children:y,__staticSelector:b,variant:w,mod:x,...C}=(0,l.Y)("InputLabel",L,r),S=(0,u.I)({name:["InputWrapper",b],props:r,classes:k,className:i,style:a,classNames:o,styles:s,unstyled:c,rootSelector:"label",vars:f,varsResolver:M}),_=j(),A=(null==_?void 0:_.getStyles)||S;return(0,n.jsxs)(d.a,{...A("label",(null==_?void 0:_.getStyles)?{className:i,style:a}:void 0),component:h,variant:w,size:p,ref:t,htmlFor:"label"===h?m:void 0,mod:[{required:g},x],onMouseDown:e=>{null==v||v(e),!e.defaultPrevented&&e.detail>1&&e.preventDefault()},...C,children:[y,g&&(0,n.jsx)("span",{...A("required"),"aria-hidden":!0,children:" *"})]})});N.classes=k,N.displayName="@mantine/core/InputLabel";let z={},B=(0,v.P9)((e,t)=>{let r=(0,l.Y)("InputPlaceholder",z,e),{classNames:o,className:i,style:a,styles:s,unstyled:c,vars:f,__staticSelector:h,variant:p,error:g,mod:m,...v}=(0,l.Y)("InputPlaceholder",z,r),y=(0,u.I)({name:["InputPlaceholder",h],props:r,classes:k,className:i,style:a,classNames:o,styles:s,unstyled:c,rootSelector:"placeholder"});return(0,n.jsx)(d.a,{...y("placeholder"),mod:[{error:!!g},m],component:"span",variant:p,ref:t,...v})});B.classes=k,B.displayName="@mantine/core/InputPlaceholder";var Y=r(1513);let F={labelElement:"label",inputContainer:e=>e,inputWrapperOrder:["label","description","input","error"]},$=(0,s.V)((e,t)=>{let{size:r}=t;return{label:{"--input-label-size":(0,a.ny)(r),"--input-asterisk-color":void 0},error:{"--input-error-size":void 0===r?void 0:"calc(".concat((0,a.ny)(r)," - ").concat((0,o.D)(2),")")},description:{"--input-description-size":void 0===r?void 0:"calc(".concat((0,a.ny)(r)," - ").concat((0,o.D)(2),")")}}}),q=(0,v.P9)((e,t)=>{let r=(0,l.Y)("InputWrapper",F,e),{classNames:o,className:a,style:s,styles:c,unstyled:f,vars:h,size:p,variant:g,__staticSelector:m,inputContainer:v,inputWrapperOrder:y,label:b,error:w,description:x,labelProps:C,descriptionProps:S,errorProps:_,labelElement:A,children:j,withAsterisk:O,id:D,required:I,__stylesApiProps:R,mod:L,...M}=r,z=(0,u.I)({name:["InputWrapper",m],props:R||r,classes:k,className:a,style:s,classNames:o,styles:c,unstyled:f,vars:h,varsResolver:$}),B={size:p,variant:g,__staticSelector:m},q=(0,Y.B)(D),V=(null==_?void 0:_.id)||"".concat(q,"-error"),U=(null==S?void 0:S.id)||"".concat(q,"-description"),H=!!w&&"boolean"!=typeof w,G=!!x,W="".concat(H?V:""," ").concat(G?U:""),X=W.trim().length>0?W.trim():void 0,J=(null==C?void 0:C.id)||"".concat(q,"-label"),K=b&&(0,n.jsx)(N,{labelElement:A,id:J,htmlFor:q,required:"boolean"==typeof O?O:I,...B,...C,children:b},"label"),Z=G&&(0,n.jsx)(P,{...S,...B,size:(null==S?void 0:S.size)||B.size,id:(null==S?void 0:S.id)||U,children:x},"description"),Q=(0,n.jsx)(i.Fragment,{children:v(j)},"input"),ee=H&&(0,i.createElement)(T,{..._,...B,size:(null==_?void 0:_.size)||B.size,key:"error",id:(null==_?void 0:_.id)||V},w),et=y.map(e=>{switch(e){case"label":return K;case"input":return Q;case"description":return Z;case"error":return ee;default:return null}});return(0,n.jsx)(E,{value:{getStyles:z,describedBy:X,inputId:q,labelId:J,...function(e,t){let{hasDescription:r,hasError:n}=t,o=e.findIndex(e=>"input"===e),i=e.slice(0,o),a=e.slice(o+1),s=r&&i.includes("description")||n&&i.includes("error");return{offsetBottom:r&&a.includes("description")||n&&a.includes("error"),offsetTop:s}}(y,{hasDescription:G,hasError:H})},children:(0,n.jsx)(d.a,{ref:t,variant:g,size:p,mod:[{error:!!w},L],...z("root"),...M,children:et})})});q.classes=k,q.displayName="@mantine/core/InputWrapper";let V={variant:"default",leftSectionPointerEvents:"none",rightSectionPointerEvents:"none",withAria:!0,withErrorStyles:!0},U=(0,s.V)((e,t,r)=>({wrapper:{"--input-margin-top":r.offsetTop?"calc(var(--mantine-spacing-xs) / 2)":void 0,"--input-margin-bottom":r.offsetBottom?"calc(var(--mantine-spacing-xs) / 2)":void 0,"--input-height":(0,a.YC)(t.size,"input-height"),"--input-fz":(0,a.ny)(t.size),"--input-radius":void 0===t.radius?void 0:(0,a.nJ)(t.radius),"--input-left-section-width":void 0!==t.leftSectionWidth?(0,o.D)(t.leftSectionWidth):void 0,"--input-right-section-width":void 0!==t.rightSectionWidth?(0,o.D)(t.rightSectionWidth):void 0,"--input-padding-y":t.multiline?(0,a.YC)(t.size,"input-padding-y"):void 0,"--input-left-section-pointer-events":t.leftSectionPointerEvents,"--input-right-section-pointer-events":t.rightSectionPointerEvents}})),H=(0,f.v)((e,t)=>{let r=(0,l.Y)("Input",V,e),{classNames:o,className:i,style:a,styles:s,unstyled:f,required:h,__staticSelector:g,__stylesApiProps:m,size:v,wrapperProps:y,error:b,disabled:w,leftSection:x,leftSectionProps:C,leftSectionWidth:S,rightSection:_,rightSectionProps:A,rightSectionWidth:E,rightSectionPointerEvents:O,leftSectionPointerEvents:D,variant:P,vars:I,pointer:R,multiline:T,radius:L,id:M,withAria:N,withErrorStyles:z,mod:B,inputSize:Y,__clearSection:F,__clearable:$,__defaultRightSection:q,...H}=r,{styleProps:G,rest:W}=(0,c.j)(H),X=j(),J={offsetBottom:null==X?void 0:X.offsetBottom,offsetTop:null==X?void 0:X.offsetTop},K=(0,u.I)({name:["Input",g],props:m||r,classes:k,className:i,style:a,classNames:o,styles:s,unstyled:f,stylesCtx:J,rootSelector:"wrapper",vars:I,varsResolver:U}),Z=N?{required:h,disabled:w,"aria-invalid":!!b,"aria-describedby":null==X?void 0:X.describedBy,id:(null==X?void 0:X.inputId)||M}:{},Q=_||$&&F||q;return(0,n.jsx)(p,{value:{size:v||"sm"},children:(0,n.jsxs)(d.a,{...K("wrapper"),...G,...y,mod:[{error:!!b&&z,pointer:R,disabled:w,multiline:T,"data-with-right-section":!!_,"data-with-left-section":!!x},B],variant:P,size:v,children:[x&&(0,n.jsx)("div",{...C,"data-position":"left",...K("section",{className:null==C?void 0:C.className,style:null==C?void 0:C.style}),children:x}),(0,n.jsx)(d.a,{component:"input",...W,...Z,ref:t,required:h,mod:{disabled:w,error:!!b&&z},variant:P,__size:Y,...K("input")}),Q&&(0,n.jsx)("div",{...A,"data-position":"right",...K("section",{className:null==A?void 0:A.className,style:null==A?void 0:A.style}),children:Q})]})})});H.classes=k,H.Wrapper=q,H.Label=N,H.Error=T,H.Description=P,H.Placeholder=B,H.ClearButton=A,H.displayName="@mantine/core/Input"},9436:(e,t,r)=>{"use strict";r.d(t,{Z:()=>C});var n=r(5155),o=r(2115),i=r(6204),a=r(8918),s=r(3664),l=r(862),u=r(4511),c=r(311),d={root:"m_1b7284a3"};let f={},h=(0,a.V)((e,t)=>{let{radius:r,shadow:n}=t;return{root:{"--paper-radius":void 0===r?void 0:(0,i.nJ)(r),"--paper-shadow":(0,i.dh)(n)}}}),p=(0,u.v)((e,t)=>{let r=(0,s.Y)("Paper",f,e),{classNames:o,className:i,style:a,styles:u,unstyled:p,withBorder:g,vars:m,radius:v,shadow:y,variant:b,mod:w,...x}=r,C=(0,l.I)({name:"Paper",props:r,classes:d,className:i,style:a,classNames:o,styles:u,unstyled:p,vars:m,varsResolver:h});return(0,n.jsx)(c.a,{ref:t,mod:[{"data-with-border":g},w],...C("root"),variant:b,...x})});p.classes=d,p.displayName="@mantine/core/Paper";let[g,m]=(0,r(6970).F)("Card component was not found in tree");var v={root:"m_e615b15f",section:"m_599a2148"};let y={},b=(0,u.v)((e,t)=>{let{classNames:r,className:o,style:i,styles:a,vars:l,withBorder:u,inheritPadding:d,mod:f,...h}=(0,s.Y)("CardSection",y,e),p=m();return(0,n.jsx)(c.a,{ref:t,mod:[{"with-border":u,"inherit-padding":d},f],...p.getStyles("section",{className:o,style:i,styles:a,classNames:r}),...h})});b.classes=v,b.displayName="@mantine/core/CardSection";let w={},x=(0,a.V)((e,t)=>{let{padding:r}=t;return{root:{"--card-padding":(0,i.GY)(r)}}}),C=(0,u.v)((e,t)=>{let r=(0,s.Y)("Card",w,e),{classNames:i,className:a,style:u,styles:c,unstyled:d,vars:f,children:h,padding:m,...y}=r,C=(0,l.I)({name:"Card",props:r,classes:v,className:a,style:u,classNames:i,styles:c,unstyled:d,vars:f,varsResolver:x}),S=o.Children.toArray(h),_=S.map((e,t)=>"object"==typeof e&&e&&"type"in e&&e.type===b?(0,o.cloneElement)(e,{"data-first-section":0===t||void 0,"data-last-section":t===S.length-1||void 0}):e);return(0,n.jsx)(g,{value:{getStyles:C},children:(0,n.jsx)(p,{ref:t,unstyled:d,...C("root"),...y,children:_})})});C.classes=v,C.displayName="@mantine/core/Card",C.Section=b},9641:e=>{!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=l(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,i=l(e),a=i[0],s=i[1],u=new o((a+s)*3/4-s),c=0,d=s>0?a-4:a;for(r=0;r<d;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],u[c++]=t>>16&255,u[c++]=t>>8&255,u[c++]=255&t;return 2===s&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,u[c++]=255&t),1===s&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,u[c++]=t>>8&255,u[c++]=255&t),u},t.fromByteArray=function(e){for(var t,n=e.length,o=n%3,i=[],a=0,s=n-o;a<s;a+=16383)i.push(function(e,t,n){for(var o,i=[],a=t;a<n;a+=3)o=(e[a]<<16&0xff0000)+(e[a+1]<<8&65280)+(255&e[a+2]),i.push(r[o>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return i.join("")}(e,a,a+16383>s?s:a+16383));return 1===o?i.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===o&&i.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),i.join("")};for(var r=[],n=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,s=i.length;a<s;++a)r[a]=i[a],n[i.charCodeAt(a)]=a;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(e,t,r){"use strict";var n=r(675),o=r(783),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function a(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,s.prototype),t}function s(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return c(e)}return l(e,t,r)}function l(e,t,r){if("string"==typeof e){var n=e,o=t;if(("string"!=typeof o||""===o)&&(o="utf8"),!s.isEncoding(o))throw TypeError("Unknown encoding: "+o);var i=0|h(n,o),l=a(i),u=l.write(n,o);return u!==i&&(l=l.slice(0,u)),l}if(ArrayBuffer.isView(e))return d(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(O(e,ArrayBuffer)||e&&O(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(O(e,SharedArrayBuffer)||e&&O(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),s.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var c=e.valueOf&&e.valueOf();if(null!=c&&c!==e)return s.from(c,t,r);var p=function(e){if(s.isBuffer(e)){var t=0|f(e.length),r=a(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?a(0):d(e):"Buffer"===e.type&&Array.isArray(e.data)?d(e.data):void 0}(e);if(p)return p;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return s.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return u(e),a(e<0?0:0|f(e))}function d(e){for(var t=e.length<0?0:0|f(e.length),r=a(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}t.Buffer=s,t.SlowBuffer=function(e){return+e!=e&&(e=0),s.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,s.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),s.poolSize=8192,s.from=function(e,t,r){return l(e,t,r)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(e,t,r){return(u(e),e<=0)?a(e):void 0!==t?"string"==typeof r?a(e).fill(t,r):a(e).fill(t):a(e)},s.allocUnsafe=function(e){return c(e)},s.allocUnsafeSlow=function(e){return c(e)};function f(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function h(e,t){if(s.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||O(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var o=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return A(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return j(e).length;default:if(o)return n?-1:A(e).length;t=(""+t).toLowerCase(),o=!0}}function p(e,t,r){var o,i,a,s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var o="",i=t;i<r;++i)o+=D[e[i]];return o}(this,t,r);case"utf8":case"utf-8":return y(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var o=t;o<r;++o)n+=String.fromCharCode(127&e[o]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var o=t;o<r;++o)n+=String.fromCharCode(e[o]);return n}(this,t,r);case"base64":return o=this,i=t,a=r,0===i&&a===o.length?n.fromByteArray(o):n.fromByteArray(o.slice(i,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),o="",i=0;i<n.length;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function g(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function m(e,t,r,n,o){var i;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(i=r*=1)!=i&&(r=o?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(o)return -1;else r=e.length-1;else if(r<0)if(!o)return -1;else r=0;if("string"==typeof t&&(t=s.from(t,n)),s.isBuffer(t))return 0===t.length?-1:v(e,t,r,n,o);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(o)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return v(e,[t],r,n,o)}throw TypeError("val must be string, number or Buffer")}function v(e,t,r,n,o){var i,a=1,s=e.length,l=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;a=2,s/=2,l/=2,r/=2}function u(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(o){var c=-1;for(i=r;i<s;i++)if(u(e,i)===u(t,-1===c?0:i-c)){if(-1===c&&(c=i),i-c+1===l)return c*a}else -1!==c&&(i-=i-c),c=-1}else for(r+l>s&&(r=s-l),i=r;i>=0;i--){for(var d=!0,f=0;f<l;f++)if(u(e,i+f)!==u(t,f)){d=!1;break}if(d)return i}return -1}s.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==s.prototype},s.compare=function(e,t){if(O(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),O(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(e)||!s.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,o=0,i=Math.min(r,n);o<i;++o)if(e[o]!==t[o]){r=e[o],n=t[o];break}return r<n?-1:+(n<r)},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=s.allocUnsafe(t),o=0;for(r=0;r<e.length;++r){var i=e[r];if(O(i,Uint8Array)&&(i=s.from(i)),!s.isBuffer(i))throw TypeError('"list" argument must be an Array of Buffers');i.copy(n,o),o+=i.length}return n},s.byteLength=h,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},s.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?y(this,0,e):p.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(e){if(!s.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},i&&(s.prototype[i]=s.prototype.inspect),s.prototype.compare=function(e,t,r,n,o){if(O(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),t<0||r>e.length||n<0||o>this.length)throw RangeError("out of range index");if(n>=o&&t>=r)return 0;if(n>=o)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,o>>>=0,this===e)return 0;for(var i=o-n,a=r-t,l=Math.min(i,a),u=this.slice(n,o),c=e.slice(t,r),d=0;d<l;++d)if(u[d]!==c[d]){i=u[d],a=c[d];break}return i<a?-1:+(a<i)},s.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},s.prototype.indexOf=function(e,t,r){return m(this,e,t,r,!0)},s.prototype.lastIndexOf=function(e,t,r){return m(this,e,t,r,!1)};function y(e,t,r){r=Math.min(e.length,r);for(var n=[],o=t;o<r;){var i,a,s,l,u=e[o],c=null,d=u>239?4:u>223?3:u>191?2:1;if(o+d<=r)switch(d){case 1:u<128&&(c=u);break;case 2:(192&(i=e[o+1]))==128&&(l=(31&u)<<6|63&i)>127&&(c=l);break;case 3:i=e[o+1],a=e[o+2],(192&i)==128&&(192&a)==128&&(l=(15&u)<<12|(63&i)<<6|63&a)>2047&&(l<55296||l>57343)&&(c=l);break;case 4:i=e[o+1],a=e[o+2],s=e[o+3],(192&i)==128&&(192&a)==128&&(192&s)==128&&(l=(15&u)<<18|(63&i)<<12|(63&a)<<6|63&s)>65535&&l<1114112&&(c=l)}null===c?(c=65533,d=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),o+=d}var f=n,h=f.length;if(h<=4096)return String.fromCharCode.apply(String,f);for(var p="",g=0;g<h;)p+=String.fromCharCode.apply(String,f.slice(g,g+=4096));return p}function b(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function w(e,t,r,n,o,i){if(!s.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>o||t<i)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function x(e,t,r,n,o,i){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function C(e,t,r,n,i){return t*=1,r>>>=0,i||x(e,t,r,4,34028234663852886e22,-34028234663852886e22),o.write(e,t,r,n,23,4),r+4}function S(e,t,r,n,i){return t*=1,r>>>=0,i||x(e,t,r,8,17976931348623157e292,-17976931348623157e292),o.write(e,t,r,n,52,8),r+8}s.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var o,i,a,s,l,u,c,d,f=this.length-t;if((void 0===r||r>f)&&(r=f),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var h=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var o=e.length-r;n?(n=Number(n))>o&&(n=o):n=o;var i=t.length;n>i/2&&(n=i/2);for(var a=0;a<n;++a){var s,l=parseInt(t.substr(2*a,2),16);if((s=l)!=s)break;e[r+a]=l}return a}(this,e,t,r);case"utf8":case"utf-8":return o=t,i=r,k(A(e,this.length-o),this,o,i);case"ascii":return a=t,s=r,k(E(e),this,a,s);case"latin1":case"binary":return function(e,t,r,n){return k(E(t),e,r,n)}(this,e,t,r);case"base64":return l=t,u=r,k(j(e),this,l,u);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=t,d=r,k(function(e,t){for(var r,n,o=[],i=0;i<e.length&&!((t-=2)<0);++i)n=(r=e.charCodeAt(i))>>8,o.push(r%256),o.push(n);return o}(e,this.length-c),this,c,d);default:if(h)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),h=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},s.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,s.prototype),n},s.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],o=1,i=0;++i<t&&(o*=256);)n+=this[e+i]*o;return n},s.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e+--t],o=1;t>0&&(o*=256);)n+=this[e+--t]*o;return n},s.prototype.readUInt8=function(e,t){return e>>>=0,t||b(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},s.prototype.readUInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],o=1,i=0;++i<t&&(o*=256);)n+=this[e+i]*o;return n>=(o*=128)&&(n-=Math.pow(2,8*t)),n},s.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=t,o=1,i=this[e+--n];n>0&&(o*=256);)i+=this[e+--n]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*t)),i},s.prototype.readInt8=function(e,t){return(e>>>=0,t||b(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},s.prototype.readInt16LE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt16BE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return e>>>=0,t||b(e,4,this.length),o.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return e>>>=0,t||b(e,4,this.length),o.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return e>>>=0,t||b(e,8,this.length),o.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return e>>>=0,t||b(e,8,this.length),o.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var o=Math.pow(2,8*r)-1;w(this,e,t,r,o,0)}var i=1,a=0;for(this[t]=255&e;++a<r&&(i*=256);)this[t+a]=e/i&255;return t+r},s.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var o=Math.pow(2,8*r)-1;w(this,e,t,r,o,0)}var i=r-1,a=1;for(this[t+i]=255&e;--i>=0&&(a*=256);)this[t+i]=e/a&255;return t+r},s.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,1,255,0),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},s.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var o=Math.pow(2,8*r-1);w(this,e,t,r,o-1,-o)}var i=0,a=1,s=0;for(this[t]=255&e;++i<r&&(a*=256);)e<0&&0===s&&0!==this[t+i-1]&&(s=1),this[t+i]=(e/a|0)-s&255;return t+r},s.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var o=Math.pow(2,8*r-1);w(this,e,t,r,o-1,-o)}var i=r-1,a=1,s=0;for(this[t+i]=255&e;--i>=0&&(a*=256);)e<0&&0===s&&0!==this[t+i+1]&&(s=1),this[t+i]=(e/a|0)-s&255;return t+r},s.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},s.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeFloatLE=function(e,t,r){return C(this,e,t,!0,r)},s.prototype.writeFloatBE=function(e,t,r){return C(this,e,t,!1,r)},s.prototype.writeDoubleLE=function(e,t,r){return S(this,e,t,!0,r)},s.prototype.writeDoubleBE=function(e,t,r){return S(this,e,t,!1,r)},s.prototype.copy=function(e,t,r,n){if(!s.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var o=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var i=o-1;i>=0;--i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return o},s.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!s.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var o,i=e.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(e=i)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var a=s.isBuffer(e)?e:s.from(e,n),l=a.length;if(0===l)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(o=0;o<r-t;++o)this[o+t]=a[o%l]}return this};var _=/[^+/0-9A-Za-z-_]/g;function A(e,t){t=t||1/0;for(var r,n=e.length,o=null,i=[],a=0;a<n;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!o){if(r>56319||a+1===n){(t-=3)>-1&&i.push(239,191,189);continue}o=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),o=r;continue}r=(o-55296<<10|r-56320)+65536}else o&&(t-=3)>-1&&i.push(239,191,189);if(o=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return i}function E(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function j(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(_,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function k(e,t,r,n){for(var o=0;o<n&&!(o+r>=t.length)&&!(o>=e.length);++o)t[o+r]=e[o];return o}function O(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var D=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,o=0;o<16;++o)t[n+o]=e[r]+e[o];return t}()},783:function(e,t){t.read=function(e,t,r,n,o){var i,a,s=8*o-n-1,l=(1<<s)-1,u=l>>1,c=-7,d=r?o-1:0,f=r?-1:1,h=e[t+d];for(d+=f,i=h&(1<<-c)-1,h>>=-c,c+=s;c>0;i=256*i+e[t+d],d+=f,c-=8);for(a=i&(1<<-c)-1,i>>=-c,c+=n;c>0;a=256*a+e[t+d],d+=f,c-=8);if(0===i)i=1-u;else{if(i===l)return a?NaN:1/0*(h?-1:1);a+=Math.pow(2,n),i-=u}return(h?-1:1)*a*Math.pow(2,i-n)},t.write=function(e,t,r,n,o,i){var a,s,l,u=8*i-o-1,c=(1<<u)-1,d=c>>1,f=5960464477539062e-23*(23===o),h=n?0:i-1,p=n?1:-1,g=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(s=+!!isNaN(t),a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-a))<1&&(a--,l*=2),a+d>=1?t+=f/l:t+=f*Math.pow(2,1-d),t*l>=2&&(a++,l/=2),a+d>=c?(s=0,a=c):a+d>=1?(s=(t*l-1)*Math.pow(2,o),a+=d):(s=t*Math.pow(2,d-1)*Math.pow(2,o),a=0));o>=8;e[r+h]=255&s,h+=p,s/=256,o-=8);for(a=a<<o|s,u+=o;u>0;e[r+h]=255&a,h+=p,a/=256,u-=8);e[r+h-p]|=128*g}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var i=r[e]={exports:{}},a=!0;try{t[e](i,i.exports,n),a=!1}finally{a&&delete r[e]}return i.exports}n.ab="//",e.exports=n(72)}()},9791:(e,t,r)=>{"use strict";r.d(t,{x:()=>P});var n=r(5155);r(2115);var o=r(8918),i=r(3664),a=r(862),s=r(6390),l=r(311),u=r(6960);let[c,d]=(0,r(6970).F)("Grid component was not found in tree");var f=r(2596),h=r(9224),p=r(1526),g=r(4861),m=r(3288),v=r(3131),y=r(8570);let b=(e,t)=>"content"===e?"auto":"auto"===e?"0rem":e?"".concat(100/(t/e),"%"):void 0,w=(e,t,r)=>r||"auto"===e?"100%":"content"===e?"unset":b(e,t),x=(e,t)=>{if(e)return"auto"===e||t?"1":"auto"},C=(e,t)=>0===e?"0":e?"".concat(100/(t/e),"%"):void 0;function S(e){var t;let{span:r,order:o,offset:i,selector:a}=e,s=(0,v.xd)(),l=d(),u=l.breakpoints||s.breakpoints,c=void 0===(0,m.D)(r)?12:(0,m.D)(r),f=(0,p.J)({"--col-order":null==(t=(0,m.D)(o))?void 0:t.toString(),"--col-flex-grow":x(c,l.grow),"--col-flex-basis":b(c,l.columns),"--col-width":"content"===c?"auto":void 0,"--col-max-width":w(c,l.columns,l.grow),"--col-offset":C((0,m.D)(i),l.columns)}),S=(0,h.H)(u).reduce((e,t)=>{if(e[t]||(e[t]={}),"object"==typeof o&&void 0!==o[t]){var n;e[t]["--col-order"]=null==(n=o[t])?void 0:n.toString()}return"object"==typeof r&&void 0!==r[t]&&(e[t]["--col-flex-grow"]=x(r[t],l.grow),e[t]["--col-flex-basis"]=b(r[t],l.columns),e[t]["--col-width"]="content"===r[t]?"auto":void 0,e[t]["--col-max-width"]=w(r[t],l.columns,l.grow)),"object"==typeof i&&void 0!==i[t]&&(e[t]["--col-offset"]=C(i[t],l.columns)),e},{}),_=(0,g.q)((0,h.H)(S),u).filter(e=>(0,h.H)(S[e.value]).length>0).map(e=>({query:"container"===l.type?"mantine-grid (min-width: ".concat(u[e.value],")"):"(min-width: ".concat(u[e.value],")"),styles:S[e.value]}));return(0,n.jsx)(y.K,{styles:f,media:"container"===l.type?void 0:_,container:"container"===l.type?_:void 0,selector:a})}var _={container:"m_8478a6da",root:"m_410352e9",inner:"m_dee7bd2f",col:"m_96bdd299"};let A={span:12},E=(0,u.P9)((e,t)=>{let{classNames:r,className:o,style:a,styles:u,vars:c,span:h,order:p,offset:g,...m}=(0,i.Y)("GridCol",A,e),v=d(),y=(0,s.C)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(S,{selector:".".concat(y),span:h,order:p,offset:g}),(0,n.jsx)(l.a,{ref:t,...v.getStyles("col",{className:(0,f.A)(o,y),style:a,classNames:r,styles:u}),...m})]})});E.classes=_,E.displayName="@mantine/core/GridCol";var j=r(6204);function k(e){let{gutter:t,selector:r,breakpoints:o,type:i}=e,a=(0,v.xd)(),s=o||a.breakpoints,l=(0,p.J)({"--grid-gutter":(0,j.GY)((0,m.D)(t))}),u=(0,h.H)(s).reduce((e,r)=>(e[r]||(e[r]={}),"object"==typeof t&&void 0!==t[r]&&(e[r]["--grid-gutter"]=(0,j.GY)(t[r])),e),{}),c=(0,g.q)((0,h.H)(u),s).filter(e=>(0,h.H)(u[e.value]).length>0).map(e=>({query:"container"===i?"mantine-grid (min-width: ".concat(s[e.value],")"):"(min-width: ".concat(s[e.value],")"),styles:u[e.value]}));return(0,n.jsx)(y.K,{styles:l,media:"container"===i?void 0:c,container:"container"===i?c:void 0,selector:r})}let O={gutter:"md",grow:!1,columns:12},D=(0,o.V)((e,t)=>{let{justify:r,align:n,overflow:o}=t;return{root:{"--grid-justify":r,"--grid-align":n,"--grid-overflow":o}}}),P=(0,u.P9)((e,t)=>{let r=(0,i.Y)("Grid",O,e),{classNames:o,className:u,style:d,styles:f,unstyled:h,vars:p,grow:g,gutter:m,columns:v,align:y,justify:b,children:w,breakpoints:x,type:C,...S}=r,A=(0,a.I)({name:"Grid",classes:_,props:r,className:u,style:d,classNames:o,styles:f,unstyled:h,vars:p,varsResolver:D}),E=(0,s.C)();return"container"===C&&x?(0,n.jsxs)(c,{value:{getStyles:A,grow:g,columns:v||12,breakpoints:x,type:C},children:[(0,n.jsx)(k,{selector:".".concat(E),...r}),(0,n.jsx)("div",{...A("container"),children:(0,n.jsx)(l.a,{ref:t,...A("root",{className:E}),...S,children:(0,n.jsx)("div",{...A("inner"),children:w})})})]}):(0,n.jsxs)(c,{value:{getStyles:A,grow:g,columns:v||12,breakpoints:x,type:C},children:[(0,n.jsx)(k,{selector:".".concat(E),...r}),(0,n.jsx)(l.a,{ref:t,...A("root",{className:E}),...S,children:(0,n.jsx)("div",{...A("inner"),children:w})})]})});P.classes=_,P.displayName="@mantine/core/Grid",P.Col=E},9830:(e,t,r)=>{"use strict";r.d(t,{e:()=>i});var n=r(5155),o=r(2115);function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=(0,o.createContext)(e);return[e=>{let{children:r,value:o}=e;return(0,n.jsx)(t.Provider,{value:o,children:r})},()=>(0,o.useContext)(t)]}}}]);