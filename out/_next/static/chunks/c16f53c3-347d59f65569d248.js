"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[545],{5299:(e,n,t)=>{t.d(n,{we:()=>C});var r,l=t(2115),u=t(6301);t(7650);var f=t(4945);let o={...r||(r=t.t(l,2))},s=o.useInsertionEffect||(e=>e());var c="undefined"!=typeof document?l.useLayoutEffect:l.useEffect;let a=!1,i=0,d=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+i++,m=o.useId||function(){let[e,n]=l.useState(()=>a?d():void 0);return c(()=>{null==e&&n(d())},[]),l.useEffect(()=>{a=!0},[]),e},v=l.createContext(null),g=l.createContext(null);function C(e){void 0===e&&(e={});let{nodeId:n}=e,t=function(e){var n;let{open:t=!1,onOpenChange:r,elements:u}=e,f=m(),o=l.useRef({}),[c]=l.useState(()=>(function(){let e=new Map;return{emit(n,t){var r;null==(r=e.get(n))||r.forEach(e=>e(t))},on(n,t){e.set(n,[...e.get(n)||[],t])},off(n,t){var r;e.set(n,(null==(r=e.get(n))?void 0:r.filter(e=>e!==t))||[])}}})()),a=null!=((null==(n=l.useContext(v))?void 0:n.id)||null),[i,d]=l.useState(u.reference),g=function(e){let n=l.useRef(()=>{});return s(()=>{n.current=e}),l.useCallback(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==n.current?void 0:n.current(...t)},[])}((e,n,t)=>{o.current.openEvent=e?n:void 0,c.emit("openchange",{open:e,event:n,reason:t,nested:a}),null==r||r(e,n,t)}),C=l.useMemo(()=>({setPositionReference:d}),[]),R=l.useMemo(()=>({reference:i||u.reference||null,floating:u.floating||null,domReference:u.reference}),[i,u.reference,u.floating]);return l.useMemo(()=>({dataRef:o,open:t,onOpenChange:g,elements:R,events:c,floatingId:f,refs:C}),[t,g,R,c,f,C])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||t,o=r.elements,[a,i]=l.useState(null),[d,C]=l.useState(null),R=(null==o?void 0:o.domReference)||a,E=l.useRef(null),M=l.useContext(g);c(()=>{R&&(E.current=R)},[R]);let h=(0,f.we)({...e,elements:{...o,...d&&{reference:d}}}),p=l.useCallback(e=>{let n=(0,u.vq)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;C(n),h.refs.setReference(n)},[h.refs]),x=l.useCallback(e=>{((0,u.vq)(e)||null===e)&&(E.current=e,i(e)),((0,u.vq)(h.refs.reference.current)||null===h.refs.reference.current||null!==e&&!(0,u.vq)(e))&&h.refs.setReference(e)},[h.refs]),k=l.useMemo(()=>({...h.refs,setReference:x,setPositionReference:p,domReference:E}),[h.refs,x,p]),S=l.useMemo(()=>({...h.elements,domReference:R}),[h.elements,R]),b=l.useMemo(()=>({...h,...r,refs:k,elements:S,nodeId:n}),[h,k,S,n,r]);return c(()=>{r.dataRef.current.floatingContext=b;let e=null==M?void 0:M.nodesRef.current.find(e=>e.id===n);e&&(e.context=b)}),l.useMemo(()=>({...h,context:b,refs:k,elements:S}),[h,k,S,b])}}}]);