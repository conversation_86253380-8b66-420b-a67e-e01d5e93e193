1:"$Sreact.fragment"
2:I[1383,["156","static/chunks/156-3422de1ed5e7e998.js","874","static/chunks/874-437a265a67d6cfee.js","237","static/chunks/237-632daa672173e060.js","177","static/chunks/app/layout-3ff8c680681dce50.js"],"MantineProvider"]
3:I[8641,["156","static/chunks/156-3422de1ed5e7e998.js","874","static/chunks/874-437a265a67d6cfee.js","237","static/chunks/237-632daa672173e060.js","177","static/chunks/app/layout-3ff8c680681dce50.js"],"AppHeader"]
4:I[1590,["156","static/chunks/156-3422de1ed5e7e998.js","874","static/chunks/874-437a265a67d6cfee.js","237","static/chunks/237-632daa672173e060.js","177","static/chunks/app/layout-3ff8c680681dce50.js"],"Container"]
5:I[7555,[],""]
6:I[1295,[],""]
7:I[5980,["156","static/chunks/156-3422de1ed5e7e998.js","874","static/chunks/874-437a265a67d6cfee.js","220","static/chunks/app/about/page-7afdee1476728bd5.js"],"TypographyStylesProvider"]
11:I[8393,[],""]
:HL["/_next/static/css/eb8abf01ba248c84.css","style"]
:HL["/_next/static/css/dcf62dc2de18d7f8.css","style"]
0:{"P":null,"b":"b7UDc5bgYYlvKbqCMqsTx","p":"","c":["","about",""],"i":false,"f":[[["",{"children":["about",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/eb8abf01ba248c84.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/dcf62dc2de18d7f8.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","meta",null,{"name":"google-adsense-account","content":"ca-pub-****************"}],["$","script",null,{"async":true,"src":"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************","crossOrigin":"anonymous"}],["$","script",null,{"async":true,"src":"https://www.googletagmanager.com/gtag/js?id=G-RCMH6J3KQ9"}],["$","script",null,{"dangerouslySetInnerHTML":{"__html":"\n              window.dataLayer = window.dataLayer || [];\n              function gtag(){dataLayer.push(arguments);}\n              gtag('js', new Date());\n              gtag('config', 'G-RCMH6J3KQ9');\n            "}}]]}],["$","body",null,{"children":["$","$L2",null,{"theme":{"primaryColor":"cyan","radius":{"sm":"12px","md":"16px","lg":"20px","xl":"24px"}},"children":[["$","$L3",null,{}],["$","$L4",null,{"fluid":true,"maw":1200,"pt":"xl","pb":"xl","children":["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]}]]}]]}],{"children":["about",["$","$1","c",{"children":[null,["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"children":[["$","h1","h1-0",{"children":"What is a QR Code?"}],"\n",["$","p","p-0",{"children":"A QR code (short for Quick Response code) is a type of matrix barcode (or two-dimensional barcode)\nfirst designed in 1994 for the automotive industry in Japan. A barcode is a machine-readable\noptical label that contains information about the item to which it is attached."}],"\n",["$","h2","h2-0",{"children":"History of QR Codes"}],"\n",["$","p","p-1",{"children":"QR codes were invented by Masahiro Hara from the Japanese company Denso Wave in 1994.\nInitially, they were used to track vehicles during manufacturing and to manage parts inventory.\nTheir ability to store more information than traditional barcodes and be read quickly\nled to their adoption in various other industries."}],"\n",["$","h2","h2-1",{"children":"How Do QR Codes Work?"}],"\n",["$","p","p-2",{"children":"A QR code consists of black squares arranged in a square grid on a white background.\nThese patterns represent binary data that can be read by an imaging device, such as a camera,\nand then processed by a processor until the data is extracted. The three large squares at the\ncorners of the QR code are \"finder patterns\" that help the scanner identify the code's orientation\nand distinguish it from other elements in an image."}],"\n","$L8","\n","$L9","\n","$La","\n","$Lb","\n","$Lc","\n","$Ld","\n","$Le"]}],null,"$Lf"]}],{},null,false]},null,false]},null,false],"$L10",false]],"m":"$undefined","G":["$11",[]],"s":false,"S":true}
12:I[6874,["156","static/chunks/156-3422de1ed5e7e998.js","874","static/chunks/874-437a265a67d6cfee.js","220","static/chunks/app/about/page-7afdee1476728bd5.js"],""]
13:I[9665,[],"OutletBoundary"]
15:I[4911,[],"AsyncMetadataOutlet"]
17:I[9665,[],"ViewportBoundary"]
19:I[9665,[],"MetadataBoundary"]
1a:"$Sreact.suspense"
8:["$","h2","h2-2",{"children":"What Can QR Codes Store?"}]
9:["$","p","p-3",{"children":"QR codes have become widely popular due to their fast readability and greater storage capacity\ncompared to standard UPC barcodes. They can store various types of data, such as:"}]
a:["$","ul","ul-0",{"children":["\n",["$","li","li-0",{"children":[["$","strong","strong-0",{"children":"URLs (website links):"}]," The most common use, directing users to websites, videos, or online profiles."]}],"\n",["$","li","li-1",{"children":[["$","strong","strong-0",{"children":"Text messages:"}]," Simple plain text that can be displayed."]}],"\n",["$","li","li-2",{"children":[["$","strong","strong-0",{"children":"Contact information (vCards):"}]," Allows users to quickly add contact details to their address book."]}],"\n",["$","li","li-3",{"children":[["$","strong","strong-0",{"children":"Wi-Fi network credentials:"}]," Simplifies connecting to Wi-Fi networks by encoding SSID, password, and encryption type."]}],"\n",["$","li","li-4",{"children":[["$","strong","strong-0",{"children":"Geographic coordinates:"}]," Can open map applications to a specific location."]}],"\n",["$","li","li-5",{"children":[["$","strong","strong-0",{"children":"Payment information:"}]," Used in mobile payment systems for quick transactions."]}],"\n",["$","li","li-6",{"children":[["$","strong","strong-0",{"children":"Application downloads:"}]," Links directly to app store pages."]}],"\n",["$","li","li-7",{"children":[["$","strong","strong-0",{"children":"Event details:"}]," Add events directly to a calendar."]}],"\n"]}]
b:["$","h2","h2-3",{"children":"How to Use a QR Code"}]
c:["$","p","p-4",{"children":"To use a QR code, simply open your smartphone's camera or a dedicated QR code scanner app,\npoint it at the code, and it will automatically detect and interpret the information.\nMost modern smartphones have built-in QR code scanning capabilities within their camera apps."}]
d:["$","p","p-5",{"children":"QR Code Donkey provides a free and easy way to generate custom QR codes for your needs!"}]
e:["$","p","p-6",{"children":["$","$L12","a-0",{"href":"/","style":{"textDecoration":"none","textAlign":"center"},"children":"Back to QR Code Generator"}]}]
f:["$","$L13",null,{"children":["$L14",["$","$L15",null,{"promise":"$@16"}]]}]
10:["$","$1","h",{"children":[null,[["$","$L17",null,{"children":"$L18"}],null],["$","$L19",null,{"children":["$","div",null,{"hidden":true,"children":["$","$1a",null,{"fallback":null,"children":"$L1b"}]}]}]]}]
18:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
14:null
1c:I[8175,[],"IconMark"]
16:{"metadata":[["$","title","0",{"children":"Free QR Code Generator Online | QRCode Donkey"}],["$","meta","1",{"name":"description","content":"Generate custom QR codes for free with QRCode Donkey. Create high-quality QR codes for websites, social media, business cards, and more. No sign-up required!"}],["$","link","2",{"rel":"icon","href":"/donkey-128.png"}],["$","$L1c","3",{}]],"error":null,"digest":"$undefined"}
1b:"$16:metadata"
