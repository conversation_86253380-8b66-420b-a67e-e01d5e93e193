<!DOCTYPE html><!--b7UDc5bgYYlvKbqCMqsTx--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="/donkey-256.png"/><link rel="stylesheet" href="/_next/static/css/eb8abf01ba248c84.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/dcf62dc2de18d7f8.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-33db22c3a9a473b4.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-69097a61540f27b4.js" async=""></script><script src="/_next/static/chunks/main-app-2316ae708f0891fd.js" async=""></script><script src="/_next/static/chunks/156-3422de1ed5e7e998.js" async=""></script><script src="/_next/static/chunks/874-437a265a67d6cfee.js" async=""></script><script src="/_next/static/chunks/237-632daa672173e060.js" async=""></script><script src="/_next/static/chunks/app/layout-3ff8c680681dce50.js" async=""></script><script src="/_next/static/chunks/app/about/page-7afdee1476728bd5.js" async=""></script><script async="" src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************" crossorigin="anonymous"></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-RCMH6J3KQ9"></script><meta name="google-adsense-account" content="ca-pub-****************"/><title>Free QR Code Generator Online | QRCode Donkey</title><meta name="description" content="Generate custom QR codes for free with QRCode Donkey. Create high-quality QR codes for websites, social media, business cards, and more. No sign-up required!"/><link rel="icon" href="/donkey-128.png"/><script>
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-RCMH6J3KQ9');
            </script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body><div hidden=""><!--$--><!--/$--></div><style data-mantine-styles="true">:root{--mantine-radius-default: 12px;--mantine-primary-color-filled: var(--mantine-color-cyan-filled);--mantine-primary-color-filled-hover: var(--mantine-color-cyan-filled-hover);--mantine-primary-color-light: var(--mantine-color-cyan-light);--mantine-primary-color-light-hover: var(--mantine-color-cyan-light-hover);--mantine-primary-color-light-color: var(--mantine-color-cyan-light-color);--mantine-radius-sm: 12px;--mantine-radius-md: 16px;--mantine-radius-lg: 20px;--mantine-radius-xl: 24px;--mantine-primary-color-0: var(--mantine-color-cyan-0);--mantine-primary-color-1: var(--mantine-color-cyan-1);--mantine-primary-color-2: var(--mantine-color-cyan-2);--mantine-primary-color-3: var(--mantine-color-cyan-3);--mantine-primary-color-4: var(--mantine-color-cyan-4);--mantine-primary-color-5: var(--mantine-color-cyan-5);--mantine-primary-color-6: var(--mantine-color-cyan-6);--mantine-primary-color-7: var(--mantine-color-cyan-7);--mantine-primary-color-8: var(--mantine-color-cyan-8);--mantine-primary-color-9: var(--mantine-color-cyan-9);}:root[data-mantine-color-scheme="dark"]{--mantine-color-anchor: var(--mantine-color-cyan-4);}:root[data-mantine-color-scheme="light"]{--mantine-color-anchor: var(--mantine-color-cyan-6);}</style><style data-mantine-styles="classes">@media (max-width: 35.99375em) {.mantine-visible-from-xs {display: none !important;}}@media (min-width: 36em) {.mantine-hidden-from-xs {display: none !important;}}@media (max-width: 47.99375em) {.mantine-visible-from-sm {display: none !important;}}@media (min-width: 48em) {.mantine-hidden-from-sm {display: none !important;}}@media (max-width: 61.99375em) {.mantine-visible-from-md {display: none !important;}}@media (min-width: 62em) {.mantine-hidden-from-md {display: none !important;}}@media (max-width: 74.99375em) {.mantine-visible-from-lg {display: none !important;}}@media (min-width: 75em) {.mantine-hidden-from-lg {display: none !important;}}@media (max-width: 87.99375em) {.mantine-visible-from-xl {display: none !important;}}@media (min-width: 88em) {.mantine-hidden-from-xl {display: none !important;}}</style><style data-mantine-styles="inline">.__m__-_R_1tb_{gap:var(--mantine-spacing-sm);flex-direction:column;}@media(min-width: 36em){.__m__-_R_1tb_{gap:var(--mantine-spacing-lg);flex-direction:row;}}</style><div style="border-bottom:1px solid var(--mantine-color-gray-1);white-space:nowrap;align-items:center;flex-wrap:wrap;padding:var(--mantine-spacing-xs)" class="m_8bffd616 mantine-Flex-root __m__-_R_1tb_"><div style="gap:var(--mantine-spacing-sm);flex-direction:column" class="m_8bffd616 mantine-Flex-root __m__-_R_dltb_"><div style="gap:var(--mantine-spacing-xs);align-items:center;flex-direction:row" class="m_8bffd616 mantine-Flex-root __m__-_R_1ddltb_"><div class="m_4451eb3a mantine-Center-root"><img style="width:calc(3.25rem * var(--mantine-scale))" class="m_9e117634 mantine-Image-root" src="/donkey-256.png" alt="Cool donkey logo | QRCode Donkey"/></div><h2 style="--title-fw:var(--mantine-h2-font-weight);--title-lh:var(--mantine-h2-line-height);--title-fz:var(--mantine-h2-font-size)" class="m_8a5d1357 mantine-Title-root" data-order="2">QRCode Donkey</h2></div></div><p style="--text-fz:var(--mantine-font-size-sm);--text-lh:var(--mantine-line-height-sm);color:var(--mantine-color-dimmed)" class="mantine-focus-auto m_b6d8b162 mantine-Text-root" data-size="sm">Free QR Code generator</p><div style="gap:var(--mantine-spacing-lg)" class="m_8bffd616 mantine-Flex-root __m__-_R_tltb_"><a style="text-decoration:none" href="/"><span style="--text-fz:var(--mantine-font-size-sm);--text-lh:var(--mantine-line-height-sm)" class="mantine-focus-auto m_849cf0da m_b6d8b162 mantine-Text-root mantine-Anchor-root" data-size="sm" data-underline="hover">QR Code Generator</span></a><a style="text-decoration:none" href="/about/"><span style="--text-fz:var(--mantine-font-size-sm);--text-lh:var(--mantine-line-height-sm)" class="mantine-focus-auto m_849cf0da m_b6d8b162 mantine-Text-root mantine-Anchor-root" data-size="sm" data-underline="hover">What is a QR Code?</span></a></div></div><div style="padding-top:var(--mantine-spacing-xl);padding-bottom:var(--mantine-spacing-xl);max-width:calc(75rem * var(--mantine-scale))" class="m_7485cace mantine-Container-root" data-fluid="true"><div class="m_d6493fad mantine-TypographyStylesProvider-root"><h1>What is a QR Code?</h1>
<p>A QR code (short for Quick Response code) is a type of matrix barcode (or two-dimensional barcode)
first designed in 1994 for the automotive industry in Japan. A barcode is a machine-readable
optical label that contains information about the item to which it is attached.</p>
<h2>History of QR Codes</h2>
<p>QR codes were invented by Masahiro Hara from the Japanese company Denso Wave in 1994.
Initially, they were used to track vehicles during manufacturing and to manage parts inventory.
Their ability to store more information than traditional barcodes and be read quickly
led to their adoption in various other industries.</p>
<h2>How Do QR Codes Work?</h2>
<p>A QR code consists of black squares arranged in a square grid on a white background.
These patterns represent binary data that can be read by an imaging device, such as a camera,
and then processed by a processor until the data is extracted. The three large squares at the
corners of the QR code are &quot;finder patterns&quot; that help the scanner identify the code&#x27;s orientation
and distinguish it from other elements in an image.</p>
<h2>What Can QR Codes Store?</h2>
<p>QR codes have become widely popular due to their fast readability and greater storage capacity
compared to standard UPC barcodes. They can store various types of data, such as:</p>
<ul>
<li><strong>URLs (website links):</strong> The most common use, directing users to websites, videos, or online profiles.</li>
<li><strong>Text messages:</strong> Simple plain text that can be displayed.</li>
<li><strong>Contact information (vCards):</strong> Allows users to quickly add contact details to their address book.</li>
<li><strong>Wi-Fi network credentials:</strong> Simplifies connecting to Wi-Fi networks by encoding SSID, password, and encryption type.</li>
<li><strong>Geographic coordinates:</strong> Can open map applications to a specific location.</li>
<li><strong>Payment information:</strong> Used in mobile payment systems for quick transactions.</li>
<li><strong>Application downloads:</strong> Links directly to app store pages.</li>
<li><strong>Event details:</strong> Add events directly to a calendar.</li>
</ul>
<h2>How to Use a QR Code</h2>
<p>To use a QR code, simply open your smartphone&#x27;s camera or a dedicated QR code scanner app,
point it at the code, and it will automatically detect and interpret the information.
Most modern smartphones have built-in QR code scanning capabilities within their camera apps.</p>
<p>QR Code Donkey provides a free and easy way to generate custom QR codes for your needs!</p>
<p><a style="text-decoration:none;text-align:center" href="/">Back to QR Code Generator</a></p></div><!--$--><!--/$--></div><script src="/_next/static/chunks/webpack-33db22c3a9a473b4.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[1383,[\"156\",\"static/chunks/156-3422de1ed5e7e998.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"237\",\"static/chunks/237-632daa672173e060.js\",\"177\",\"static/chunks/app/layout-3ff8c680681dce50.js\"],\"MantineProvider\"]\n3:I[8641,[\"156\",\"static/chunks/156-3422de1ed5e7e998.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"237\",\"static/chunks/237-632daa672173e060.js\",\"177\",\"static/chunks/app/layout-3ff8c680681dce50.js\"],\"AppHeader\"]\n4:I[1590,[\"156\",\"static/chunks/156-3422de1ed5e7e998.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"237\",\"static/chunks/237-632daa672173e060.js\",\"177\",\"static/chunks/app/layout-3ff8c680681dce50.js\"],\"Container\"]\n5:I[7555,[],\"\"]\n6:I[1295,[],\"\"]\n7:I[5980,[\"156\",\"static/chunks/156-3422de1ed5e7e998.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"220\",\"static/chunks/app/about/page-7afdee1476728bd5.js\"],\"TypographyStylesProvider\"]\n11:I[8393,[],\"\"]\n:HL[\"/_next/static/css/eb8abf01ba248c84.css\",\"style\"]\n:HL[\"/_next/static/css/dcf62dc2de18d7f8.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"b7UDc5bgYYlvKbqCMqsTx\",\"p\":\"\",\"c\":[\"\",\"about\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"about\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/eb8abf01ba248c84.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/dcf62dc2de18d7f8.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"name\":\"google-adsense-account\",\"content\":\"ca-pub-****************\"}],[\"$\",\"script\",null,{\"async\":true,\"src\":\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************\",\"crossOrigin\":\"anonymous\"}],[\"$\",\"script\",null,{\"async\":true,\"src\":\"https://www.googletagmanager.com/gtag/js?id=G-RCMH6J3KQ9\"}],[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n              window.dataLayer = window.dataLayer || [];\\n              function gtag(){dataLayer.push(arguments);}\\n              gtag('js', new Date());\\n              gtag('config', 'G-RCMH6J3KQ9');\\n            \"}}]]}],[\"$\",\"body\",null,{\"children\":[\"$\",\"$L2\",null,{\"theme\":{\"primaryColor\":\"cyan\",\"radius\":{\"sm\":\"12px\",\"md\":\"16px\",\"lg\":\"20px\",\"xl\":\"24px\"}},\"children\":[[\"$\",\"$L3\",null,{}],[\"$\",\"$L4\",null,{\"fluid\":true,\"maw\":1200,\"pt\":\"xl\",\"pb\":\"xl\",\"children\":[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]}]]}]]}],{\"children\":[\"about\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L7\",null,{\"children\":[[\"$\",\"h1\",\"h1-0\",{\"children\":\"What is a QR Code?\"}],\"\\n\",[\"$\",\"p\",\"p-0\",{\"children\":\"A QR code (short for Quick Response code) is a type of matrix barcode (or two-dimensional barcode)\\nfirst designed in 1994 for the automotive industry in Japan. A barcode is a machine-readable\\noptical label that contains information about the item to which it is attached.\"}],\"\\n\",[\"$\",\"h2\",\"h2-0\",{\"children\":\"History of QR Codes\"}],\"\\n\",[\"$\",\"p\",\"p-1\",{\"children\":\"QR codes were invented by Masahiro Hara from the Japanese company Denso Wave in 1994.\\nInitially, they were used to track vehicles during manufacturing and to manage parts inventory.\\nTheir ability to store more information than traditional barcodes and be read quickly\\nled to their adoption in various other industries.\"}],\"\\n\",[\"$\",\"h2\",\"h2-1\",{\"children\":\"How Do QR Codes Work?\"}],\"\\n\",[\"$\",\"p\",\"p-2\",{\"children\":\"A QR code consists of black squares arranged in a square grid on a white background.\\nThese patterns represent binary data that can be read by an imaging device, such as a camera,\\nand then processed by a processor until the data is extracted. The three large squares at the\\ncorners of the QR code are \\\"finder patterns\\\" that help the scanner identify the code's orientation\\nand distinguish it from other elements in an image.\"}],\"\\n\",\"$L8\",\"\\n\",\"$L9\",\"\\n\",\"$La\",\"\\n\",\"$Lb\",\"\\n\",\"$Lc\",\"\\n\",\"$Ld\",\"\\n\",\"$Le\"]}],null,\"$Lf\"]}],{},null,false]},null,false]},null,false],\"$L10\",false]],\"m\":\"$undefined\",\"G\":[\"$11\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:I[6874,[\"156\",\"static/chunks/156-3422de1ed5e7e998.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"220\",\"static/chunks/app/about/page-7afdee1476728bd5.js\"],\"\"]\n13:I[9665,[],\"OutletBoundary\"]\n15:I[4911,[],\"AsyncMetadataOutlet\"]\n17:I[9665,[],\"ViewportBoundary\"]\n19:I[9665,[],\"MetadataBoundary\"]\n1a:\"$Sreact.suspense\"\n8:[\"$\",\"h2\",\"h2-2\",{\"children\":\"What Can QR Codes Store?\"}]\n9:[\"$\",\"p\",\"p-3\",{\"children\":\"QR codes have become widely popular due to their fast readability and greater storage capacity\\ncompared to standard UPC barcodes. They can store various types of data, such as:\"}]\n"])</script><script>self.__next_f.push([1,"a:[\"$\",\"ul\",\"ul-0\",{\"children\":[\"\\n\",[\"$\",\"li\",\"li-0\",{\"children\":[[\"$\",\"strong\",\"strong-0\",{\"children\":\"URLs (website links):\"}],\" The most common use, directing users to websites, videos, or online profiles.\"]}],\"\\n\",[\"$\",\"li\",\"li-1\",{\"children\":[[\"$\",\"strong\",\"strong-0\",{\"children\":\"Text messages:\"}],\" Simple plain text that can be displayed.\"]}],\"\\n\",[\"$\",\"li\",\"li-2\",{\"children\":[[\"$\",\"strong\",\"strong-0\",{\"children\":\"Contact information (vCards):\"}],\" Allows users to quickly add contact details to their address book.\"]}],\"\\n\",[\"$\",\"li\",\"li-3\",{\"children\":[[\"$\",\"strong\",\"strong-0\",{\"children\":\"Wi-Fi network credentials:\"}],\" Simplifies connecting to Wi-Fi networks by encoding SSID, password, and encryption type.\"]}],\"\\n\",[\"$\",\"li\",\"li-4\",{\"children\":[[\"$\",\"strong\",\"strong-0\",{\"children\":\"Geographic coordinates:\"}],\" Can open map applications to a specific location.\"]}],\"\\n\",[\"$\",\"li\",\"li-5\",{\"children\":[[\"$\",\"strong\",\"strong-0\",{\"children\":\"Payment information:\"}],\" Used in mobile payment systems for quick transactions.\"]}],\"\\n\",[\"$\",\"li\",\"li-6\",{\"children\":[[\"$\",\"strong\",\"strong-0\",{\"children\":\"Application downloads:\"}],\" Links directly to app store pages.\"]}],\"\\n\",[\"$\",\"li\",\"li-7\",{\"children\":[[\"$\",\"strong\",\"strong-0\",{\"children\":\"Event details:\"}],\" Add events directly to a calendar.\"]}],\"\\n\"]}]\n"])</script><script>self.__next_f.push([1,"b:[\"$\",\"h2\",\"h2-3\",{\"children\":\"How to Use a QR Code\"}]\nc:[\"$\",\"p\",\"p-4\",{\"children\":\"To use a QR code, simply open your smartphone's camera or a dedicated QR code scanner app,\\npoint it at the code, and it will automatically detect and interpret the information.\\nMost modern smartphones have built-in QR code scanning capabilities within their camera apps.\"}]\nd:[\"$\",\"p\",\"p-5\",{\"children\":\"QR Code Donkey provides a free and easy way to generate custom QR codes for your needs!\"}]\ne:[\"$\",\"p\",\"p-6\",{\"children\":[\"$\",\"$L12\",\"a-0\",{\"href\":\"/\",\"style\":{\"textDecoration\":\"none\",\"textAlign\":\"center\"},\"children\":\"Back to QR Code Generator\"}]}]\nf:[\"$\",\"$L13\",null,{\"children\":[\"$L14\",[\"$\",\"$L15\",null,{\"promise\":\"$@16\"}]]}]\n10:[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L17\",null,{\"children\":\"$L18\"}],null],[\"$\",\"$L19\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$1a\",null,{\"fallback\":null,\"children\":\"$L1b\"}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"18:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n14:null\n"])</script><script>self.__next_f.push([1,"1c:I[8175,[],\"IconMark\"]\n16:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Free QR Code Generator Online | QRCode Donkey\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Generate custom QR codes for free with QRCode Donkey. Create high-quality QR codes for websites, social media, business cards, and more. No sign-up required!\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/donkey-128.png\"}],[\"$\",\"$L1c\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"1b:\"$16:metadata\"\n"])</script></body></html>