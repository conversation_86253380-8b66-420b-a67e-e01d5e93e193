import { Center, Container, Loader, MantineProvider, createTheme } from '@mantine/core';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AppHeader } from './AppHeader';
import { Suspense, lazy } from 'react';
const AboutQRCodePage = lazy(() => import('./AboutQRCodePage'));
const QRCodeGeneratorPage = lazy(() => import('./QRCodeGeneratorPage')); // Import the new QR Code Generator page

import '@mantine/core/styles.css';

const theme = createTheme({
  primaryColor: 'cyan',
  radius: {
    sm: '12px',
    md: '16px',
    lg: '20px',
    xl: '24px',
  },
});

function App() {
  return (
    <MantineProvider theme={theme}>
      <Router>
        <AppHeader />
        <Container fluid maw={1200} pt="xl" pb="xl">
          <Suspense
            fallback={
              <Center p="xl">
                <Loader />
              </Center>
            }
          >
            <Routes>
              <Route path="/" Component={QRCodeGeneratorPage} />{' '}
              <Route path="/about" Component={AboutQRCodePage} />
            </Routes>
          </Suspense>
        </Container>
      </Router>
    </MantineProvider>
  );
}

export default App;
