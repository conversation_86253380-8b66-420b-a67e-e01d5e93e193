'use client';

import { Button, Stack, TextInput, SimpleGrid, ColorInput, Select } from '@mantine/core';
import { useForm } from '@mantine/form';
import { DotType } from 'qr-code-styling';

interface QRCodeFormProps {
  initialUrl: string;
  initialDotType: DotType;
  initialDotColor: string;
  initialBackgroundColor: string;
  onGenerate: (values: { url: string; dotType: DotType; dotColor: string; backgroundColor: string }) => void;
  onDotTypeChange: (value: DotType) => void;
  onDotColorChange: (value: string) => void;
  onBackgroundColorChange: (value: string) => void;
}

export function QRCodeForm({
  initialUrl,
  initialDotType,
  initialDotColor,
  initialBackgroundColor,
  onGenerate,
  onDotTypeChange,
  onDotColorChange,
  onBackgroundColorChange,
}: QRCodeFormProps) {
  const form = useForm({
    mode: 'uncontrolled',
    initialValues: {
      url: initialUrl,
    },
    validate: {
      url: (value) =>
        /[(http(s)?)://(www.)?a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/.test(
          value
        )
          ? null
          : 'Invalid URL',
    },
  });

  return (
    <form
      onSubmit={form.onSubmit((values) =>
        onGenerate({
          url: values.url,
          dotType: initialDotType,
          dotColor: initialDotColor,
          backgroundColor: initialBackgroundColor,
        })
      )}
    >
      <Stack>
        <TextInput
          size="xl"
          label="Your URL"
          placeholder="https://www.sample.com"
          autoComplete="qrcode-url"
          key={form.key('url')}
          {...form.getInputProps('url')}
          styles={{ input: { fontWeight: 'bold' } }}
        />
        <SimpleGrid cols={3}>
          <Select
            size="md"
            label="Dot style"
            value={initialDotType}
            onChange={(_value, option) => onDotTypeChange(option.value as DotType)}
            data={[
              { value: 'square', label: 'Square' },
              { value: 'dots', label: 'Dots' },
              { value: 'rounded', label: 'Rounded' },
              { value: 'extra-rounded', label: 'Extra rounded' },
              { value: 'classy', label: 'Classy' },
              { value: 'classy-rounded', label: 'Classy rounded' },
            ]}
          />
          <ColorInput size="md" label="Dot color" value={initialDotColor} onChange={onDotColorChange} />
          <ColorInput
            size="md"
            label="Background color"
            value={initialBackgroundColor}
            onChange={onBackgroundColorChange}
          />
        </SimpleGrid>
        <Button size="lg" type="submit">
          Generate
        </Button>
      </Stack>
    </form>
  );
}