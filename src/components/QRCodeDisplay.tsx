'use client';

import { useRef, useEffect } from 'react';
import { Center } from '@mantine/core';
import QRCodeStyling from 'qr-code-styling';
import { DotType } from 'qr-code-styling';

interface QRCodeDisplayProps {
  data: string;
  dotType: DotType;
  dotColor: string;
  backgroundColor: string;
  size?: number;
  onSvgGenerated?: (svgString: string) => void;
}

export function QRCodeDisplay({
  data,
  dotType,
  dotColor,
  backgroundColor,
  size = 300,
  onSvgGenerated,
}: QRCodeDisplayProps) {
  const ref = useRef<HTMLDivElement>(null);
  const qrCode = useRef(
    new QRCodeStyling({
      width: size,
      height: size,
      type: 'svg',
      data: data,
      image: '',
      dotsOptions: {
        color: dotColor,
        type: dotType,
      },
      backgroundOptions: {
        color: backgroundColor,
      },
      imageOptions: {
        crossOrigin: 'anonymous',
        margin: 20,
      },
    })
  );

  useEffect(() => {
    if (ref.current) {
      qrCode.current.append(ref.current);
    }
  }, []);

  useEffect(() => {
    qrCode.current.update({
      data: data,
      dotsOptions: {
        type: dotType,
        color: dotColor,
      },
      backgroundOptions: {
        color: backgroundColor,
      },
    });

    // Generate SVG string and pass it to the callback
    if (onSvgGenerated && ref.current) {
      const svgElement = ref.current.querySelector('svg');
      if (svgElement) {
        const svgString = new XMLSerializer().serializeToString(svgElement);
        onSvgGenerated(svgString);
      }
    }
  }, [data, dotType, dotColor, backgroundColor, onSvgGenerated]);

  return (
    <Center>
      <div ref={ref}></div>
    </Center>
  );
}
