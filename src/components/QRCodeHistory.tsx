'use client';

import { Card, Group, Text, ActionIcon, Center, Button, SimpleGrid, Title } from '@mantine/core';
import { QRCodeDisplay } from './QRCodeDisplay';
import { IconTrash } from '@tabler/icons-react';
import { DotType } from 'qr-code-styling';

interface HistoryEntry {
  url: string;
  dotType: DotType;
  dotColor: string;
  backgroundColor: string;
  timestamp: number;
}

interface QRCodeHistoryProps {
  history: HistoryEntry[];
  onLoadHistoryEntry: (entry: HistoryEntry) => void;
  onDeleteHistoryEntry: (index: number) => void;
}

export function QRCodeHistory({
  history,
  onLoadHistoryEntry,
  onDeleteHistoryEntry,
}: QRCodeHistoryProps) {
  return (
    <>
      <Title order={2}>History</Title>
      {history.length === 0 ? (
        <Text>No history yet. Generate a QR code to see it here!</Text>
      ) : (
        <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }}>
          {history.map((entry, index) => (
            <Card key={index} shadow="sm" padding="lg" radius="md" withBorder pos="relative">
              <Group justify="space-between" mb="xs">
                <Text fw={500} truncate>
                  {entry.url}
                </Text>
              </Group>
              <ActionIcon
                variant="transparent"
                color="red"
                onClick={() => onDeleteHistoryEntry(index)}
                aria-label="Delete QR code"
                pos="absolute"
                top={10}
                right={10}
              >
                <IconTrash style={{ width: '70%', height: '70%' }} stroke={1.5} />
              </ActionIcon>

              <Center>
                <QRCodeDisplay
                  data={entry.url}
                  dotType={entry.dotType}
                  dotColor={entry.dotColor}
                  backgroundColor={entry.backgroundColor}
                  size={150}
                />
              </Center>

              <Button
                variant="light"
                fullWidth
                mt="md"
                radius="md"
                onClick={() => onLoadHistoryEntry(entry)}
              >
                Load
              </Button>
            </Card>
          ))}
        </SimpleGrid>
      )}
    </>
  );
}
