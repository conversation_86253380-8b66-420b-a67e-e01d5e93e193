'use client';

import { Flex, Image, Title, Text, Center, Anchor } from '@mantine/core';
import Link from 'next/link';

// Constants for paths and image sources
const DONKEY_LOGO_PATH = '/donkey-256.png';

// Helper component for navigation links
interface HeaderLinkProps {
  to: string;
  label: string;
}

function HeaderLink({ to, label }: HeaderLinkProps) {
  return (
    <Link href={to} style={{ textDecoration: 'none' }}>
      <Anchor component="span" size="sm">
        {label}
      </Anchor>
    </Link>
  );
}

export function AppHeader() {
  return (
    <Flex
      direction={{ base: 'column', xs: 'row' }}
      align="center"
      gap={{ base: 'sm', xs: 'lg' }}
      p="xs"
      wrap={'wrap'}
      style={{
        borderBottom: '1px solid var(--mantine-color-gray-1)',
        whiteSpace: 'nowrap',
      }}
    >
      <Flex direction="column" gap="sm">
        <Flex direction="row" align="center" gap="xs">
          <Center>
            <Image w={52} src={DONKEY_LOGO_PATH} alt="Cool donkey logo | QRCode Donkey" />
          </Center>
          <Title order={2}>QRCode Donkey</Title>
        </Flex>
      </Flex>

      <Text size="sm" c="dimmed">
        Free QR Code generator
      </Text>
      <Flex gap="lg">
        <HeaderLink to="/" label="QR Code Generator" />
        <HeaderLink to="/about" label="What is a QR Code?" />
      </Flex>
    </Flex>
  );
}
