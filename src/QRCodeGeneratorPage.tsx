'use client';

import { useEffect, useRef, useState } from 'react';
import { Stack, Flex, Grid } from '@mantine/core';
import QRCodeStyling, { DotType } from 'qr-code-styling';
import { QRCodeForm } from './components/QRCodeForm';
import { QRCodeDisplay } from './components/QRCodeDisplay';
import { QRCodeHistory } from './components/QRCodeHistory';

interface HistoryEntry {
  url: string;
  dotType: DotType;
  dotColor: string;
  backgroundColor: string;
  timestamp: number;
}

const LOCAL_STORAGE_KEY = 'qr_code_history';
const MAX_HISTORY_ENTRIES = 100;

const DEFAULT_URL = 'https://www.qrcode-donkey.com';
const DEFAULT_DOT_TYPE: DotType = 'square';
const DEFAULT_DOT_COLOR = '#000000';
const DEFAULT_BACKGROUND_COLOR = '#ffffff';

export default function QRCodeGeneratorPage() {
  const [url, setUrl] = useState(DEFAULT_URL);
  const [dotType, setDotType] = useState<DotType>(DEFAULT_DOT_TYPE);
  const [dotColor, setDotColor] = useState(DEFAULT_DOT_COLOR);
  const [backgroundColor, setBackgroundColor] = useState(DEFAULT_BACKGROUND_COLOR);
  const [history, setHistory] = useState<HistoryEntry[]>([]);

  const qrCodeRef = useRef<HTMLDivElement>(null);
  const qrCodeStylingInstance = useRef(
    new QRCodeStyling({
      width: 300,
      height: 300,
      type: 'svg',
      data: url,
      image: '',
      dotsOptions: {
        color: dotColor,
        type: dotType,
      },
      backgroundOptions: {
        color: backgroundColor,
      },
      imageOptions: {
        crossOrigin: 'anonymous',
        margin: 20,
      },
    })
  );

  const loadHistory = () => {
    try {
      const storedHistory = localStorage.getItem(LOCAL_STORAGE_KEY);
      return storedHistory ? JSON.parse(storedHistory) : [];
    } catch (error) {
      console.error('Failed to load history from local storage:', error);
      return [];
    }
  };

  const saveHistory = (newHistory: HistoryEntry[]) => {
    try {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(newHistory));
    } catch (error) {
      console.error('Failed to save history to local storage:', error);
    }
  };

  useEffect(() => {
    if (qrCodeRef.current) {
      const urlParams = new URLSearchParams(window.location.search);
      const configParam = urlParams.get('config');
      if (configParam) {
        try {
          const decodedConfig = JSON.parse(atob(configParam));
          if (decodedConfig.url) setUrl(decodedConfig.url);
          if (decodedConfig.dotType) setDotType(decodedConfig.dotType);
          if (decodedConfig.dotColor) setDotColor(decodedConfig.dotColor);
          if (decodedConfig.backgroundColor) setBackgroundColor(decodedConfig.backgroundColor);
        } catch (error) {
          console.error('Failed to parse config from URL:', error);
        }
      }
      qrCodeStylingInstance.current.append(qrCodeRef.current);
    }
    setHistory(loadHistory());
  }, []);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const config = { url, dotType, dotColor, backgroundColor };

    const isDefaultConfig =
      url === DEFAULT_URL &&
      dotType === DEFAULT_DOT_TYPE &&
      dotColor === DEFAULT_DOT_COLOR &&
      backgroundColor === DEFAULT_BACKGROUND_COLOR;

    if (isDefaultConfig) {
      urlParams.delete('config');
    } else {
      const encodedConfig = btoa(JSON.stringify(config));
      urlParams.set('config', encodedConfig);
    }

    // These parameters are now handled by the 'config' parameter, so they should always be deleted.
    urlParams.delete('dotType');
    urlParams.delete('dotColor');
    urlParams.delete('backgroundColor');

    window.history.replaceState(
      {},
      '',
      `${window.location.pathname}${isDefaultConfig ? '' : '?'}${urlParams}`
    );

    qrCodeStylingInstance.current.update({
      data: url,
      dotsOptions: {
        type: dotType,
        color: dotColor,
      },
      backgroundOptions: {
        color: backgroundColor,
      },
    });
  }, [url, dotType, dotColor, backgroundColor]);

  const handleGenerateQRCode = async (values: {
    url: string;
    dotType: DotType;
    dotColor: string;
    backgroundColor: string;
  }) => {
    setUrl(values.url);
    setDotType(values.dotType);
    setDotColor(values.dotColor);
    setBackgroundColor(values.backgroundColor);

    const newEntry: HistoryEntry = {
      url: values.url,
      dotType: values.dotType,
      dotColor: values.dotColor,
      backgroundColor: values.backgroundColor,
      timestamp: Date.now(),
    };

    setHistory((prevHistory) => {
      const updatedHistory = [newEntry, ...prevHistory].slice(0, MAX_HISTORY_ENTRIES);
      saveHistory(updatedHistory);
      return updatedHistory;
    });
  };

  const handleLoadHistoryEntry = (entry: HistoryEntry) => {
    setUrl(entry.url);
    setDotType(entry.dotType);
    setDotColor(entry.dotColor);
    setBackgroundColor(entry.backgroundColor);
  };

  const handleDeleteHistoryEntry = (index: number) => {
    const updatedHistory = history.filter((_, i) => i !== index);
    setHistory(updatedHistory);
    saveHistory(updatedHistory);
  };

  return (
    <Flex gap="md" justify="space-between" align="flex-start" wrap="wrap">
      <Stack gap="xl" style={{ flexGrow: 1 }}>
        <Grid
          gutter="xl"
          breakpoints={{ xs: '320px', sm: '640px', md: '768px', lg: '1024px', xl: '1200px' }}
        >
          <Grid.Col
            span={{
              sm: 12,
              md: 6,
              lg: 8,
            }}
            order={{ base: 2, md: 1 }}
          >
            <QRCodeForm
              initialUrl={url}
              initialDotType={dotType}
              initialDotColor={dotColor}
              initialBackgroundColor={backgroundColor}
              onGenerate={handleGenerateQRCode}
              onDotTypeChange={setDotType}
              onDotColorChange={setDotColor}
              onBackgroundColorChange={setBackgroundColor}
            />
          </Grid.Col>
          <Grid.Col
            span={{
              sm: 12,
              md: 6,
              lg: 4,
            }}
            order={{ base: 1, md: 2 }}
          >
            <QRCodeDisplay
              data={url}
              dotType={dotType}
              dotColor={dotColor}
              backgroundColor={backgroundColor}
            />
          </Grid.Col>
        </Grid>
      </Stack>
      <Stack w="100%" gap="md">
        <QRCodeHistory
          history={history}
          onLoadHistoryEntry={handleLoadHistoryEntry}
          onDeleteHistoryEntry={handleDeleteHistoryEntry}
        />
      </Stack>
    </Flex>
  );
}
