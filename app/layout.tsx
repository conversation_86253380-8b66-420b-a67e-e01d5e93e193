import { MantineProvider, createTheme, Container } from '@mantine/core';
import { AppHeader } from '../src/AppHeader';
import '@mantine/core/styles.css';
import '../src/index.css';

const theme = createTheme({
  primaryColor: 'cyan',
  radius: {
    sm: '12px',
    md: '16px',
    lg: '20px',
    xl: '24px',
  },
});

export const metadata = {
  title: 'Free QR Code Generator Online | QRCode Donkey',
  description: 'Generate custom QR codes for free with QRCode Donkey. Create high-quality QR codes for websites, social media, business cards, and more. No sign-up required!',
  icons: {
    icon: '/donkey-128.png',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta name="google-adsense-account" content="ca-pub-****************" />
        <script
          async
          src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
          crossOrigin="anonymous"
        />
        <script
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-RCMH6J3KQ9"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-RCMH6J3KQ9');
            `,
          }}
        />
      </head>
      <body>
        <MantineProvider theme={theme}>
          <AppHeader />
          <Container fluid maw={1200} pt="xl" pb="xl">
            {children}
          </Container>
        </MantineProvider>
      </body>
    </html>
  );
}
