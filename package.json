{"name": "qr-code-donkey", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@mantine/carousel": "^7.16.0", "@mantine/charts": "^7.16.0", "@mantine/code-highlight": "^7.16.0", "@mantine/core": "^7.16.0", "@mantine/dates": "^7.16.0", "@mantine/dropzone": "^7.16.0", "@mantine/form": "^7.16.0", "@mantine/hooks": "^7.16.0", "@mantine/modals": "^7.16.0", "@mantine/notifications": "^7.16.0", "@mantine/nprogress": "^7.16.0", "@mantine/spotlight": "^7.16.0", "@mantine/tiptap": "^7.16.0", "@tabler/icons-react": "^3.34.0", "@tiptap/extension-link": "^2.11.2", "@tiptap/pm": "^2.11.2", "@tiptap/react": "^2.11.2", "@tiptap/starter-kit": "^2.11.2", "dayjs": "^1.11.13", "embla-carousel-react": "^7.1.0", "qr-code-styling": "^1.9.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.7.0", "recharts": "^2.15.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}