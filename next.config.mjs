/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    optimizePackageImports: ['@mantine/core', '@mantine/hooks'],
  },
  transpilePackages: [
    '@mantine/core',
    '@mantine/hooks',
    '@mantine/form',
    '@mantine/dates',
    '@mantine/dropzone',
    '@mantine/modals',
    '@mantine/notifications',
    '@mantine/nprogress',
    '@mantine/spotlight',
    '@mantine/tiptap',
    '@mantine/carousel',
    '@mantine/charts',
    '@mantine/code-highlight',
  ],
  // Enable static exports for better performance
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true,
  },
};

export default nextConfig;
